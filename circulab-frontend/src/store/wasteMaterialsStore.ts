import { create } from 'zustand';
import { WasteMaterial, CreateWasteMaterialData, WasteMaterialQuery, WasteMaterialStatistics } from '@/types';
import { legacyApiClient as apiClient } from '@/lib/api';

interface WasteMaterialsState {
  materials: WasteMaterial[];
  currentMaterial: WasteMaterial | null;
  statistics: WasteMaterialStatistics | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
}

interface WasteMaterialsActions {
  fetchMaterials: (query?: WasteMaterialQuery) => Promise<void>;
  fetchMaterialById: (id: string) => Promise<void>;
  createMaterial: (data: CreateWasteMaterialData) => Promise<WasteMaterial>;
  updateMaterial: (id: string, data: Partial<CreateWasteMaterialData>) => Promise<WasteMaterial>;
  deleteMaterial: (id: string) => Promise<void>;
  fetchStatistics: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  clearCurrentMaterial: () => void;
}

type WasteMaterialsStore = WasteMaterialsState & WasteMaterialsActions;

export const useWasteMaterialsStore = create<WasteMaterialsStore>((set, get) => ({
  // Initial state
  materials: [],
  currentMaterial: null,
  statistics: null,
  isLoading: false,
  error: null,
  pagination: null,

  // Actions
  fetchMaterials: async (query?: WasteMaterialQuery) => {
    try {
      set({ isLoading: true, error: null });

      const params = new URLSearchParams();
      if (query) {
        Object.entries(query).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }

      const response = await apiClient.get(`/waste-materials?${params.toString()}`);

      if (response.success && response.data) {
        set({
          materials: response.data,
          pagination: response.pagination || null,
          isLoading: false,
          error: null,
        });
      } else {
        throw new Error(response.message || 'Failed to fetch waste materials');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch waste materials';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  fetchMaterialById: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.get(`/waste-materials/${id}`);

      if (response.success && response.data) {
        set({
          currentMaterial: response.data,
          isLoading: false,
          error: null,
        });
      } else {
        throw new Error(response.message || 'Failed to fetch waste material');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch waste material';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  createMaterial: async (data: CreateWasteMaterialData) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.post('/waste-materials', data);

      if (response.success && response.data) {
        // Add the new material to the list
        set((state) => ({
          materials: [response.data, ...state.materials],
          isLoading: false,
          error: null,
        }));

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create waste material');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create waste material';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  updateMaterial: async (id: string, data: Partial<CreateWasteMaterialData>) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.put(`/waste-materials/${id}`, data);

      if (response.success && response.data) {
        // Update the material in the list
        set((state) => ({
          materials: state.materials.map((material) =>
            material.id === id ? response.data : material
          ),
          currentMaterial: state.currentMaterial?.id === id ? response.data : state.currentMaterial,
          isLoading: false,
          error: null,
        }));

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update waste material');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update waste material';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  deleteMaterial: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.delete(`/waste-materials/${id}`);

      if (response.success) {
        // Remove the material from the list
        set((state) => ({
          materials: state.materials.filter((material) => material.id !== id),
          currentMaterial: state.currentMaterial?.id === id ? null : state.currentMaterial,
          isLoading: false,
          error: null,
        }));
      } else {
        throw new Error(response.message || 'Failed to delete waste material');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to delete waste material';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  fetchStatistics: async () => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.get('/waste-materials/statistics');

      if (response.success && response.data) {
        set({
          statistics: response.data,
          isLoading: false,
          error: null,
        });
      } else {
        throw new Error(response.message || 'Failed to fetch statistics');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch statistics';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  clearError: () => set({ error: null }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),

  clearCurrentMaterial: () => set({ currentMaterial: null }),
}));

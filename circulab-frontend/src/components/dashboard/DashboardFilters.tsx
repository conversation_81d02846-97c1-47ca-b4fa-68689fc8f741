'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface FilterOption {
  id: string;
  label: string;
  checked: boolean;
}

interface FilterGroup {
  title: string;
  options: FilterOption[];
}

export function DashboardFilters() {
  const [filters, setFilters] = useState<FilterGroup[]>([
    {
      title: 'Organization',
      options: [
        { id: 'org1', label: 'CircuLab HQ', checked: false },
        { id: 'org2', label: 'Regional Office', checked: false },
        { id: 'org3', label: 'Processing Center', checked: false },
      ]
    },
    {
      title: 'Hub',
      options: [
        { id: 'hub1', label: 'North Hub', checked: false },
        { id: 'hub2', label: 'South Hub', checked: false },
        { id: 'hub3', label: 'Central Hub', checked: false },
      ]
    },
    {
      title: 'Facility',
      options: [
        { id: 'fac1', label: 'Processing Plant A', checked: false },
        { id: 'fac2', label: 'Processing Plant B', checked: false },
        { id: 'fac3', label: 'Sorting Facility', checked: false },
      ]
    },
    {
      title: 'Site',
      options: [
        { id: 'site1', label: 'Collection Point 1', checked: false },
        { id: 'site2', label: 'Collection Point 2', checked: false },
        { id: 'site3', label: 'Storage Site', checked: false },
      ]
    },
    {
      title: 'Activity',
      options: [
        { id: 'act1', label: 'Collection', checked: false },
        { id: 'act2', label: 'Processing', checked: false },
        { id: 'act3', label: 'Recycling', checked: false },
        { id: 'act4', label: 'Disposal', checked: false },
      ]
    },
    {
      title: 'Scope',
      options: [
        { id: 'scope1', label: 'Organic Waste', checked: false },
        { id: 'scope2', label: 'Plastic Waste', checked: false },
        { id: 'scope3', label: 'Metal Waste', checked: false },
      ]
    },
    {
      title: 'Location',
      options: [
        { id: 'loc1', label: 'Urban Areas', checked: false },
        { id: 'loc2', label: 'Industrial Zones', checked: false },
        { id: 'loc3', label: 'Rural Areas', checked: false },
      ]
    },
    {
      title: 'Tags',
      options: [
        { id: 'tag1', label: 'High Priority', checked: false },
        { id: 'tag2', label: 'Recyclable', checked: false },
        { id: 'tag3', label: 'Hazardous', checked: false },
      ]
    },
    {
      title: 'Time period',
      options: [
        { id: 'time1', label: 'Last 7 days', checked: false },
        { id: 'time2', label: 'Last 30 days', checked: true },
        { id: 'time3', label: 'Last 90 days', checked: false },
        { id: 'time4', label: 'Last year', checked: false },
      ]
    }
  ]);

  const handleFilterChange = (groupIndex: number, optionIndex: number) => {
    const newFilters = [...filters];
    newFilters[groupIndex].options[optionIndex].checked = 
      !newFilters[groupIndex].options[optionIndex].checked;
    setFilters(newFilters);
  };

  const applyFilters = () => {
    // Implementation for applying filters
    console.log('Applying filters:', filters);
  };

  return (
    <div className="w-80 bg-gray-800 text-white h-full overflow-y-auto">
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-6">Filter options</h2>
        
        <div className="space-y-6">
          {filters.map((group, groupIndex) => (
            <div key={group.title}>
              <h3 className="text-sm font-medium text-gray-300 mb-3">{group.title}</h3>
              <div className="space-y-2">
                {group.options.map((option, optionIndex) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={option.id}
                      checked={option.checked}
                      onCheckedChange={() => handleFilterChange(groupIndex, optionIndex)}
                      className="border-gray-500 data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500"
                    />
                    <label
                      htmlFor={option.id}
                      className="text-sm text-gray-300 cursor-pointer hover:text-white transition-colors"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <Button 
          onClick={applyFilters}
          className="w-full mt-8 bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded"
        >
          APPLY FILTER
        </Button>
      </div>
    </div>
  );
}

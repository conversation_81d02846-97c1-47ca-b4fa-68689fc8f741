'use client';

interface ProcessingChartProps {
  data?: Array<{ month: string; processed: number; collected: number }>;
}

export function ProcessingChart({ data }: ProcessingChartProps) {
  // Default data if none provided
  const defaultData = [
    { month: 'Jan', processed: 85, collected: 100 },
    { month: 'Feb', processed: 92, collected: 110 },
    { month: 'Mar', processed: 78, collected: 95 },
    { month: 'Apr', processed: 88, collected: 105 },
    { month: 'May', processed: 95, collected: 115 },
    { month: 'Jun', processed: 82, collected: 98 },
  ];

  const chartData = data || defaultData;
  const maxValue = Math.max(...chartData.flatMap(d => [d.processed, d.collected]));

  return (
    <div className="space-y-4">
      {/* Chart */}
      <div className="relative h-48">
        <svg width="100%" height="100%" viewBox="0 0 400 200" className="overflow-visible">
          {/* Grid lines */}
          {[0, 25, 50, 75, 100].map((value) => (
            <g key={value}>
              <line
                x1="40"
                y1={160 - (value / maxValue) * 120}
                x2="360"
                y2={160 - (value / maxValue) * 120}
                stroke="#E5E7EB"
                strokeWidth="1"
              />
              <text
                x="35"
                y={165 - (value / maxValue) * 120}
                textAnchor="end"
                className="text-xs fill-gray-500"
              >
                {value}
              </text>
            </g>
          ))}

          {/* Bars */}
          {chartData.map((item, index) => {
            const barWidth = 20;
            const groupWidth = 50;
            const x = 60 + index * groupWidth;
            
            const processedHeight = (item.processed / maxValue) * 120;
            const collectedHeight = (item.collected / maxValue) * 120;

            return (
              <g key={item.month}>
                {/* Collected bar (background) */}
                <rect
                  x={x}
                  y={160 - collectedHeight}
                  width={barWidth}
                  height={collectedHeight}
                  fill="#E5E7EB"
                  rx="2"
                />
                
                {/* Processed bar (foreground) */}
                <rect
                  x={x}
                  y={160 - processedHeight}
                  width={barWidth}
                  height={processedHeight}
                  fill="#E91E63"
                  rx="2"
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                />

                {/* Month label */}
                <text
                  x={x + barWidth / 2}
                  y="180"
                  textAnchor="middle"
                  className="text-xs fill-gray-600"
                >
                  {item.month}
                </text>

                {/* Value labels */}
                <text
                  x={x + barWidth / 2}
                  y={155 - processedHeight}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 font-medium"
                >
                  {item.processed}
                </text>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-pink-500 rounded"></div>
          <span className="text-sm text-gray-600">Processed</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-300 rounded"></div>
          <span className="text-sm text-gray-600">Collected</span>
        </div>
      </div>

      {/* Summary stats */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">
            {chartData.reduce((sum, item) => sum + item.processed, 0)}T
          </div>
          <div className="text-xs text-gray-500">Total Processed</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">
            {Math.round((chartData.reduce((sum, item) => sum + item.processed, 0) / 
                        chartData.reduce((sum, item) => sum + item.collected, 0)) * 100)}%
          </div>
          <div className="text-xs text-gray-500">Efficiency Rate</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">
            {chartData.length}
          </div>
          <div className="text-xs text-gray-500">Months Tracked</div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';

interface WasteGeneratedMapProps {
  viewMode: string;
}

interface WasteLocation {
  id: string;
  name: string;
  x: number;
  y: number;
  value: number;
  type: 'collection' | 'processing' | 'disposal';
}

export function WasteGeneratedMap({ viewMode }: WasteGeneratedMapProps) {
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

  // Mock data for waste locations
  const wasteLocations: WasteLocation[] = [
    { id: '1', name: 'Collection Point A', x: 20, y: 30, value: 45, type: 'collection' },
    { id: '2', name: 'Processing Plant B', x: 60, y: 25, value: 78, type: 'processing' },
    { id: '3', name: 'Collection Point C', x: 35, y: 60, value: 32, type: 'collection' },
    { id: '4', name: 'Disposal Site D', x: 80, y: 70, value: 25, type: 'disposal' },
    { id: '5', name: 'Processing Plant E', x: 15, y: 75, value: 56, type: 'processing' },
    { id: '6', name: 'Collection Point F', x: 70, y: 40, value: 38, type: 'collection' },
  ];

  const getLocationColor = (type: string) => {
    switch (type) {
      case 'collection': return '#22C55E'; // Green
      case 'processing': return '#3B82F6'; // Blue
      case 'disposal': return '#EF4444'; // Red
      default: return '#6B7280'; // Gray
    }
  };

  const getLocationSize = (value: number) => {
    return Math.max(8, Math.min(20, value / 4));
  };

  const renderMapView = () => (
    <div className="relative w-full h-96 bg-gray-100 rounded-lg overflow-hidden">
      {/* Simple map background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50">
        {/* Grid lines for map effect */}
        <svg className="w-full h-full opacity-20">
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94A3B8" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Waste locations */}
      {wasteLocations.map((location) => (
        <div
          key={location.id}
          className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110"
          style={{
            left: `${location.x}%`,
            top: `${location.y}%`,
          }}
          onClick={() => setSelectedLocation(location.id)}
        >
          <div
            className="rounded-full shadow-lg border-2 border-white flex items-center justify-center"
            style={{
              backgroundColor: getLocationColor(location.type),
              width: `${getLocationSize(location.value)}px`,
              height: `${getLocationSize(location.value)}px`,
            }}
          >
            <span className="text-white text-xs font-bold">
              {location.value}
            </span>
          </div>
          
          {selectedLocation === location.id && (
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg shadow-lg p-2 min-w-max z-10">
              <p className="text-sm font-medium">{location.name}</p>
              <p className="text-xs text-gray-500">{location.value}T waste</p>
              <p className="text-xs text-gray-500 capitalize">{location.type}</p>
            </div>
          )}
        </div>
      ))}

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <h4 className="text-sm font-medium mb-2">Legend</h4>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs">Collection</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs">Processing</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-xs">Disposal</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTableView = () => (
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-2 px-3 font-medium text-gray-600">Location</th>
            <th className="text-left py-2 px-3 font-medium text-gray-600">Type</th>
            <th className="text-right py-2 px-3 font-medium text-gray-600">Waste (T)</th>
          </tr>
        </thead>
        <tbody>
          {wasteLocations.map((location) => (
            <tr key={location.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-2 px-3">{location.name}</td>
              <td className="py-2 px-3">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize ${
                  location.type === 'collection' ? 'bg-green-100 text-green-800' :
                  location.type === 'processing' ? 'bg-blue-100 text-blue-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {location.type}
                </span>
              </td>
              <td className="py-2 px-3 text-right font-medium">{location.value}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const renderChartView = () => (
    <div className="space-y-4">
      {wasteLocations.map((location) => (
        <div key={location.id} className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">{location.name}</span>
            <span className="text-sm font-bold text-gray-900">{location.value}T</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="h-2 rounded-full transition-all duration-500 ease-out"
              style={{
                width: `${(location.value / Math.max(...wasteLocations.map(l => l.value))) * 100}%`,
                backgroundColor: getLocationColor(location.type)
              }}
            />
          </div>
        </div>
      ))}
    </div>
  );

  const renderTargetsView = () => (
    <div className="grid grid-cols-2 gap-4">
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">85%</div>
        <div className="text-sm text-gray-500">Collection Target</div>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
        </div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">92%</div>
        <div className="text-sm text-gray-500">Processing Target</div>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div className="bg-blue-500 h-2 rounded-full" style={{ width: '92%' }}></div>
        </div>
      </div>
    </div>
  );

  const renderYearlyView = () => (
    <div className="text-center">
      <div className="text-3xl font-bold text-gray-900 mb-2">2,847T</div>
      <div className="text-sm text-gray-500 mb-4">Total waste processed this year</div>
      <div className="grid grid-cols-4 gap-4 text-sm">
        <div>
          <div className="font-medium">Q1</div>
          <div className="text-gray-500">642T</div>
        </div>
        <div>
          <div className="font-medium">Q2</div>
          <div className="text-gray-500">718T</div>
        </div>
        <div>
          <div className="font-medium">Q3</div>
          <div className="text-gray-500">756T</div>
        </div>
        <div>
          <div className="font-medium">Q4</div>
          <div className="text-gray-500">731T</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full">
      {viewMode === 'map' && renderMapView()}
      {viewMode === 'table' && renderTableView()}
      {viewMode === 'chart' && renderChartView()}
      {viewMode === 'targets' && renderTargetsView()}
      {viewMode === 'yearly' && renderYearlyView()}
    </div>
  );
}

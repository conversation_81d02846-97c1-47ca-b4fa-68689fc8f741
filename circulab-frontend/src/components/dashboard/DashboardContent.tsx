'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { apiClient } from '@/lib/api';
import { WasteMaterialStatistics } from '@/types';
import { 
  TrendingUp, 
  Recycle, 
  BarChart3, 
  Package, 
  Plus,
  ArrowUpRight,
  Loader2
} from 'lucide-react';

export function DashboardContent() {
  const [statistics, setStatistics] = useState<WasteMaterialStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get<WasteMaterialStatistics>('/waste-materials/statistics');
      
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        throw new Error('Failed to fetch statistics');
      }
    } catch (error: any) {
      console.error('Error fetching statistics:', error);
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <Button onClick={fetchStatistics} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const overview = statistics?.overview || {
    totalMaterials: 0,
    availableMaterials: 0,
    processingMaterials: 0,
    valorizedMaterials: 0,
    totalQuantity: 0,
  };

  const valorisationRate = overview.totalMaterials > 0 
    ? Math.round((overview.valorizedMaterials / overview.totalMaterials) * 100)
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Vue d'ensemble de la valorisation des déchets industriels
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Nouveau déchet
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Production Totale</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalQuantity}T</div>
            <p className="text-xs text-muted-foreground">
              {overview.totalMaterials} matériaux
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valorisé</CardTitle>
            <Recycle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics?.materialsByStatus?.find(s => s.currentStatus === 'VALORIZED')?._sum?.quantity || 0}T
            </div>
            <p className="text-xs text-muted-foreground">
              {overview.valorizedMaterials} matériaux
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de Valorisation</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{valorisationRate}%</div>
            <p className="text-xs text-muted-foreground">
              +2.1% ce mois
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Traitement</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {statistics?.materialsByStatus?.find(s => s.currentStatus === 'PROCESSING')?._sum?.quantity || 0}T
            </div>
            <p className="text-xs text-muted-foreground">
              {overview.processingMaterials} matériaux
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Details */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Types de Déchets</CardTitle>
            <CardDescription>
              Répartition par type de matériau
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statistics?.materialsByType?.map((material, index) => (
                <div key={material.type} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-blue-500' : 
                      index === 1 ? 'bg-green-500' : 
                      'bg-orange-500'
                    }`} />
                    <span className="text-sm font-medium">{material.type}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{material._sum?.quantity || 0}T</div>
                    <div className="text-xs text-muted-foreground">
                      {material._count?.type || 0} matériaux
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Actions Rapides</CardTitle>
            <CardDescription>
              Accès direct aux fonctionnalités
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start gap-2">
              <Package className="h-4 w-4" />
              Gérer les déchets
              <ArrowUpRight className="h-4 w-4 ml-auto" />
            </Button>
            <Button variant="outline" className="w-full justify-start gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
              <ArrowUpRight className="h-4 w-4 ml-auto" />
            </Button>
            <Button variant="outline" className="w-full justify-start gap-2">
              <Recycle className="h-4 w-4" />
              Traitements
              <ArrowUpRight className="h-4 w-4 ml-auto" />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle>État des Matériaux</CardTitle>
          <CardDescription>
            Répartition par statut de traitement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            {statistics?.materialsByStatus?.map((status) => (
              <div key={status.currentStatus} className="flex items-center space-x-2">
                <Badge variant={
                  status.currentStatus === 'AVAILABLE' ? 'default' :
                  status.currentStatus === 'PROCESSING' ? 'secondary' :
                  status.currentStatus === 'VALORIZED' ? 'outline' :
                  'destructive'
                }>
                  {status.currentStatus === 'AVAILABLE' ? 'Disponible' :
                   status.currentStatus === 'PROCESSING' ? 'En traitement' :
                   status.currentStatus === 'VALORIZED' ? 'Valorisé' :
                   status.currentStatus}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {status._sum?.quantity || 0}T ({status._count?.currentStatus || 0})
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default DashboardContent;

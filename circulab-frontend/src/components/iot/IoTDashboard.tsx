"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wifi, 
  WifiOff, 
  Battery, 
  BatteryLow,
  AlertTriangle, 
  CheckCircle,
  Activity,
  Thermometer,
  Droplets,
  BarChart3,
  Weight,
  Zap,
  Settings,
  MapPin,
  Clock,
  TrendingUp,
  Signal
} from 'lucide-react';
import { useIoTSimulator, SensorConfig, SensorReading, Alert, IoTDashboardData } from '@/lib/iot/sensorSimulator';

interface IoTDashboardProps {
  autoStart?: boolean;
  refreshInterval?: number;
}

export function IoTDashboard({ autoStart = true, refreshInterval = 5000 }: IoTDashboardProps) {
  const {
    startSimulation,
    stopSimulation,
    getSensors,
    getReadings,
    getAlerts,
    acknowledgeAlert,
    getDashboardData,
    performMaintenance
  } = useIoTSimulator();

  const [isRunning, setIsRunning] = useState(false);
  const [sensors, setSensors] = useState<SensorConfig[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [dashboardData, setDashboardData] = useState<IoTDashboardData | null>(null);
  const [selectedSensor, setSelectedSensor] = useState<string | null>(null);
  const [sensorReadings, setSensorReadings] = useState<SensorReading[]>([]);

  // Démarrage automatique
  useEffect(() => {
    if (autoStart) {
      handleStartSimulation();
    }
    return () => {
      if (isRunning) {
        stopSimulation();
      }
    };
  }, [autoStart]);

  // Rafraîchissement automatique
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [isRunning, refreshInterval]);

  const handleStartSimulation = () => {
    startSimulation();
    setIsRunning(true);
    refreshData();
  };

  const handleStopSimulation = () => {
    stopSimulation();
    setIsRunning(false);
  };

  const refreshData = () => {
    setSensors(getSensors());
    setAlerts(getAlerts(20));
    setDashboardData(getDashboardData());
    
    if (selectedSensor) {
      setSensorReadings(getReadings(selectedSensor, 50));
    }
  };

  const handleSensorSelect = (sensorId: string) => {
    setSelectedSensor(sensorId);
    setSensorReadings(getReadings(sensorId, 50));
  };

  const handleAcknowledgeAlert = (alertId: string) => {
    acknowledgeAlert(alertId);
    refreshData();
  };

  const handleMaintenance = (sensorId: string) => {
    performMaintenance(sensorId);
    refreshData();
  };

  const getSensorIcon = (type: SensorConfig['type']) => {
    const iconProps = { className: "h-4 w-4" };
    switch (type) {
      case 'temperature': return <Thermometer {...iconProps} />;
      case 'humidity': return <Droplets {...iconProps} />;
      case 'level': return <BarChart3 {...iconProps} />;
      case 'weight': return <Weight {...iconProps} />;
      case 'gas': return <Zap {...iconProps} />;
      case 'ph': return <Activity {...iconProps} />;
      default: return <Activity {...iconProps} />;
    }
  };

  const getSeverityColor = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (sensor: SensorConfig) => {
    if (!sensor.isActive) return 'text-gray-500';
    if (sensor.batteryLevel < 20) return 'text-red-500';
    if (sensor.signalStrength < 50) return 'text-orange-500';
    return 'text-green-500';
  };

  return (
    <div className="space-y-6">
      {/* Header avec contrôles */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Wifi className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-blue-800">
                  Dashboard IoT Temps Réel
                </CardTitle>
                <CardDescription className="text-blue-600">
                  Monitoring intelligent des capteurs de déchets
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={isRunning ? "default" : "secondary"}>
                {isRunning ? "En cours" : "Arrêté"}
              </Badge>
              {isRunning ? (
                <Button onClick={handleStopSimulation} variant="outline" size="sm">
                  <WifiOff className="h-4 w-4 mr-2" />
                  Arrêter
                </Button>
              ) : (
                <Button onClick={handleStartSimulation} size="sm">
                  <Wifi className="h-4 w-4 mr-2" />
                  Démarrer
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Métriques globales */}
      {dashboardData && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Wifi className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.activeSensors}</div>
                  <div className="text-xs text-gray-600">Capteurs actifs</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.alertsCount}</div>
                  <div className="text-xs text-gray-600">Alertes</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Battery className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.averageBatteryLevel}%</div>
                  <div className="text-xs text-gray-600">Batterie moy.</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.dataPointsToday}</div>
                  <div className="text-xs text-gray-600">Données/jour</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.systemHealth}%</div>
                  <div className="text-xs text-gray-600">Santé système</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{dashboardData.totalSensors}</div>
                  <div className="text-xs text-gray-600">Total capteurs</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="sensors" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sensors">Capteurs</TabsTrigger>
          <TabsTrigger value="alerts">Alertes</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="sensors" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sensors.map(sensor => {
              const latestReading = getReadings(sensor.id, 1)[0];
              return (
                <Card 
                  key={sensor.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedSensor === sensor.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => handleSensorSelect(sensor.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getSensorIcon(sensor.type)}
                        <CardTitle className="text-sm">{sensor.name}</CardTitle>
                      </div>
                      <div className={`flex items-center space-x-1 ${getStatusColor(sensor)}`}>
                        {sensor.isActive ? (
                          <Wifi className="h-3 w-3" />
                        ) : (
                          <WifiOff className="h-3 w-3" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {/* Valeur actuelle */}
                    {latestReading && (
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold">
                          {latestReading.value} {latestReading.unit}
                        </div>
                        <div className="text-xs text-gray-600">
                          {new Date(latestReading.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    )}

                    {/* Informations du capteur */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center space-x-1">
                          <Battery className="h-3 w-3" />
                          <span>Batterie</span>
                        </span>
                        <span className={sensor.batteryLevel < 20 ? 'text-red-500' : 'text-green-500'}>
                          {sensor.batteryLevel}%
                        </span>
                      </div>
                      <Progress value={sensor.batteryLevel} className="h-2" />

                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center space-x-1">
                          <Signal className="h-3 w-3" />
                          <span>Signal</span>
                        </span>
                        <span>{sensor.signalStrength}%</span>
                      </div>
                      <Progress value={sensor.signalStrength} className="h-2" />

                      <div className="flex items-center justify-between text-xs text-gray-600">
                        <span className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{sensor.location}</span>
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-600">
                        <span className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>Maintenance</span>
                        </span>
                        <span>
                          {Math.floor((Date.now() - sensor.lastMaintenance.getTime()) / (1000 * 60 * 60 * 24))}j
                        </span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMaintenance(sensor.id);
                        }}
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        Maintenance
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="space-y-3">
            {alerts.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                  <div className="text-lg font-medium text-green-700">Aucune alerte</div>
                  <div className="text-sm text-gray-600">Tous les systèmes fonctionnent normalement</div>
                </CardContent>
              </Card>
            ) : (
              alerts.map(alert => (
                <Card key={alert.id} className={`border-l-4 ${getSeverityColor(alert.severity)}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className={`h-5 w-5 ${
                          alert.severity === 'critical' ? 'text-red-500' :
                          alert.severity === 'high' ? 'text-orange-500' :
                          alert.severity === 'medium' ? 'text-yellow-500' :
                          'text-blue-500'
                        }`} />
                        <div>
                          <div className="font-medium">{alert.message}</div>
                          <div className="text-sm text-gray-600">
                            {new Date(alert.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={alert.acknowledged ? "secondary" : "destructive"}>
                          {alert.severity}
                        </Badge>
                        {!alert.acknowledged && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleAcknowledgeAlert(alert.id)}
                          >
                            Acquitter
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          {selectedSensor ? (
            <Card>
              <CardHeader>
                <CardTitle>
                  Monitoring - {sensors.find(s => s.id === selectedSensor)?.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Graphique simple des dernières valeurs */}
                  <div className="h-64 bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 mb-2">
                      Dernières lectures ({sensorReadings.length} points)
                    </div>
                    <div className="grid grid-cols-10 gap-1 h-48">
                      {sensorReadings.slice(-10).map((reading, index) => {
                        const sensor = sensors.find(s => s.id === selectedSensor);
                        const maxValue = sensor?.maxValue || 100;
                        const height = (reading.value / maxValue) * 100;
                        return (
                          <div key={index} className="flex flex-col justify-end">
                            <div 
                              className="bg-blue-500 rounded-t"
                              style={{ height: `${height}%` }}
                              title={`${reading.value} ${reading.unit} - ${new Date(reading.timestamp).toLocaleTimeString()}`}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Statistiques */}
                  {sensorReadings.length > 0 && (
                    <div className="grid grid-cols-4 gap-4">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="text-sm text-blue-600">Dernière valeur</div>
                        <div className="font-bold">
                          {sensorReadings[sensorReadings.length - 1]?.value} {sensorReadings[sensorReadings.length - 1]?.unit}
                        </div>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div className="text-sm text-green-600">Moyenne</div>
                        <div className="font-bold">
                          {(sensorReadings.reduce((sum, r) => sum + r.value, 0) / sensorReadings.length).toFixed(2)}
                        </div>
                      </div>
                      <div className="p-3 bg-orange-50 rounded-lg">
                        <div className="text-sm text-orange-600">Maximum</div>
                        <div className="font-bold">
                          {Math.max(...sensorReadings.map(r => r.value)).toFixed(2)}
                        </div>
                      </div>
                      <div className="p-3 bg-purple-50 rounded-lg">
                        <div className="text-sm text-purple-600">Minimum</div>
                        <div className="font-bold">
                          {Math.min(...sensorReadings.map(r => r.value)).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <div className="text-lg font-medium text-gray-700">Sélectionnez un capteur</div>
                <div className="text-sm text-gray-600">Cliquez sur un capteur pour voir son monitoring détaillé</div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default IoTDashboard;

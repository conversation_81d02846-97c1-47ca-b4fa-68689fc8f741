"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Link2, 
  Shield, 
  Award, 
  Leaf, 
  FileText,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Zap,
  Hash,
  Users,
  Package,
  Recycle
} from 'lucide-react';
import { useTraceabilityBlockchain, WasteTransaction, SmartContract, TraceabilityReport } from '@/lib/blockchain/traceabilityChain';

interface TraceabilityDashboardProps {
  wasteId?: string;
}

export function TraceabilityDashboard({ wasteId: initialWasteId }: TraceabilityDashboardProps) {
  const {
    createTransaction,
    mineBlock,
    createSmartContract,
    generateReport,
    getChainLength,
    getPendingTransactionsCount,
    getSmartContracts,
    isChainValid
  } = useTraceabilityBlockchain();

  const [wasteId, setWasteId] = useState(initialWasteId || '');
  const [report, setReport] = useState<TraceabilityReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [chainStats, setChainStats] = useState({
    blocks: 0,
    pendingTx: 0,
    contracts: 0,
    isValid: true
  });

  // Formulaire pour nouvelle transaction
  const [newTransaction, setNewTransaction] = useState({
    type: 'collection' as WasteTransaction['type'],
    location: {
      name: '',
      coordinates: { lat: 48.8566, lng: 2.3522 },
      address: ''
    },
    actor: {
      id: '',
      name: '',
      type: 'collector' as WasteTransaction['actor']['type'],
      certification: ''
    },
    data: {
      quantity: 0,
      quality: 0.8,
      treatment: '',
      destination: ''
    }
  });

  useEffect(() => {
    updateChainStats();
  }, []);

  const updateChainStats = () => {
    setChainStats({
      blocks: getChainLength(),
      pendingTx: getPendingTransactionsCount(),
      contracts: getSmartContracts().length,
      isValid: isChainValid()
    });
  };

  const handleGenerateReport = async () => {
    if (!wasteId) return;
    
    setIsLoading(true);
    try {
      const traceabilityReport = generateReport(wasteId);
      setReport(traceabilityReport);
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTransaction = () => {
    if (!wasteId) return;

    const transaction = createTransaction({
      wasteId,
      type: newTransaction.type,
      timestamp: new Date(),
      location: newTransaction.location,
      actor: newTransaction.actor,
      data: newTransaction.data,
      metadata: {
        temperature: 20 + Math.random() * 10,
        humidity: 50 + Math.random() * 20,
        photos: [`photo_${Date.now()}.jpg`],
        documents: [`doc_${Date.now()}.pdf`],
        signatures: [`signature_${newTransaction.actor.id}`]
      }
    });

    console.log('Transaction créée:', transaction);
    updateChainStats();
    
    // Rafraîchir le rapport si disponible
    if (report) {
      handleGenerateReport();
    }
  };

  const handleMineBlock = () => {
    const newBlock = mineBlock();
    if (newBlock) {
      console.log('Nouveau bloc miné:', newBlock);
      updateChainStats();
      
      // Rafraîchir le rapport si disponible
      if (report) {
        handleGenerateReport();
      }
    }
  };

  const handleCreateSmartContract = (type: SmartContract['type']) => {
    if (!wasteId) return;

    const contractConditions = {
      recycling_certificate: { minQuality: 0.8 },
      carbon_credit: { minCarbonSavings: 100 },
      quality_guarantee: { guaranteedQuality: 0.9, coverageAmount: 1000 },
      payment_escrow: { amount: 500 }
    };

    const contract = createSmartContract({
      name: `${type.replace('_', ' ').toUpperCase()} - ${wasteId}`,
      type,
      wasteId,
      conditions: contractConditions[type]
    });

    console.log('Smart contract créé:', contract);
    updateChainStats();
  };

  const getTransactionIcon = (type: WasteTransaction['type']) => {
    const iconProps = { className: "h-4 w-4" };
    switch (type) {
      case 'collection': return <Package {...iconProps} />;
      case 'transport': return <MapPin {...iconProps} />;
      case 'processing': return <Zap {...iconProps} />;
      case 'recycling': return <Recycle {...iconProps} />;
      case 'disposal': return <AlertCircle {...iconProps} />;
      case 'certification': return <Award {...iconProps} />;
      default: return <FileText {...iconProps} />;
    }
  };

  const getSustainabilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <div className="space-y-6">
      {/* Header Blockchain */}
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Link2 className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-purple-800">
                  Traçabilité Blockchain
                </CardTitle>
                <CardDescription className="text-purple-600">
                  Système de traçabilité décentralisé et immutable
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={chainStats.isValid ? "default" : "destructive"}>
                {chainStats.isValid ? "Chaîne valide" : "Chaîne corrompue"}
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistiques de la chaîne */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{chainStats.blocks}</div>
                <div className="text-xs text-gray-600">Blocs</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{chainStats.pendingTx}</div>
                <div className="text-xs text-gray-600">TX en attente</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{chainStats.contracts}</div>
                <div className="text-xs text-gray-600">Smart Contracts</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">
                  {chainStats.isValid ? "100%" : "0%"}
                </div>
                <div className="text-xs text-gray-600">Intégrité</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="trace" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trace">Traçabilité</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="contracts">Smart Contracts</TabsTrigger>
          <TabsTrigger value="mining">Mining</TabsTrigger>
        </TabsList>

        <TabsContent value="trace" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recherche de Traçabilité</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Label htmlFor="waste-id">ID du déchet</Label>
                  <Input
                    id="waste-id"
                    value={wasteId}
                    onChange={(e) => setWasteId(e.target.value)}
                    placeholder="waste_001"
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={handleGenerateReport} disabled={isLoading || !wasteId}>
                    {isLoading ? "Génération..." : "Générer le rapport"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {report && (
            <div className="space-y-4">
              {/* Résumé du rapport */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Rapport de Traçabilité - {report.wasteId}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-sm text-blue-600">Transactions</div>
                      <div className="text-2xl font-bold text-blue-800">{report.totalTransactions}</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-sm text-green-600">Taux de recyclage</div>
                      <div className="text-2xl font-bold text-green-800">{report.recyclingRate.toFixed(1)}%</div>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <div className="text-sm text-purple-600">Score de vérification</div>
                      <div className="text-2xl font-bold text-purple-800">{report.verificationScore.toFixed(1)}%</div>
                    </div>
                    <div className={`p-3 rounded-lg ${getSustainabilityColor(report.sustainability.score)}`}>
                      <div className="text-sm">Durabilité</div>
                      <div className="text-2xl font-bold">{report.sustainability.score}%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Parcours du déchet */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>Parcours du Déchet</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {report.journey.map((transaction, index) => (
                      <div key={transaction.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            {getTransactionIcon(transaction.type)}
                          </div>
                          <div className="text-sm font-medium">{index + 1}</div>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">{transaction.type.toUpperCase()}</div>
                          <div className="text-sm text-gray-600">{transaction.location.name}</div>
                          <div className="text-sm text-gray-600">
                            {transaction.actor.name} - {transaction.data.quantity}kg
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">{new Date(transaction.timestamp).toLocaleDateString()}</div>
                          <div className="flex items-center space-x-1">
                            {transaction.verified ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-xs">
                              {transaction.verified ? "Vérifié" : "Non vérifié"}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Certifications */}
              {report.certifications.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="h-5 w-5" />
                      <span>Certifications</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {report.certifications.map((cert, index) => (
                        <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Impact environnemental */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Leaf className="h-5 w-5" />
                    <span>Impact Environnemental</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="text-sm text-green-600">Empreinte Carbone</div>
                      <div className="text-xl font-bold text-green-800">
                        {report.carbonFootprint.toFixed(2)} kg CO₂
                      </div>
                      <div className="text-xs text-green-600">
                        {report.carbonFootprint < 0 ? "Économies réalisées" : "Émissions générées"}
                      </div>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="text-sm text-blue-600">Facteurs de Durabilité</div>
                      <div className="space-y-1">
                        {Object.entries(report.sustainability.factors).map(([factor, score]) => (
                          <div key={factor} className="flex justify-between text-xs">
                            <span className="capitalize">{factor.replace(/([A-Z])/g, ' $1')}</span>
                            <span>{score.toFixed(0)}%</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Créer une Transaction</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Type de transaction</Label>
                  <Select 
                    value={newTransaction.type} 
                    onValueChange={(value: WasteTransaction['type']) => 
                      setNewTransaction(prev => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="collection">Collecte</SelectItem>
                      <SelectItem value="transport">Transport</SelectItem>
                      <SelectItem value="processing">Traitement</SelectItem>
                      <SelectItem value="recycling">Recyclage</SelectItem>
                      <SelectItem value="disposal">Élimination</SelectItem>
                      <SelectItem value="certification">Certification</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Quantité (kg)</Label>
                  <Input
                    type="number"
                    value={newTransaction.data.quantity}
                    onChange={(e) => setNewTransaction(prev => ({
                      ...prev,
                      data: { ...prev.data, quantity: Number(e.target.value) }
                    }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Nom de l'acteur</Label>
                  <Input
                    value={newTransaction.actor.name}
                    onChange={(e) => setNewTransaction(prev => ({
                      ...prev,
                      actor: { ...prev.actor, name: e.target.value, id: `actor_${Date.now()}` }
                    }))}
                    placeholder="Nom de l'entreprise"
                  />
                </div>
                <div>
                  <Label>Localisation</Label>
                  <Input
                    value={newTransaction.location.name}
                    onChange={(e) => setNewTransaction(prev => ({
                      ...prev,
                      location: { ...prev.location, name: e.target.value, address: e.target.value }
                    }))}
                    placeholder="Nom du site"
                  />
                </div>
              </div>

              <Button onClick={handleCreateTransaction} disabled={!wasteId} className="w-full">
                Créer la Transaction
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Smart Contracts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  onClick={() => handleCreateSmartContract('recycling_certificate')}
                  disabled={!wasteId}
                  variant="outline"
                >
                  <Award className="h-4 w-4 mr-2" />
                  Certificat de Recyclage
                </Button>
                <Button 
                  onClick={() => handleCreateSmartContract('carbon_credit')}
                  disabled={!wasteId}
                  variant="outline"
                >
                  <Leaf className="h-4 w-4 mr-2" />
                  Crédit Carbone
                </Button>
                <Button 
                  onClick={() => handleCreateSmartContract('quality_guarantee')}
                  disabled={!wasteId}
                  variant="outline"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Garantie Qualité
                </Button>
                <Button 
                  onClick={() => handleCreateSmartContract('payment_escrow')}
                  disabled={!wasteId}
                  variant="outline"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Paiement Sécurisé
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mining" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Mining de Blocs</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-lg font-medium mb-2">
                  {chainStats.pendingTx} transaction(s) en attente
                </div>
                <Button 
                  onClick={handleMineBlock} 
                  disabled={chainStats.pendingTx === 0}
                  className="bg-gradient-to-r from-purple-600 to-blue-600"
                >
                  <Hash className="h-4 w-4 mr-2" />
                  Miner un Bloc
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default TraceabilityDashboard;

"use client";

import React, { Suspense, useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';
import { OrbitControls, Text, Box, Sphere, Cylinder, Environment, Grid, Html } from '@react-three/drei';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Maximize, 
  Settings, 
  Eye,
  Layers,
  Zap,
  Thermometer,
  Gauge,
  AlertTriangle,
  CheckCircle,
  Camera,
  Scan
} from 'lucide-react';
import { useSite3DVisualization, Site3DData, DigitalTwinConfig, ARScanResult } from '@/lib/3d/siteVisualization';
import * as THREE from 'three';

interface Site3DViewerProps {
  siteId?: string;
  enableAR?: boolean;
  enableDigitalTwin?: boolean;
}

// Composant 3D pour un site
function Site3DModel({ site, config }: { site: Site3DData; config: DigitalTwinConfig }) {
  const meshRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState<string | null>(null);
  const [selected, setSelected] = useState<string | null>(null);

  useFrame((state) => {
    if (meshRef.current && config.enablePhysics) {
      // Animation légère du site
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.02;
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return '#10B981';
      case 'maintenance': return '#F59E0B';
      case 'full': return '#EF4444';
      case 'empty': return '#6B7280';
      case 'error': return '#DC2626';
      default: return '#6B7280';
    }
  };

  const getMaterialColor = (type: string) => {
    switch (type) {
      case 'plastic': return '#3B82F6';
      case 'paper': return '#F59E0B';
      case 'metal': return '#6B7280';
      case 'glass': return '#10B981';
      case 'organic': return '#22C55E';
      default: return '#9CA3AF';
    }
  };

  return (
    <group ref={meshRef} position={[site.position.x, site.position.y, site.position.z]}>
      {/* Bâtiment principal */}
      <Box
        args={[site.dimensions.width, site.dimensions.height, site.dimensions.depth]}
        position={[0, site.dimensions.height / 2, 0]}
        onPointerOver={() => setHovered(site.id)}
        onPointerOut={() => setHovered(null)}
        onClick={() => setSelected(selected === site.id ? null : site.id)}
      >
        <meshStandardMaterial 
          color={getStatusColor(site.status)} 
          transparent 
          opacity={hovered === site.id ? 0.8 : 0.6}
          wireframe={selected === site.id}
        />
      </Box>

      {/* Texte du nom du site */}
      <Text
        position={[0, site.dimensions.height + 5, 0]}
        fontSize={3}
        color="#1F2937"
        anchorX="center"
        anchorY="middle"
      >
        {site.name}
      </Text>

      {/* Matériaux stockés */}
      {site.materials.map((material, index) => (
        <group key={`material-${index}`}>
          <Box
            args={[8, 4, 8]}
            position={[
              material.position.x - site.position.x,
              material.position.y + 2,
              material.position.z - site.position.z
            ]}
          >
            <meshStandardMaterial color={getMaterialColor(material.type)} />
          </Box>
          <Text
            position={[
              material.position.x - site.position.x,
              material.position.y + 6,
              material.position.z - site.position.z
            ]}
            fontSize={1.5}
            color="#374151"
            anchorX="center"
          >
            {material.type}: {material.quantity}t
          </Text>
        </group>
      ))}

      {/* Équipements */}
      {site.equipment.map((equipment, index) => (
        <group 
          key={`equipment-${index}`}
          position={[
            equipment.position.x - site.position.x,
            equipment.position.y,
            equipment.position.z - site.position.z
          ]}
          rotation={[equipment.rotation.x, equipment.rotation.y, equipment.rotation.z]}
        >
          {equipment.type === 'conveyor' && (
            <Box args={[20, 1, 3]}>
              <meshStandardMaterial 
                color={equipment.status === 'active' ? '#059669' : '#6B7280'} 
              />
            </Box>
          )}
          {equipment.type === 'separator' && (
            <Cylinder args={[3, 3, 6, 8]}>
              <meshStandardMaterial 
                color={equipment.status === 'active' ? '#0D9488' : '#6B7280'} 
              />
            </Cylinder>
          )}
          {equipment.type === 'compactor' && (
            <Box args={[6, 8, 6]}>
              <meshStandardMaterial 
                color={equipment.status === 'active' ? '#7C3AED' : '#6B7280'} 
              />
            </Box>
          )}
          {equipment.type === 'crane' && (
            <group>
              <Cylinder args={[0.5, 0.5, 12, 8]} position={[0, 6, 0]}>
                <meshStandardMaterial color="#DC2626" />
              </Cylinder>
              <Box args={[15, 1, 1]} position={[7.5, 10, 0]}>
                <meshStandardMaterial color="#DC2626" />
              </Box>
            </group>
          )}
        </group>
      ))}

      {/* Capteurs */}
      {site.sensors.map((sensor, index) => (
        <group 
          key={`sensor-${index}`}
          position={[
            sensor.position.x - site.position.x,
            sensor.position.y,
            sensor.position.z - site.position.z
          ]}
        >
          <Sphere args={[0.8]}>
            <meshStandardMaterial 
              color={sensor.status === 'online' ? '#10B981' : '#EF4444'}
              emissive={sensor.status === 'online' ? '#065F46' : '#7F1D1D'}
              emissiveIntensity={0.3}
            />
          </Sphere>
          {config.qualityLevel !== 'low' && (
            <Html distanceFactor={10}>
              <div className="bg-white p-2 rounded shadow-lg text-xs">
                <div className="font-bold">{sensor.type}</div>
                <div>{sensor.value.toFixed(1)}</div>
              </div>
            </Html>
          )}
        </group>
      ))}

      {/* Zones de sécurité */}
      {config.qualityLevel === 'high' && site.safety.zones.map((zone, index) => (
        <Box
          key={`safety-${index}`}
          args={[
            zone.bounds.max.x - zone.bounds.min.x,
            0.1,
            zone.bounds.max.z - zone.bounds.min.z
          ]}
          position={[
            (zone.bounds.max.x + zone.bounds.min.x) / 2 - site.position.x,
            0.05,
            (zone.bounds.max.z + zone.bounds.min.z) / 2 - site.position.z
          ]}
        >
          <meshStandardMaterial 
            color={
              zone.type === 'safe' ? '#10B981' :
              zone.type === 'caution' ? '#F59E0B' : '#EF4444'
            }
            transparent
            opacity={0.2}
          />
        </Box>
      ))}

      {/* Sorties de secours */}
      {site.safety.emergencyExits.map((exit, index) => (
        <Cylinder
          key={`exit-${index}`}
          args={[1, 1, 0.2, 8]}
          position={[exit.x - site.position.x, 0.1, exit.z - site.position.z]}
        >
          <meshStandardMaterial color="#DC2626" />
        </Cylinder>
      ))}
    </group>
  );
}

// Composant de contrôle de caméra
function CameraController({ controls }: { controls: any }) {
  const { camera } = useThree();

  useEffect(() => {
    camera.position.set(controls.camera.position.x, controls.camera.position.y, controls.camera.position.z);
    camera.lookAt(controls.camera.target.x, controls.camera.target.y, controls.camera.target.z);

    // Vérifier si c'est une PerspectiveCamera avant de définir fov
    if ('fov' in camera) {
      (camera as THREE.PerspectiveCamera).fov = controls.camera.fov;
    }
    camera.updateProjectionMatrix();
  }, [camera, controls]);

  return null;
}

// Composant principal
export function Site3DViewer({ siteId, enableAR = false, enableDigitalTwin = true }: Site3DViewerProps) {
  const {
    getSites,
    getSite,
    startDigitalTwin,
    stopDigitalTwin,
    getDigitalTwinConfig,
    updateDigitalTwinConfig,
    getViewerControls,
    simulateARScan,
    optimizeForMobile
  } = useSite3DVisualization();

  const [sites, setSites] = useState<Site3DData[]>([]);
  const [selectedSiteId, setSelectedSiteId] = useState(siteId || '');
  const [selectedSite, setSelectedSite] = useState<Site3DData | null>(null);
  const [config, setConfig] = useState<DigitalTwinConfig | null>(null);
  const [controls, setControls] = useState(getViewerControls());
  const [isRunning, setIsRunning] = useState(false);
  const [arScans, setArScans] = useState<ARScanResult[]>([]);
  const [viewMode, setViewMode] = useState<'3d' | 'ar' | 'twin'>('3d');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Détection mobile
    setIsMobile(window.innerWidth < 768);
    
    setSites(getSites());
    if (selectedSiteId) {
      const site = getSite(selectedSiteId);
      const siteConfig = getDigitalTwinConfig(selectedSiteId);
      setSelectedSite(site || null);
      setConfig(siteConfig || null);
    }
  }, [selectedSiteId]);

  useEffect(() => {
    if (isMobile && config) {
      const mobileConfig = optimizeForMobile();
      updateDigitalTwinConfig(selectedSiteId, mobileConfig);
      setConfig(mobileConfig);
    }
  }, [isMobile]);

  const handleStartDigitalTwin = () => {
    if (selectedSiteId) {
      startDigitalTwin(selectedSiteId);
      setIsRunning(true);
    }
  };

  const handleStopDigitalTwin = () => {
    stopDigitalTwin();
    setIsRunning(false);
  };

  const handleARScan = () => {
    if (!selectedSite) return;
    
    // Simulation de scan AR à une position aléatoire
    const randomPosition = {
      x: selectedSite.position.x + (Math.random() - 0.5) * 20,
      y: selectedSite.position.y + Math.random() * 5,
      z: selectedSite.position.z + (Math.random() - 0.5) * 20
    };
    
    const scanResult = simulateARScan(randomPosition);
    setArScans(prev => [scanResult, ...prev.slice(0, 9)]); // Garder les 10 derniers scans
  };

  const handleConfigChange = (key: keyof DigitalTwinConfig, value: any) => {
    if (config && selectedSiteId) {
      const newConfig = { ...config, [key]: value };
      updateDigitalTwinConfig(selectedSiteId, newConfig);
      setConfig(newConfig);
    }
  };

  const resetCamera = () => {
    setControls(getViewerControls());
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Eye className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-indigo-800">
                  Visualisation 3D & AR
                </CardTitle>
                <CardDescription className="text-indigo-600">
                  Jumeau numérique immersif des sites de déchets
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={isRunning ? "default" : "secondary"}>
                {isRunning ? "Digital Twin actif" : "Statique"}
              </Badge>
              <Select value={viewMode} onValueChange={(value: 'ar' | '3d' | 'twin') => setViewMode(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3d">Vue 3D</SelectItem>
                  <SelectItem value="twin">Digital Twin</SelectItem>
                  <SelectItem value="ar">Mode AR</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Sélection du Site</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Select value={selectedSiteId} onValueChange={setSelectedSiteId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choisir un site" />
                </SelectTrigger>
                <SelectContent>
                  {sites.map(site => (
                    <SelectItem key={site.id} value={site.id}>
                      {site.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedSite && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Statut:</span>
                    <Badge variant={selectedSite.status === 'operational' ? 'default' : 'secondary'}>
                      {selectedSite.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Capacité:</span>
                    <span>{selectedSite.capacity.current}/{selectedSite.capacity.maximum} {selectedSite.capacity.unit}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Équipements:</span>
                    <span>{selectedSite.equipment.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Capteurs:</span>
                    <span>{selectedSite.sensors.filter(s => s.status === 'online').length}/{selectedSite.sensors.length}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Contrôles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex space-x-2">
                {isRunning ? (
                  <Button onClick={handleStopDigitalTwin} size="sm" variant="outline">
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                ) : (
                  <Button onClick={handleStartDigitalTwin} size="sm" disabled={!selectedSiteId}>
                    <Play className="h-4 w-4 mr-1" />
                    Start
                  </Button>
                )}
                <Button onClick={resetCamera} size="sm" variant="outline">
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>

              {enableAR && (
                <Button onClick={handleARScan} size="sm" className="w-full" variant="outline">
                  <Scan className="h-4 w-4 mr-2" />
                  Scan AR
                </Button>
              )}
            </CardContent>
          </Card>

          {config && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <label className="text-xs font-medium">Qualité</label>
                  <Select 
                    value={config.qualityLevel} 
                    onValueChange={(value: 'low' | 'medium' | 'high' | 'ultra') => 
                      handleConfigChange('qualityLevel', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Basse</SelectItem>
                      <SelectItem value="medium">Moyenne</SelectItem>
                      <SelectItem value="high">Haute</SelectItem>
                      <SelectItem value="ultra">Ultra</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Physique</span>
                    <input
                      type="checkbox"
                      checked={config.enablePhysics}
                      onChange={(e) => handleConfigChange('enablePhysics', e.target.checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Particules</span>
                    <input
                      type="checkbox"
                      checked={config.enableParticles}
                      onChange={(e) => handleConfigChange('enableParticles', e.target.checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Ombres</span>
                    <input
                      type="checkbox"
                      checked={config.enableShadows}
                      onChange={(e) => handleConfigChange('enableShadows', e.target.checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Scans AR récents */}
          {arScans.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Scans AR Récents</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {arScans.slice(0, 3).map((scan, index) => (
                  <div key={scan.id} className="p-2 bg-gray-50 rounded text-xs">
                    <div className="font-medium">{scan.wasteType}</div>
                    <div className="text-gray-600">
                      {scan.estimatedWeight.toFixed(1)}kg - {(scan.confidence * 100).toFixed(0)}%
                    </div>
                    <div className="text-gray-500">
                      Recyclabilité: {(scan.recyclability * 100).toFixed(0)}%
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Visualisation 3D */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-0">
              <div className="h-[600px] w-full bg-gradient-to-b from-sky-200 to-sky-50 rounded-lg overflow-hidden">
                <Suspense fallback={
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                      <div className="text-gray-600">Chargement de la scène 3D...</div>
                    </div>
                  </div>
                }>
                  <Canvas
                    camera={{ position: [50, 30, 50], fov: 75 }}
                    shadows={config?.enableShadows}
                  >
                    <CameraController controls={controls} />
                    
                    {/* Éclairage */}
                    <ambientLight intensity={0.4} />
                    <directionalLight 
                      position={[100, 100, 50]} 
                      intensity={1} 
                      castShadow={config?.enableShadows}
                      shadow-mapSize-width={2048}
                      shadow-mapSize-height={2048}
                    />
                    
                    {/* Environnement */}
                    {config?.qualityLevel !== 'low' && <Environment preset="city" />}
                    
                    {/* Grille au sol */}
                    <Grid 
                      args={[200, 200]} 
                      position={[0, -0.1, 0]}
                      cellColor="#94A3B8"
                      sectionColor="#475569"
                    />

                    {/* Sites 3D */}
                    {selectedSite && config && (
                      <Site3DModel site={selectedSite} config={config} />
                    )}

                    {/* Contrôles de navigation */}
                    <OrbitControls 
                      enablePan={true}
                      enableZoom={true}
                      enableRotate={true}
                      maxPolarAngle={Math.PI / 2}
                      minDistance={10}
                      maxDistance={200}
                    />
                  </Canvas>
                </Suspense>
              </div>
            </CardContent>
          </Card>

          {/* Informations temps réel */}
          {selectedSite && isRunning && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-sm">Données Temps Réel</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="h-4 w-4 text-red-500" />
                    <div>
                      <div className="text-xs text-gray-600">Température</div>
                      <div className="font-bold">{selectedSite.environmental.temperature.toFixed(1)}°C</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Gauge className="h-4 w-4 text-blue-500" />
                    <div>
                      <div className="text-xs text-gray-600">Humidité</div>
                      <div className="font-bold">{selectedSite.environmental.humidity.toFixed(0)}%</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-green-500" />
                    <div>
                      <div className="text-xs text-gray-600">Qualité Air</div>
                      <div className="font-bold">{selectedSite.environmental.airQuality.toFixed(0)}/100</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                    <div>
                      <div className="text-xs text-gray-600">Bruit</div>
                      <div className="font-bold">{selectedSite.environmental.noiseLevel.toFixed(0)} dB</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

export default Site3DViewer;

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  Activity,
  BarChart3
} from 'lucide-react';

interface MetricData {
  timestamp: Date;
  confidenceScore: number;
  responseTime: number;
  errorCount: number;
  recoveryTime?: number;
}

interface ValidationMetrics {
  uptime: number;
  averageConfidence: number;
  averageRecoveryTime: number;
  errorReduction: number;
  lastIncident: Date | null;
  totalIncidents: number;
}

export function MetricsDashboard() {
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [validationMetrics, setValidationMetrics] = useState<ValidationMetrics | null>(null);
  const [isCollecting, setIsCollecting] = useState(false);

  // Simulate metrics collection
  useEffect(() => {
    const interval = setInterval(() => {
      if (isCollecting) {
        collectMetrics();
      }
    }, 30000); // Collect every 30 seconds

    return () => clearInterval(interval);
  }, [isCollecting]);

  const collectMetrics = async () => {
    try {
      // Simulate health check
      const startTime = Date.now();
      
      // Mock API call to health endpoint
      const response = await fetch('/api/health').catch(() => ({ ok: false }));
      const responseTime = Date.now() - startTime;
      
      // Mock confidence score calculation
      const confidenceScore = response.ok ? 85 + Math.random() * 15 : 30 + Math.random() * 30;
      
      const newMetric: MetricData = {
        timestamp: new Date(),
        confidenceScore: Math.round(confidenceScore),
        responseTime,
        errorCount: response.ok ? 0 : 1
      };

      setMetrics(prev => [...prev.slice(-19), newMetric]); // Keep last 20 metrics
    } catch (error) {
      console.error('Failed to collect metrics:', error);
    }
  };

  // Calculate validation metrics
  useEffect(() => {
    if (metrics.length > 0) {
      const now = new Date();
      const last24h = metrics.filter(m => 
        now.getTime() - m.timestamp.getTime() < 24 * 60 * 60 * 1000
      );

      const uptime = last24h.length > 0 
        ? (last24h.filter(m => m.confidenceScore > 60).length / last24h.length) * 100
        : 100;

      const averageConfidence = last24h.length > 0
        ? last24h.reduce((sum, m) => sum + m.confidenceScore, 0) / last24h.length
        : 0;

      const errorMetrics = last24h.filter(m => m.errorCount > 0);
      const lastIncident = errorMetrics.length > 0 
        ? errorMetrics[errorMetrics.length - 1].timestamp
        : null;

      setValidationMetrics({
        uptime: Math.round(uptime),
        averageConfidence: Math.round(averageConfidence),
        averageRecoveryTime: 4.2, // Mock data
        errorReduction: 73, // Mock data
        lastIncident,
        totalIncidents: errorMetrics.length
      });
    }
  }, [metrics]);

  const getMetricTrend = (current: number, previous: number) => {
    if (current > previous) return 'up';
    if (current < previous) return 'down';
    return 'stable';
  };

  const getMetricIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const startMetricsCollection = () => {
    setIsCollecting(true);
    collectMetrics(); // Collect immediately
  };

  const stopMetricsCollection = () => {
    setIsCollecting(false);
  };

  const exportMetrics = () => {
    const dataStr = JSON.stringify({
      metrics,
      validationMetrics,
      exportedAt: new Date().toISOString()
    }, null, 2);
    
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `circulab-metrics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Metrics Collection Control
          </CardTitle>
          <CardDescription>
            Monitor and validate the defensive architecture performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={startMetricsCollection}
              disabled={isCollecting}
              variant={isCollecting ? "secondary" : "default"}
            >
              {isCollecting ? "Collecting..." : "Start Collection"}
            </Button>
            
            <Button
              onClick={stopMetricsCollection}
              disabled={!isCollecting}
              variant="outline"
            >
              Stop Collection
            </Button>
            
            <Button
              onClick={exportMetrics}
              disabled={metrics.length === 0}
              variant="outline"
            >
              Export Data
            </Button>
            
            <Badge variant={isCollecting ? "default" : "secondary"}>
              {isCollecting ? "Active" : "Inactive"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Validation Metrics */}
      {validationMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Uptime */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{validationMetrics.uptime}%</div>
              <p className="text-xs text-muted-foreground">
                Last 24 hours
              </p>
              <Badge 
                variant={validationMetrics.uptime > 95 ? "default" : "destructive"}
                className="mt-2"
              >
                {validationMetrics.uptime > 95 ? "Excellent" : "Needs Attention"}
              </Badge>
            </CardContent>
          </Card>

          {/* Average Confidence */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{validationMetrics.averageConfidence}%</div>
              <p className="text-xs text-muted-foreground">
                System health score
              </p>
              <Badge 
                variant={validationMetrics.averageConfidence > 80 ? "default" : "secondary"}
                className="mt-2"
              >
                {validationMetrics.averageConfidence > 80 ? "Healthy" : "Warning"}
              </Badge>
            </CardContent>
          </Card>

          {/* Recovery Time */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Recovery</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{validationMetrics.averageRecoveryTime}min</div>
              <p className="text-xs text-muted-foreground">
                Time to recover from errors
              </p>
              <Badge 
                variant={validationMetrics.averageRecoveryTime < 5 ? "default" : "secondary"}
                className="mt-2"
              >
                {validationMetrics.averageRecoveryTime < 5 ? "Fast" : "Acceptable"}
              </Badge>
            </CardContent>
          </Card>

          {/* Error Reduction */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Reduction</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{validationMetrics.errorReduction}%</div>
              <p className="text-xs text-muted-foreground">
                Compared to baseline
              </p>
              <Badge variant="default" className="mt-2">
                Improved
              </Badge>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Metrics</CardTitle>
          <CardDescription>
            Last {metrics.length} health check results
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No metrics collected yet. Start collection to see data.
            </div>
          ) : (
            <div className="space-y-2">
              {metrics.slice(-10).reverse().map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant={metric.confidenceScore > 80 ? "default" : 
                               metric.confidenceScore > 60 ? "secondary" : "destructive"}
                    >
                      {metric.confidenceScore}%
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {metric.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm">
                      {metric.responseTime}ms
                    </span>
                    {metric.errorCount > 0 && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Success Criteria */}
      <Card>
        <CardHeader>
          <CardTitle>Validation Criteria</CardTitle>
          <CardDescription>
            Target metrics for defensive architecture validation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">✅ Success Criteria</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Confidence score maintained > 80% for 24h</li>
                <li>• Recovery time < 5 minutes for common errors</li>
                <li>• Zero development interruptions from extensions</li>
                <li>• 70% reduction in debugging time</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">📊 Current Status</h4>
              <ul className="text-sm space-y-1">
                <li className="flex items-center gap-2">
                  {validationMetrics?.averageConfidence && validationMetrics.averageConfidence > 80 ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  }
                  Confidence: {validationMetrics?.averageConfidence || 0}%
                </li>
                <li className="flex items-center gap-2">
                  {validationMetrics?.averageRecoveryTime && validationMetrics.averageRecoveryTime < 5 ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  }
                  Recovery: {validationMetrics?.averageRecoveryTime || 0}min
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Error Reduction: {validationMetrics?.errorReduction || 0}%
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

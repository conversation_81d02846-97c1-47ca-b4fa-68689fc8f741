"use client";

import React, { useState, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Map, 
  Route, 
  Layers, 
  BarChart3,
  TrendingUp,
  MapPin,
  Navigation,
  Zap,
  Target,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Truck
} from 'lucide-react';
import { 
  useGeospatialIntelligence, 
  WasteCollectionPoint, 
  OptimizedRoute, 
  HeatmapData, 
  ClusterAnalysis, 
  PredictionZone 
} from '@/lib/geospatial/intelligenceEngine';

// Import dynamique pour éviter les erreurs SSR avec Leaflet
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });
const Popup = dynamic(() => import('react-leaflet').then(mod => mod.Popup), { ssr: false });
const Circle = dynamic(() => import('react-leaflet').then(mod => mod.Circle), { ssr: false });
const Polyline = dynamic(() => import('react-leaflet').then(mod => mod.Polyline), { ssr: false });
const Rectangle = dynamic(() => import('react-leaflet').then(mod => mod.Rectangle), { ssr: false });

interface GeospatialMapProps {
  height?: number;
  enableRouteOptimization?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  enablePredictions?: boolean;
}

// Composant de marqueur personnalisé
function CollectionPointMarker({ point, onClick }: { point: WasteCollectionPoint; onClick?: () => void }) {
  const getMarkerColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#DC2626';
      case 'high': return '#EA580C';
      case 'medium': return '#D97706';
      case 'low': return '#059669';
      default: return '#6B7280';
    }
  };

  const fillRate = point.capacity.current / point.capacity.maximum;

  return (
    <Marker 
      position={[point.position.lat, point.position.lng]}
      eventHandlers={{ click: onClick }}
    >
      <Popup>
        <div className="p-2 min-w-[200px]">
          <h3 className="font-bold text-sm mb-2">{point.name}</h3>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Type:</span>
              <Badge variant="outline" className="text-xs">{point.type}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Priorité:</span>
              <Badge 
                style={{ backgroundColor: getMarkerColor(point.priority), color: 'white' }}
                className="text-xs"
              >
                {point.priority}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Capacité:</span>
              <span>{point.capacity.current.toFixed(0)}/{point.capacity.maximum.toFixed(0)} {point.capacity.unit}</span>
            </div>
            <div className="flex justify-between">
              <span>Taux de remplissage:</span>
              <span>{(fillRate * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span>Déchets:</span>
              <span>{point.wasteTypes.join(', ')}</span>
            </div>
            <div className="flex justify-between">
              <span>Dernière collecte:</span>
              <span>{point.lastCollection.toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Prochaine collecte:</span>
              <span>{point.nextScheduledCollection.toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </Popup>
    </Marker>
  );
}

// Composant de route optimisée
function OptimizedRouteDisplay({ route }: { route: OptimizedRoute }) {
  const routeCoordinates = route.points.map(point => [point.position.lat, point.position.lng] as [number, number]);

  return (
    <>
      <Polyline 
        positions={routeCoordinates}
        color="#3B82F6"
        weight={4}
        opacity={0.8}
      />
      {route.points.map((point, index) => (
        <Circle
          key={`route-${route.id}-${index}`}
          center={[point.position.lat, point.position.lng]}
          radius={50}
          color="#3B82F6"
          fillColor="#3B82F6"
          fillOpacity={0.3}
        />
      ))}
    </>
  );
}

// Composant de heatmap
function HeatmapDisplay({ data }: { data: HeatmapData[] }) {
  return (
    <>
      {data.map((point, index) => (
        <Circle
          key={`heatmap-${index}`}
          center={[point.position.lat, point.position.lng]}
          radius={point.intensity * 200}
          color={`hsl(${(1 - point.intensity) * 120}, 70%, 50%)`}
          fillColor={`hsl(${(1 - point.intensity) * 120}, 70%, 50%)`}
          fillOpacity={0.4}
          weight={2}
        />
      ))}
    </>
  );
}

// Composant de clusters
function ClusterDisplay({ clusters }: { clusters: ClusterAnalysis[] }) {
  return (
    <>
      {clusters.map((cluster, index) => (
        <Circle
          key={`cluster-${index}`}
          center={[cluster.center.lat, cluster.center.lng]}
          radius={cluster.radius}
          color="#8B5CF6"
          fillColor="#8B5CF6"
          fillOpacity={0.1}
          weight={2}
          dashArray="5, 5"
        >
          <Popup>
            <div className="p-2">
              <h3 className="font-bold text-sm mb-2">Cluster {cluster.id}</h3>
              <div className="space-y-1 text-xs">
                <div>Points: {cluster.points.length}</div>
                <div>Capacité moyenne: {cluster.characteristics.averageCapacity.toFixed(0)} kg</div>
                <div>Taux de remplissage: {(cluster.characteristics.averageFillRate * 100).toFixed(1)}%</div>
                <div>Type dominant: {cluster.characteristics.dominantWasteType}</div>
                <div>Fréquence: {cluster.characteristics.collectionFrequency} jours</div>
              </div>
            </div>
          </Popup>
        </Circle>
      ))}
    </>
  );
}

// Composant de zones de prédiction
function PredictionZoneDisplay({ zones }: { zones: PredictionZone[] }) {
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return '#DC2626';
      case 'medium': return '#D97706';
      case 'low': return '#059669';
      default: return '#6B7280';
    }
  };

  return (
    <>
      {zones.map((zone, index) => (
        <Rectangle
          key={`zone-${index}`}
          bounds={[
            [zone.bounds.south, zone.bounds.west],
            [zone.bounds.north, zone.bounds.east]
          ]}
          color={getRiskColor(zone.riskLevel)}
          fillColor={getRiskColor(zone.riskLevel)}
          fillOpacity={0.2}
          weight={2}
        >
          <Popup>
            <div className="p-2">
              <h3 className="font-bold text-sm mb-2">Zone de Prédiction</h3>
              <div className="space-y-1 text-xs">
                <div>Génération actuelle: {zone.wasteGeneration.current.toFixed(1)} tonnes/mois</div>
                <div>Prédiction 6 mois: {zone.wasteGeneration.predicted6Months.toFixed(1)} tonnes/mois</div>
                <div>Prédiction 12 mois: {zone.wasteGeneration.predicted12Months.toFixed(1)} tonnes/mois</div>
                <div>Confiance: {(zone.wasteGeneration.confidence * 100).toFixed(0)}%</div>
                <div>Niveau de risque: 
                  <Badge 
                    style={{ backgroundColor: getRiskColor(zone.riskLevel), color: 'white' }}
                    className="ml-1 text-xs"
                  >
                    {zone.riskLevel}
                  </Badge>
                </div>
              </div>
            </div>
          </Popup>
        </Rectangle>
      ))}
    </>
  );
}

export function GeospatialMap({ 
  height = 600, 
  enableRouteOptimization = true,
  enableHeatmap = true,
  enableClustering = true,
  enablePredictions = true
}: GeospatialMapProps) {
  const {
    getCollectionPoints,
    optimizeRoutes,
    generateHeatmapData,
    performClusterAnalysis,
    generatePredictionZones,
    getStatistics
  } = useGeospatialIntelligence();

  const [collectionPoints, setCollectionPoints] = useState<WasteCollectionPoint[]>([]);
  const [optimizedRoutes, setOptimizedRoutes] = useState<OptimizedRoute[]>([]);
  const [heatmapData, setHeatmapData] = useState<HeatmapData[]>([]);
  const [clusters, setClusters] = useState<ClusterAnalysis[]>([]);
  const [predictionZones, setPredictionZones] = useState<PredictionZone[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [selectedPoint, setSelectedPoint] = useState<WasteCollectionPoint | null>(null);
  const [mapMode, setMapMode] = useState<'points' | 'routes' | 'heatmap' | 'clusters' | 'predictions'>('points');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    loadData();
  }, []);

  const loadData = () => {
    setCollectionPoints(getCollectionPoints());
    setHeatmapData(generateHeatmapData());
    setClusters(performClusterAnalysis());
    setPredictionZones(generatePredictionZones());
    setStatistics(getStatistics());
  };

  const handleOptimizeRoutes = async () => {
    setIsOptimizing(true);
    try {
      const constraints = {
        maxCapacity: 2000, // kg
        maxTime: 480, // 8 heures
        vehicleType: 'medium',
        timeWindows: [{ start: '06:00', end: '18:00' }]
      };
      
      const routes = optimizeRoutes(constraints);
      setOptimizedRoutes(routes);
      setMapMode('routes');
    } catch (error) {
      console.error('Erreur lors de l\'optimisation:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const parisCenter: [number, number] = [48.8566, 2.3522];

  // Calculs de métriques
  const totalOptimizationSavings = useMemo(() => {
    if (optimizedRoutes.length === 0) return null;
    
    const totalDistance = optimizedRoutes.reduce((sum, route) => sum + route.totalDistance, 0);
    const totalTime = optimizedRoutes.reduce((sum, route) => sum + route.estimatedTime, 0);
    const totalFuel = optimizedRoutes.reduce((sum, route) => sum + route.fuelConsumption, 0);
    const totalCO2 = optimizedRoutes.reduce((sum, route) => sum + route.co2Emissions, 0);
    const avgDistanceReduction = optimizedRoutes.reduce((sum, route) => sum + route.optimizationMetrics.distanceReduction, 0) / optimizedRoutes.length;
    
    return {
      totalDistance: totalDistance.toFixed(1),
      totalTime: (totalTime / 60).toFixed(1),
      totalFuel: totalFuel.toFixed(1),
      totalCO2: totalCO2.toFixed(1),
      avgDistanceReduction: avgDistanceReduction.toFixed(1),
      routeCount: optimizedRoutes.length
    };
  }, [optimizedRoutes]);

  if (!isClient) {
    return (
      <div className="h-[600px] flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Chargement de la carte...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Map className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-green-800">
                  Intelligence Géospatiale
                </CardTitle>
                <CardDescription className="text-green-600">
                  Optimisation IA des routes et analyse prédictive
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Select value={mapMode} onValueChange={(value: any) => setMapMode(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="points">Points de collecte</SelectItem>
                  <SelectItem value="routes">Routes optimisées</SelectItem>
                  <SelectItem value="heatmap">Carte de chaleur</SelectItem>
                  <SelectItem value="clusters">Clusters</SelectItem>
                  <SelectItem value="predictions">Prédictions</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistiques */}
      {statistics && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{statistics.totalPoints}</div>
                  <div className="text-xs text-gray-600">Points de collecte</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{(statistics.averageFillRate * 100).toFixed(0)}%</div>
                  <div className="text-xs text-gray-600">Taux de remplissage</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="text-2xl font-bold">{(statistics.averageRecyclingRate * 100).toFixed(0)}%</div>
                  <div className="text-xs text-gray-600">Taux de recyclage</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-orange-500" />
                <div>
                  <div className="text-2xl font-bold">{statistics.priorityCounts.urgent || 0}</div>
                  <div className="text-xs text-gray-600">Urgents</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-2xl font-bold">{(statistics.currentLoad / 1000).toFixed(1)}</div>
                  <div className="text-xs text-gray-600">Tonnes actuelles</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold">{(statistics.totalCapacity / 1000).toFixed(1)}</div>
                  <div className="text-xs text-gray-600">Capacité totale</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Optimisation des Routes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={handleOptimizeRoutes} 
                disabled={isOptimizing}
                className="w-full"
              >
                {isOptimizing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Optimisation...
                  </>
                ) : (
                  <>
                    <Route className="h-4 w-4 mr-2" />
                    Optimiser les Routes
                  </>
                )}
              </Button>

              {totalOptimizationSavings && (
                <div className="space-y-2 text-xs">
                  <div className="font-medium">Résultats d'optimisation:</div>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>Routes:</span>
                      <span>{totalOptimizationSavings.routeCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Distance totale:</span>
                      <span>{totalOptimizationSavings.totalDistance} km</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Temps total:</span>
                      <span>{totalOptimizationSavings.totalTime} h</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Carburant:</span>
                      <span>{totalOptimizationSavings.totalFuel} L</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CO₂:</span>
                      <span>{totalOptimizationSavings.totalCO2} kg</span>
                    </div>
                    <div className="flex justify-between text-green-600 font-medium">
                      <span>Réduction:</span>
                      <span>-{totalOptimizationSavings.avgDistanceReduction}%</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Modes d'Affichage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-1 gap-2">
                <Button 
                  variant={mapMode === 'points' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setMapMode('points')}
                >
                  <MapPin className="h-3 w-3 mr-1" />
                  Points
                </Button>
                <Button 
                  variant={mapMode === 'routes' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setMapMode('routes')}
                  disabled={optimizedRoutes.length === 0}
                >
                  <Route className="h-3 w-3 mr-1" />
                  Routes
                </Button>
                <Button 
                  variant={mapMode === 'heatmap' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setMapMode('heatmap')}
                >
                  <Activity className="h-3 w-3 mr-1" />
                  Heatmap
                </Button>
                <Button 
                  variant={mapMode === 'clusters' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setMapMode('clusters')}
                >
                  <Target className="h-3 w-3 mr-1" />
                  Clusters
                </Button>
                <Button 
                  variant={mapMode === 'predictions' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setMapMode('predictions')}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Prédictions
                </Button>
              </div>
            </CardContent>
          </Card>

          {selectedPoint && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Point Sélectionné</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs">
                <div className="font-medium">{selectedPoint.name}</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Type:</span>
                    <span>{selectedPoint.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Priorité:</span>
                    <Badge variant="outline" className="text-xs">{selectedPoint.priority}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Capacité:</span>
                    <span>{selectedPoint.capacity.current.toFixed(0)}/{selectedPoint.capacity.maximum.toFixed(0)} {selectedPoint.capacity.unit}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Déchets:</span>
                    <span>{selectedPoint.wasteTypes.join(', ')}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Carte */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-0">
              <div style={{ height: `${height}px` }} className="rounded-lg overflow-hidden">
                <MapContainer
                  center={parisCenter}
                  zoom={12}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  />

                  {/* Points de collecte */}
                  {(mapMode === 'points' || mapMode === 'routes') && collectionPoints.map(point => (
                    <CollectionPointMarker
                      key={point.id}
                      point={point}
                      onClick={() => setSelectedPoint(point)}
                    />
                  ))}

                  {/* Routes optimisées */}
                  {mapMode === 'routes' && optimizedRoutes.map(route => (
                    <OptimizedRouteDisplay key={route.id} route={route} />
                  ))}

                  {/* Heatmap */}
                  {mapMode === 'heatmap' && <HeatmapDisplay data={heatmapData} />}

                  {/* Clusters */}
                  {mapMode === 'clusters' && <ClusterDisplay clusters={clusters} />}

                  {/* Zones de prédiction */}
                  {mapMode === 'predictions' && <PredictionZoneDisplay zones={predictionZones} />}
                </MapContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default GeospatialMap;

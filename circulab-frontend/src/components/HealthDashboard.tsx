"use client";

import React, { useState, useEffect } from 'react';
import { HealthMonitor, SystemHealth, HealthStatus } from '@/lib/healthCheck';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Activity,
  Shield,
  Database,
  Wifi,
  HardDrive,
  Settings
} from 'lucide-react';

interface HealthDashboardProps {
  isMinimized?: boolean;
  onToggle?: () => void;
}

export function HealthDashboard({ isMinimized = false, onToggle }: HealthDashboardProps) {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const healthMonitor = HealthMonitor.getInstance();

  const performHealthCheck = async () => {
    setIsLoading(true);
    try {
      const healthStatus = await healthMonitor.performFullHealthCheck();
      setHealth(healthStatus);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    performHealthCheck();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(performHealthCheck, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={onToggle}
          variant="outline"
          size="sm"
          className={`shadow-lg ${health ? getStatusColor(health.overall.status) : 'bg-gray-100'}`}
        >
          {health && getStatusIcon(health.overall.status)}
          <span className="ml-2">
            Health: {health ? `${health.confidence}%` : '...'}
          </span>
        </Button>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Health Dashboard
          </CardTitle>
          <CardDescription>
            Real-time monitoring of application health and stability
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          {lastUpdate && (
            <span className="text-xs text-muted-foreground">
              Last check: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <Button
            onClick={performHealthCheck}
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {onToggle && (
            <Button onClick={onToggle} size="sm" variant="ghost">
              Minimize
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {!health ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Performing initial health check...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overall Status */}
            <div className="flex items-center justify-between p-4 rounded-lg border">
              <div className="flex items-center gap-3">
                {getStatusIcon(health.overall.status)}
                <div>
                  <h3 className="font-semibold">Overall System Health</h3>
                  <p className="text-sm text-muted-foreground">
                    {health.overall.message}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getConfidenceColor(health.confidence)}`}>
                  {health.confidence}%
                </div>
                <div className="text-xs text-muted-foreground">Confidence</div>
              </div>
            </div>

            {/* Individual Components */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Webpack Health */}
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Settings className="h-4 w-4" />
                  <span className="font-medium">Webpack</span>
                  {getStatusIcon(health.webpack.status)}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {health.webpack.message}
                </p>
                <Badge variant="outline" className={getStatusColor(health.webpack.status)}>
                  {health.webpack.status}
                </Badge>
              </div>

              {/* Extensions */}
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="h-4 w-4" />
                  <span className="font-medium">Extensions</span>
                  {getStatusIcon(health.extensions.status)}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {health.extensions.message}
                </p>
                <Badge variant="outline" className={getStatusColor(health.extensions.status)}>
                  {health.extensions.status}
                </Badge>
              </div>

              {/* Dependencies */}
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4" />
                  <span className="font-medium">Dependencies</span>
                  {getStatusIcon(health.dependencies.status)}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {health.dependencies.message}
                </p>
                <Badge variant="outline" className={getStatusColor(health.dependencies.status)}>
                  {health.dependencies.status}
                </Badge>
              </div>

              {/* API Health */}
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Wifi className="h-4 w-4" />
                  <span className="font-medium">API</span>
                  {getStatusIcon(health.api.status)}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {health.api.message}
                </p>
                <Badge variant="outline" className={getStatusColor(health.api.status)}>
                  {health.api.status}
                </Badge>
              </div>

              {/* Local Storage */}
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <HardDrive className="h-4 w-4" />
                  <span className="font-medium">Storage</span>
                  {getStatusIcon(health.localStorage.status)}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {health.localStorage.message}
                </p>
                <Badge variant="outline" className={getStatusColor(health.localStorage.status)}>
                  {health.localStorage.status}
                </Badge>
              </div>
            </div>

            {/* Detailed Information */}
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                  Technical Details (Development Mode)
                </summary>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                  <pre className="text-xs overflow-auto">
                    {JSON.stringify(health, null, 2)}
                  </pre>
                </div>
              </details>
            )}

            {/* Recommendations */}
            {health.overall.status !== 'healthy' && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Recommendations</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  {health.webpack.status === 'error' && (
                    <li>• Clear webpack cache and restart development server</li>
                  )}
                  {health.extensions.status === 'error' && (
                    <li>• Disable problematic VS Code extensions temporarily</li>
                  )}
                  {health.dependencies.status === 'error' && (
                    <li>• Run npm install to resolve missing dependencies</li>
                  )}
                  {health.api.status === 'error' && (
                    <li>• Check if the backend server is running on port 4000</li>
                  )}
                  {health.localStorage.status === 'error' && (
                    <li>• Clear browser cache and cookies</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Lightbulb, 
  Leaf,
  Target,
  BarChart3,
  Zap,
  Sparkles
} from 'lucide-react';
import { usePredictiveAI, WasteAnalysisInput, PredictionResult } from '@/lib/ai/predictiveEngine';

interface PredictiveAnalysisProps {
  initialData?: Partial<WasteAnalysisInput>;
  onPredictionComplete?: (result: PredictionResult) => void;
}

export function PredictiveAnalysis({ initialData, onPredictionComplete }: PredictiveAnalysisProps) {
  const { predictWasteValue, getAvailableModels } = usePredictiveAI();
  
  const [input, setInput] = useState<WasteAnalysisInput>({
    type: 'plastic_pet',
    quantity: 1000,
    location: 'Paris',
    composition: { pet: 0.95, contaminants: 0.05 },
    contamination: 0.05,
    moisture: 0.02,
    density: 1.38,
    seasonality: 'spring',
    ...initialData
  });

  const [prediction, setPrediction] = useState<PredictionResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState<string[]>([]);

  useEffect(() => {
    setAvailableModels(getAvailableModels());
  }, [getAvailableModels]);

  const handlePredict = async () => {
    setIsLoading(true);
    try {
      const result = await predictWasteValue(input);
      setPrediction(result);
      onPredictionComplete?.(result);
    } catch (error) {
      console.error('Erreur lors de la prédiction:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateInput = (field: keyof WasteAnalysisInput, value: any) => {
    setInput(prev => ({ ...prev, [field]: value }));
  };

  const wasteTypes = [
    { value: 'plastic_pet', label: 'Plastique PET' },
    { value: 'plastic_hdpe', label: 'Plastique HDPE' },
    { value: 'plastic_ldpe', label: 'Plastique LDPE' },
    { value: 'cardboard', label: 'Carton' },
    { value: 'paper', label: 'Papier' },
    { value: 'metal_aluminum', label: 'Aluminium' },
    { value: 'metal_steel', label: 'Acier' },
    { value: 'glass', label: 'Verre' },
    { value: 'organic', label: 'Organique' },
    { value: 'textile', label: 'Textile' },
    { value: 'electronic', label: 'Électronique' }
  ];

  const locations = [
    'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 
    'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'
  ];

  const seasons = [
    { value: 'spring', label: 'Printemps' },
    { value: 'summer', label: 'Été' },
    { value: 'autumn', label: 'Automne' },
    { value: 'winter', label: 'Hiver' }
  ];

  return (
    <div className="space-y-6">
      {/* Header avec IA */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Brain className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <CardTitle className="text-xl text-purple-800">
                Analyse Prédictive IA
                <Sparkles className="inline h-5 w-5 ml-2 text-yellow-500" />
              </CardTitle>
              <CardDescription className="text-purple-600">
                Prédiction intelligente de la valorisation des déchets
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Formulaire d'entrée */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Paramètres d'Analyse</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Type de déchet */}
            <div className="space-y-2">
              <Label htmlFor="waste-type">Type de déchet</Label>
              <Select value={input.type} onValueChange={(value: string) => updateInput('type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un type" />
                </SelectTrigger>
                <SelectContent>
                  {wasteTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Quantité */}
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantité (kg)</Label>
              <Input
                id="quantity"
                type="number"
                value={input.quantity}
                onChange={(e) => updateInput('quantity', Number(e.target.value))}
                placeholder="1000"
              />
            </div>

            {/* Localisation */}
            <div className="space-y-2">
              <Label htmlFor="location">Localisation</Label>
              <Select value={input.location} onValueChange={(value: string) => updateInput('location', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une ville" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map(location => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Paramètres avancés */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contamination">Contamination (%)</Label>
                <Input
                  id="contamination"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={(input.contamination || 0) * 100}
                  onChange={(e) => updateInput('contamination', Number(e.target.value) / 100)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="moisture">Humidité (%)</Label>
                <Input
                  id="moisture"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={(input.moisture || 0) * 100}
                  onChange={(e) => updateInput('moisture', Number(e.target.value) / 100)}
                />
              </div>
            </div>

            {/* Saisonnalité */}
            <div className="space-y-2">
              <Label htmlFor="seasonality">Saisonnalité</Label>
              <Select value={input.seasonality} onValueChange={(value: string) => updateInput('seasonality', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une saison" />
                </SelectTrigger>
                <SelectContent>
                  {seasons.map(season => (
                    <SelectItem key={season.value} value={season.value}>
                      {season.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bouton d'analyse */}
            <Button 
              onClick={handlePredict} 
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {isLoading ? (
                <>
                  <Zap className="mr-2 h-4 w-4 animate-spin" />
                  Analyse en cours...
                </>
              ) : (
                <>
                  <Brain className="mr-2 h-4 w-4" />
                  Analyser avec l'IA
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Résultats de prédiction */}
        {prediction && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Résultats de l'Analyse</span>
                <Badge variant="secondary" className="ml-auto">
                  Confiance: {(prediction.confidence * 100).toFixed(1)}%
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="valuation" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="valuation">Valorisation</TabsTrigger>
                  <TabsTrigger value="trends">Tendances</TabsTrigger>
                  <TabsTrigger value="optimization">Optimisation</TabsTrigger>
                  <TabsTrigger value="impact">Impact</TabsTrigger>
                </TabsList>

                <TabsContent value="valuation" className="space-y-4">
                  <div className="text-center p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                    <div className="text-3xl font-bold text-green-600">
                      {prediction.valueEstimate.toLocaleString('fr-FR', { 
                        style: 'currency', 
                        currency: 'EUR' 
                      })}
                    </div>
                    <div className="text-sm text-green-700 mt-1">
                      Valeur estimée
                    </div>
                    <Progress 
                      value={prediction.confidence * 100} 
                      className="mt-3"
                    />
                    <div className="text-xs text-gray-600 mt-1">
                      Niveau de confiance: {(prediction.confidence * 100).toFixed(1)}%
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Traitement recommandé</Label>
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="font-medium text-blue-800">
                        {prediction.recommendedTreatment}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="trends" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">30 jours</span>
                      </div>
                      <div className="text-lg font-bold text-green-600">
                        {prediction.marketTrends.predicted30Days.toLocaleString('fr-FR', { 
                          style: 'currency', 
                          currency: 'EUR' 
                        })}
                      </div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">90 jours</span>
                      </div>
                      <div className="text-lg font-bold text-blue-600">
                        {prediction.marketTrends.predicted90Days.toLocaleString('fr-FR', { 
                          style: 'currency', 
                          currency: 'EUR' 
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">Volatilité du marché</span>
                    </div>
                    <Progress 
                      value={prediction.marketTrends.volatility * 100} 
                      className="mb-2"
                    />
                    <div className="text-xs text-yellow-700">
                      {(prediction.marketTrends.volatility * 100).toFixed(1)}% de volatilité
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="optimization" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Lightbulb className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium">Suggestions d'optimisation</span>
                    </div>
                    {prediction.optimizationSuggestions.map((suggestion, index) => (
                      <div key={index} className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                        <div className="text-sm text-yellow-800">{suggestion}</div>
                      </div>
                    ))}
                  </div>

                  {prediction.riskFactors.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="font-medium">Facteurs de risque</span>
                      </div>
                      {prediction.riskFactors.map((risk, index) => (
                        <div key={index} className="p-3 bg-red-50 rounded-lg border-l-4 border-red-400">
                          <div className="text-sm text-red-800">{risk}</div>
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="impact" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Leaf className="h-4 w-4 text-green-600" />
                      <span className="font-medium">Impact Carbone</span>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div className="p-3 bg-red-50 rounded-lg text-center">
                        <div className="text-sm text-red-600">Actuel</div>
                        <div className="font-bold text-red-700">
                          {prediction.carbonFootprint.current} kg CO₂
                        </div>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg text-center">
                        <div className="text-sm text-green-600">Optimisé</div>
                        <div className="font-bold text-green-700">
                          {prediction.carbonFootprint.optimized} kg CO₂
                        </div>
                      </div>
                      <div className="p-3 bg-blue-50 rounded-lg text-center">
                        <div className="text-sm text-blue-600">Réduction</div>
                        <div className="font-bold text-blue-700">
                          -{prediction.carbonFootprint.reduction} kg CO₂
                        </div>
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="text-sm text-green-800">
                        💡 L'optimisation proposée permettrait de réduire l'empreinte carbone de{' '}
                        <span className="font-bold">
                          {((prediction.carbonFootprint.reduction / prediction.carbonFootprint.current) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Informations sur les modèles */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Modèles IA Disponibles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {availableModels.map(model => (
              <Badge key={model} variant="outline" className="text-xs">
                {model}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PredictiveAnalysis;

"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  MessageCircle, 
  Send, 
  X, 
  Minimize2, 
  Maximize2,
  Camera,
  Mic,
  MicOff,
  Image,
  Bot,
  User,
  Sparkles,
  Brain,
  Zap,
  Upload
} from 'lucide-react';
import { 
  useConversationalAI, 
  ConversationMessage, 
  ConversationContext,
  ImageAnalysisResult 
} from '@/lib/ai/conversationalEngine';

interface ChatBotProps {
  userId?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme?: 'light' | 'dark' | 'auto';
  enableImageAnalysis?: boolean;
  enableVoice?: boolean;
}

export function ChatBot({ 
  userId = 'user_001',
  position = 'bottom-right',
  theme = 'auto',
  enableImageAnalysis = true,
  enableVoice = false
}: ChatBotProps) {
  const {
    processMessage,
    analyzeImage,
    createContext,
    getContext,
    updateContext
  } = useConversationalAI();

  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [context, setContext] = useState<ConversationContext | null>(null);
  const [isListening, setIsListening] = useState(false);
  const [imageAnalysis, setImageAnalysis] = useState<ImageAnalysisResult | null>(null);
  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialiser le contexte de conversation
    const newContext = createContext(userId);
    setContext(newContext);

    // Message de bienvenue
    const welcomeMessage: ConversationMessage = {
      id: 'welcome',
      type: 'assistant',
      content: '👋 Bonjour ! Je suis votre assistant IA CircuLab. Je peux vous aider avec l\'identification de déchets, l\'optimisation des routes, les prédictions, le monitoring IoT et bien plus ! Comment puis-je vous aider ?',
      timestamp: new Date(),
      metadata: {
        intent: 'greeting',
        confidence: 1.0
      }
    };

    setMessages([welcomeMessage]);
  }, [userId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !context) return;

    setIsTyping(true);
    const userMessage = inputMessage;
    setInputMessage('');

    try {
      const response = await processMessage(userMessage, context);
      setMessages(prev => [...prev, response]);
      
      // Mettre à jour le contexte local
      const updatedContext = getContext(context.sessionId);
      if (updatedContext) {
        setContext(updatedContext);
      }
    } catch (error) {
      console.error('Erreur lors du traitement du message:', error);
      const errorMessage: ConversationMessage = {
        id: `error_${Date.now()}`,
        type: 'assistant',
        content: 'Désolé, j\'ai rencontré une erreur. Pouvez-vous réessayer ?',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !enableImageAnalysis) return;

    setIsAnalyzingImage(true);

    try {
      // Simuler la lecture du fichier
      const reader = new FileReader();
      reader.onload = async (e) => {
        const imageData = e.target?.result as string;
        
        // Ajouter un message utilisateur avec l'image
        const imageMessage: ConversationMessage = {
          id: `img_${Date.now()}`,
          type: 'user',
          content: '📷 Image uploadée pour analyse',
          timestamp: new Date(),
          metadata: {
            attachments: [{
              type: 'image',
              url: imageData,
            }]
          }
        };
        setMessages(prev => [...prev, imageMessage]);

        // Analyser l'image
        const analysis = await analyzeImage(imageData);
        setImageAnalysis(analysis);

        // Créer la réponse d'analyse
        const analysisMessage: ConversationMessage = {
          id: `analysis_${Date.now()}`,
          type: 'assistant',
          content: `🔍 **Analyse d'image terminée !**\n\n📦 **Type détecté:** ${analysis.wasteType.replace('_', ' ')}\n✅ **Confiance:** ${(analysis.confidence * 100).toFixed(1)}%\n♻️ **Recyclabilité:** ${(analysis.recyclability * 100).toFixed(0)}%\n⚖️ **Poids estimé:** ${analysis.estimatedWeight?.toFixed(1)} kg\n⭐ **Score qualité:** ${(analysis.qualityScore! * 100).toFixed(0)}%\n\n**🎯 Recommandations:**\n${analysis.recommendations.map(r => `• ${r}`).join('\n')}\n\n**📊 Composition:**\n${Object.entries(analysis.composition).map(([k, v]) => `• ${k}: ${(v * 100).toFixed(0)}%`).join('\n')}`,
          timestamp: new Date(),
          metadata: {
            intent: 'waste_identification',
            confidence: analysis.confidence
          }
        };

        setMessages(prev => [...prev, analysisMessage]);
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Erreur lors de l\'analyse d\'image:', error);
    } finally {
      setIsAnalyzingImage(false);
    }
  };

  const handleVoiceToggle = () => {
    if (!enableVoice) return;
    
    if (isListening) {
      // Arrêter l'écoute
      setIsListening(false);
      // Ici on arrêterait la reconnaissance vocale
    } else {
      // Démarrer l'écoute
      setIsListening(true);
      // Ici on démarrerait la reconnaissance vocale
      // Pour la démo, on simule
      setTimeout(() => {
        setInputMessage('Analyser les déchets de cette semaine');
        setIsListening(false);
      }, 2000);
    }
  };

  const getPositionClasses = () => {
    const baseClasses = 'fixed z-50';
    switch (position) {
      case 'bottom-right':
        return `${baseClasses} bottom-4 right-4`;
      case 'bottom-left':
        return `${baseClasses} bottom-4 left-4`;
      case 'top-right':
        return `${baseClasses} top-4 right-4`;
      case 'top-left':
        return `${baseClasses} top-4 left-4`;
      default:
        return `${baseClasses} bottom-4 right-4`;
    }
  };

  const formatMessage = (content: string) => {
    // Convertir le markdown simple en HTML
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br/>');
  };

  if (!isOpen) {
    return (
      <div className={getPositionClasses()}>
        <Button
          onClick={() => setIsOpen(true)}
          className="h-14 w-14 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
          size="lg"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
        {/* Notification badge */}
        <div className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
          <Sparkles className="h-3 w-3 text-white" />
        </div>
      </div>
    );
  }

  return (
    <div className={getPositionClasses()}>
      <Card className={`w-96 h-[600px] shadow-2xl border-0 ${isMinimized ? 'h-16' : ''} transition-all duration-300`}>
        {/* Header */}
        <CardHeader className="pb-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Bot className="h-5 w-5" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium">Assistant IA CircuLab</CardTitle>
                <div className="flex items-center space-x-1 text-xs opacity-90">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>En ligne</span>
                  {isTyping && (
                    <>
                      <span>•</span>
                      <span>En train d'écrire...</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
              >
                {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-[calc(600px-80px)]">
            {/* Messages */}
            <div 
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
            >
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    {/* Avatar */}
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.type === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Brain className="h-4 w-4" />
                      )}
                    </div>

                    {/* Message bubble */}
                    <div className={`rounded-2xl px-4 py-3 ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white border shadow-sm'
                    }`}>
                      {/* Image attachment */}
                      {message.metadata?.attachments?.map((attachment, index) => (
                        attachment.type === 'image' && (
                          <div key={index} className="mb-2">
                            <img 
                              src={attachment.url} 
                              alt="Uploaded" 
                              className="max-w-full h-32 object-cover rounded-lg"
                            />
                          </div>
                        )
                      ))}

                      {/* Message content */}
                      <div 
                        className="text-sm whitespace-pre-wrap"
                        dangerouslySetInnerHTML={{ __html: formatMessage(message.content) }}
                      />

                      {/* Metadata */}
                      {message.metadata && (
                        <div className="mt-2 flex items-center space-x-2">
                          {message.metadata.intent && (
                            <Badge variant="secondary" className="text-xs">
                              {message.metadata.intent}
                            </Badge>
                          )}
                          {message.metadata.confidence && (
                            <Badge variant="outline" className="text-xs">
                              {(message.metadata.confidence * 100).toFixed(0)}%
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Timestamp */}
                      <div className={`text-xs mt-1 opacity-70 ${
                        message.type === 'user' ? 'text-white' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Typing indicator */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                      <Brain className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-white border rounded-2xl px-4 py-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Image analysis indicator */}
              {isAnalyzingImage && (
                <div className="flex justify-start">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                      <Zap className="h-4 w-4 text-white animate-pulse" />
                    </div>
                    <div className="bg-white border rounded-2xl px-4 py-3">
                      <div className="text-sm text-gray-600">
                        🔍 Analyse d'image en cours...
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input area */}
            <div className="p-4 border-t bg-white">
              <div className="flex items-center space-x-2">
                {/* Image upload */}
                {enableImageAnalysis && (
                  <>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isAnalyzingImage}
                      className="h-10 w-10 p-0"
                    >
                      {isAnalyzingImage ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      ) : (
                        <Camera className="h-4 w-4" />
                      )}
                    </Button>
                  </>
                )}

                {/* Voice input */}
                {enableVoice && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleVoiceToggle}
                    className={`h-10 w-10 p-0 ${isListening ? 'bg-red-100 border-red-300' : ''}`}
                  >
                    {isListening ? (
                      <MicOff className="h-4 w-4 text-red-600" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>
                )}

                {/* Text input */}
                <div className="flex-1">
                  <Input
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Tapez votre message..."
                    disabled={isTyping}
                    className="border-gray-300 focus:border-blue-500"
                  />
                </div>

                {/* Send button */}
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                  className="h-10 w-10 p-0 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>

              {/* Quick actions */}
              <div className="mt-2 flex flex-wrap gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInputMessage('Optimiser mes routes de collecte')}
                  className="text-xs h-7"
                >
                  🗺️ Optimiser routes
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInputMessage('Analyser les prédictions de déchets')}
                  className="text-xs h-7"
                >
                  📈 Prédictions
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInputMessage('État des capteurs IoT')}
                  className="text-xs h-7"
                >
                  🌐 IoT Status
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}

export default ChatBot;

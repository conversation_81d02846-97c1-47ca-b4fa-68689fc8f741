'use client';

import { useState } from 'react';
import { HealthDashboard } from '@/components/HealthDashboard';
import { MetricsDashboard } from '@/components/MetricsDashboard';
import { useErrorLogger } from '@/lib/safeImports';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function HealthPage() {
  const [showHealthDashboard, setShowHealthDashboard] = useState(true);
  const errorLogger = useErrorLogger();

  const clearErrors = () => {
    errorLogger.clearErrors();
  };

  const errors = errorLogger.getErrors();

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">System Health & Monitoring</h1>
        <p className="text-muted-foreground">
          Real-time system health monitoring, error tracking, and performance metrics
        </p>
      </div>

      <Tabs defaultValue="health" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="health">Health Dashboard</TabsTrigger>
          <TabsTrigger value="metrics">Performance Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="health" className="space-y-6">
          {/* Health Dashboard */}
          <HealthDashboard
            isMinimized={!showHealthDashboard}
            onToggle={() => setShowHealthDashboard(!showHealthDashboard)}
          />

      {/* Error Log */}
      {errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Error Log ({errors.length})
              <Button onClick={clearErrors} variant="outline" size="sm">
                Clear Errors
              </Button>
            </CardTitle>
            <CardDescription>Recent errors captured by the safe import system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-auto">
              {errors.map((errorEntry, index) => (
                <div key={index} className="p-3 bg-red-50 border border-red-200 rounded">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-red-800">{errorEntry.context}</span>
                    <span className="text-xs text-red-600">
                      {errorEntry.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm text-red-700">{errorEntry.error.message}</p>
                  {process.env.NODE_ENV === 'development' && errorEntry.error.stack && (
                    <details className="mt-2">
                      <summary className="text-xs text-red-600 cursor-pointer">Stack Trace</summary>
                      <pre className="text-xs text-red-600 mt-1 overflow-auto bg-red-100 p-2 rounded">
                        {errorEntry.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Health Monitoring Guide</CardTitle>
          <CardDescription>How to use the health monitoring system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">🟢 Healthy Status</h4>
              <p className="text-sm text-muted-foreground">
                All systems are operating normally. Confidence score above 80%.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">🟡 Warning Status</h4>
              <p className="text-sm text-muted-foreground">
                Some issues detected but system is functional. Confidence score 50-80%.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">🔴 Error Status</h4>
              <p className="text-sm text-muted-foreground">
                Critical issues detected. Confidence score below 50%. Immediate attention required.
              </p>
            </div>

            <div className="mt-6">
              <h4 className="font-medium mb-2">Monitored Components</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Webpack:</strong> Module resolution and hot reload health</li>
                <li>• <strong>Extensions:</strong> VS Code extension compatibility</li>
                <li>• <strong>Dependencies:</strong> Critical package availability</li>
                <li>• <strong>API:</strong> Backend connectivity and response</li>
                <li>• <strong>Storage:</strong> localStorage functionality</li>
              </ul>
            </div>

            <div className="mt-6">
              <h4 className="font-medium mb-2">Automatic Actions</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Health checks run every 30 seconds</li>
                <li>• Errors are automatically logged and categorized</li>
                <li>• Safe imports provide fallbacks for failed components</li>
                <li>• Recommendations are generated based on detected issues</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <MetricsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  BarChart3, 
  Target, 
  Lightbulb,
  Zap,
  Eye,
  Activity,
  DollarSign,
  Award,
  Sparkles,
  RefreshCw
} from 'lucide-react';
import { BusinessIntelligenceDashboard } from '@/components/ai/BusinessIntelligenceDashboard';
import {
  useIntelligentAutomation
} from '@/lib/ai/automationEngine';

export default function BusinessIntelligencePage() {
  const {
    getReports,
    getLearningProgress,
    getAutomationRules,
    startAutomation,
    stopAutomation,
    isAutomationRunning
  } = useIntelligentAutomation();

  const [reports, setReports] = useState<any[]>([]);
  const [learningProgress, setLearningProgress] = useState<any[]>([]);
  const [automationRules, setAutomationRules] = useState<any[]>([]);
  const [isAutomationActive, setIsAutomationActive] = useState(false);

  useEffect(() => {
    loadData();
    setIsAutomationActive(isAutomationRunning());
  }, []);

  const loadData = () => {
    setReports(getReports());
    setLearningProgress(getLearningProgress());
    setAutomationRules(getAutomationRules());
  };

  const handleToggleAutomation = () => {
    if (isAutomationActive) {
      stopAutomation();
    } else {
      startAutomation();
    }
    setIsAutomationActive(!isAutomationActive);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'training':
        return 'bg-blue-100 text-blue-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'improving':
        return 'bg-yellow-100 text-yellow-800';
      case 'stable':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Business Intelligence IA</h1>
          <p className="text-muted-foreground">
            Intelligence artificielle intégrée pour optimisation et insights business
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant={isAutomationActive ? "default" : "secondary"} className="px-3 py-1">
            {isAutomationActive ? "🤖 Automatisation Active" : "⏸️ Automatisation Inactive"}
          </Badge>
          <Button 
            onClick={handleToggleAutomation}
            variant={isAutomationActive ? "destructive" : "default"}
          >
            {isAutomationActive ? (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Arrêter
              </>
            ) : (
              <>
                <Activity className="h-4 w-4 mr-2" />
                Démarrer
              </>
            )}
          </Button>
          <Button onClick={loadData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Brain className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{learningProgress.length}</div>
                <div className="text-xs text-gray-600">Modèles IA</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{reports.length}</div>
                <div className="text-xs text-gray-600">Rapports auto</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{automationRules.filter(r => r.isActive).length}</div>
                <div className="text-xs text-gray-600">Règles actives</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">5</div>
                <div className="text-xs text-gray-600">Prédictions</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-2xl font-bold">3</div>
                <div className="text-xs text-gray-600">Anomalies</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">8</div>
                <div className="text-xs text-gray-600">Optimisations</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard">Dashboard BI</TabsTrigger>
          <TabsTrigger value="automation">Automatisation</TabsTrigger>
          <TabsTrigger value="learning">Apprentissage IA</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <BusinessIntelligenceDashboard 
            userRole="manager"
            autoRefresh={true}
            refreshInterval={30000}
          />
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Règles d'automatisation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  <span>Règles d'Automatisation</span>
                </CardTitle>
                <CardDescription>
                  Gestion des règles d'automatisation intelligente
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {automationRules.slice(0, 5).map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{rule.name}</div>
                      <div className="text-xs text-gray-600">{rule.description}</div>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant={rule.isActive ? "default" : "secondary"} className="text-xs">
                          {rule.isActive ? "Actif" : "Inactif"}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          Succès: {(rule.successRate * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold">{rule.executionCount}</div>
                      <div className="text-xs text-gray-600">exécutions</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Rapports automatiques */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  <span>Rapports Automatiques</span>
                </CardTitle>
                <CardDescription>
                  Rapports générés automatiquement par l'IA
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {reports.slice(0, 5).map((report) => (
                  <div key={report.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium text-sm">{report.title}</div>
                      <Badge variant="outline" className="text-xs">
                        {report.type}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      Généré le {report.generatedAt.toLocaleDateString()}
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>Sections: {report.sections.length}</div>
                      <div>KPIs: {Object.keys(report.kpis).length}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="learning" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {learningProgress.map((progress) => (
              <Card key={progress.domain}>
                <CardHeader>
                  <CardTitle className="text-base capitalize flex items-center space-x-2">
                    <Brain className="h-4 w-4" />
                    <span>{progress.domain}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Précision</span>
                      <span>{(progress.accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${progress.accuracy * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <div className="text-gray-600">Cycles</div>
                      <div className="font-bold">{progress.trainingCycles}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Données</div>
                      <div className="font-bold">{progress.dataPoints.toLocaleString()}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className={getStatusColor(progress.status)}>
                      {progress.status}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      Prochain: {progress.nextTraining.toLocaleDateString()}
                    </div>
                  </div>

                  {progress.improvements.length > 0 && (
                    <div>
                      <div className="text-xs font-medium mb-1">Dernière amélioration:</div>
                      <div className="text-xs text-gray-600">
                        {progress.improvements[progress.improvements.length - 1].cause}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

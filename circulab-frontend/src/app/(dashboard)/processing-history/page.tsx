'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, History, Package, Building2, Calendar, Trash2, Edit, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { processingHistoryApi, type ProcessingHistoryResponse, type ProcessingHistory } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { ProcessingHistoryExportButton } from '@/components/ui/export-button';

export default function ProcessingHistoryPage() {
  const [processingHistory, setProcessingHistory] = useState<ProcessingHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [wasteTypeFilter, setWasteTypeFilter] = useState('');
  const [treatmentMethodFilter, setTreatmentMethodFilter] = useState('');
  const [dateFromFilter, setDateFromFilter] = useState('');
  const [dateToFilter, setDateToFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const { toast } = useToast();

  const fetchProcessingHistory = async (page = 1) => {
    try {
      setLoading(true);
      const response = await processingHistoryApi.getAll({
        page,
        limit: 10,
        wasteType: wasteTypeFilter || undefined,
        treatmentMethod: treatmentMethodFilter || undefined,
        dateFrom: dateFromFilter || undefined,
        dateTo: dateToFilter || undefined,
        sortBy: 'dateProcessed',
        sortOrder: 'desc'
      });

      if (response.success && response.data) {
        setProcessingHistory(response.data.processingHistory);
        setTotal(response.data.total);
        setTotalPages(response.data.totalPages);
        setCurrentPage(response.data.page);
      }
    } catch (error) {
      console.error('Error fetching processing history:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch processing history',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProcessingHistory(1);
  }, [wasteTypeFilter, treatmentMethodFilter, dateFromFilter, dateToFilter]);

  const handlePageChange = (page: number) => {
    fetchProcessingHistory(page);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this processing record?')) return;

    try {
      await processingHistoryApi.delete(id);
      toast({
        title: 'Success',
        description: 'Processing record deleted successfully',
      });
      fetchProcessingHistory(currentPage);
    } catch (error) {
      console.error('Error deleting processing record:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete processing record',
        variant: 'destructive',
      });
    }
  };

  const formatQuantity = (quantity: number | null | undefined, unit: string) => {
    if (quantity === null || quantity === undefined) return 'N/A';
    return `${quantity} ${unit}`;
  };

  const calculateEfficiency = (input: number | null | undefined, output: number | null | undefined) => {
    if (!input || !output || input === 0) return null;
    return Math.round((output / input) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Processing History</h1>
          <p className="text-muted-foreground">
            Track waste material processing activities and outcomes
          </p>
        </div>
        <div className="flex items-center gap-2">
          <ProcessingHistoryExportButton history={processingHistory} />
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Record Processing
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Processing Records</CardTitle>
          <CardDescription>
            Filter by waste type, treatment method, and date range
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Input
              placeholder="Waste type..."
              value={wasteTypeFilter}
              onChange={(e) => setWasteTypeFilter(e.target.value)}
            />
            <Input
              placeholder="Treatment method..."
              value={treatmentMethodFilter}
              onChange={(e) => setTreatmentMethodFilter(e.target.value)}
            />
            <Input
              type="date"
              placeholder="From date"
              value={dateFromFilter}
              onChange={(e) => setDateFromFilter(e.target.value)}
            />
            <Input
              type="date"
              placeholder="To date"
              value={dateToFilter}
              onChange={(e) => setDateToFilter(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Processing History List */}
      <div className="grid gap-4">
        {loading ? (
          <div className="text-center py-8">Loading processing history...</div>
        ) : processingHistory.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <History className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No processing records found</h3>
              <p className="text-muted-foreground mb-4">
                {wasteTypeFilter || treatmentMethodFilter || dateFromFilter || dateToFilter 
                  ? 'No records match your filter criteria.' 
                  : 'Get started by recording your first processing activity.'}
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Record Processing
              </Button>
            </CardContent>
          </Card>
        ) : (
          processingHistory.map((record) => (
            <Card key={record.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <History className="h-5 w-5 text-green-600" />
                      <h3 className="text-lg font-semibold">
                        {record.wasteMaterial.type} Processing
                      </h3>
                      <Badge variant="outline">
                        {record.treatmentMethod.name}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Waste Material</p>
                        <p className="text-sm flex items-center gap-1">
                          <Package className="h-3 w-3" />
                          {formatQuantity(record.wasteMaterial.quantity, record.wasteMaterial.unit)}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Processor</p>
                        <p className="text-sm flex items-center gap-1">
                          <Building2 className="h-3 w-3" />
                          {record.processorCompany.name}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Date Processed</p>
                        <p className="text-sm flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(record.dateProcessed).toLocaleDateString()}
                        </p>
                      </div>
                      
                      {record.inputQuantity && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Input Quantity</p>
                          <p className="text-sm">{formatQuantity(record.inputQuantity, record.wasteMaterial.unit)}</p>
                        </div>
                      )}
                      
                      {record.outputQuantity && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Output Quantity</p>
                          <p className="text-sm">{formatQuantity(record.outputQuantity, record.wasteMaterial.unit)}</p>
                        </div>
                      )}
                      
                      {record.inputQuantity && record.outputQuantity && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Efficiency</p>
                          <p className="text-sm">
                            {calculateEfficiency(record.inputQuantity, record.outputQuantity)}%
                          </p>
                        </div>
                      )}
                    </div>

                    {record.outputMaterialType && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-muted-foreground">Output Material Type</p>
                        <p className="text-sm">{record.outputMaterialType}</p>
                      </div>
                    )}

                    {record.notes && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-muted-foreground">Notes</p>
                        <p className="text-sm">{record.notes}</p>
                      </div>
                    )}

                    {record.environmentalImpactAchieved && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-muted-foreground">Environmental Impact</p>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {Object.entries(record.environmentalImpactAchieved as Record<string, any>).map(([key, value]) => (
                            <Badge key={key} variant="secondary" className="text-xs">
                              {key}: {value}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Recorded {new Date(record.createdAt).toLocaleDateString()}</span>
                      {record.processedByUser && (
                        <span>by {record.processedByUser.username || record.processedByUser.email}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDelete(record.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}

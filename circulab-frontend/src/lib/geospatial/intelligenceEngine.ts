// CircuLab Geospatial Intelligence Engine
// Moteur d'intelligence géospatiale pour optimisation des déchets

export interface GeoPoint {
  lat: number;
  lng: number;
  elevation?: number;
}

export interface WasteCollectionPoint {
  id: string;
  name: string;
  position: GeoPoint;
  type: 'container' | 'dumpster' | 'recycling_center' | 'collection_point';
  capacity: {
    current: number;
    maximum: number;
    unit: string;
  };
  wasteTypes: string[];
  lastCollection: Date;
  nextScheduledCollection: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  accessibility: {
    vehicleType: 'small' | 'medium' | 'large';
    timeRestrictions: string[];
    roadConditions: 'excellent' | 'good' | 'fair' | 'poor';
  };
  demographics: {
    populationDensity: number;
    averageIncome: number;
    recyclingRate: number;
    wasteGeneration: number; // kg/person/day
  };
}

export interface OptimizedRoute {
  id: string;
  vehicleId: string;
  points: WasteCollectionPoint[];
  totalDistance: number; // km
  estimatedTime: number; // minutes
  fuelConsumption: number; // liters
  co2Emissions: number; // kg
  efficiency: number; // 0-1
  constraints: {
    maxCapacity: number;
    maxTime: number;
    vehicleType: string;
    timeWindows: Array<{ start: string; end: string }>;
  };
  optimizationMetrics: {
    distanceReduction: number; // %
    timeReduction: number; // %
    fuelSavings: number; // %
    co2Reduction: number; // %
  };
}

export interface HeatmapData {
  position: GeoPoint;
  intensity: number; // 0-1
  wasteType: string;
  volume: number;
  timestamp: Date;
  predictedGrowth: number; // % per month
}

export interface ClusterAnalysis {
  id: string;
  center: GeoPoint;
  radius: number; // meters
  points: WasteCollectionPoint[];
  characteristics: {
    averageCapacity: number;
    averageFillRate: number;
    dominantWasteType: string;
    collectionFrequency: number; // days
  };
  recommendations: {
    optimizeRoutes: boolean;
    addCapacity: boolean;
    changeFrequency: boolean;
    relocatePoints: boolean;
  };
}

export interface PredictionZone {
  id: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  wasteGeneration: {
    current: number; // tonnes/month
    predicted6Months: number;
    predicted12Months: number;
    confidence: number; // 0-1
  };
  factors: {
    populationGrowth: number;
    economicDevelopment: number;
    seasonality: number;
    recyclingPrograms: number;
  };
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

// Algorithmes d'optimisation
class GeospatialOptimizer {
  // Algorithme génétique pour optimisation des routes
  private geneticAlgorithmRouteOptimization(
    points: WasteCollectionPoint[],
    constraints: OptimizedRoute['constraints']
  ): WasteCollectionPoint[] {
    const populationSize = 50;
    const generations = 100;
    const mutationRate = 0.1;
    const crossoverRate = 0.8;

    // Population initiale
    let population = this.generateInitialPopulation(points, populationSize);

    for (let gen = 0; gen < generations; gen++) {
      // Évaluation de la fitness
      const fitness = population.map(route => this.calculateRouteFitness(route, constraints));

      // Sélection
      const selected = this.tournamentSelection(population, fitness, populationSize);

      // Croisement
      const offspring = this.crossover(selected, crossoverRate);

      // Mutation
      const mutated = this.mutate(offspring, mutationRate);

      population = mutated;
    }

    // Retourner la meilleure solution
    const fitness = population.map(route => this.calculateRouteFitness(route, constraints));
    const bestIndex = fitness.indexOf(Math.max(...fitness));
    return population[bestIndex];
  }

  private generateInitialPopulation(points: WasteCollectionPoint[], size: number): WasteCollectionPoint[][] {
    const population: WasteCollectionPoint[][] = [];

    for (let i = 0; i < size; i++) {
      const shuffled = [...points].sort(() => Math.random() - 0.5);
      population.push(shuffled);
    }

    return population;
  }

  private calculateRouteFitness(route: WasteCollectionPoint[], constraints: OptimizedRoute['constraints']): number {
    const distance = this.calculateTotalDistance(route);
    const time = this.calculateTotalTime(route);
    const capacity = this.calculateTotalCapacity(route);

    // Pénalités pour violations de contraintes
    let penalty = 0;
    if (capacity > constraints.maxCapacity) penalty += 1000;
    if (time > constraints.maxTime) penalty += 500;

    // Fitness = 1 / (distance + time + penalty)
    return 1 / (distance + time * 0.1 + penalty);
  }

  private tournamentSelection(population: WasteCollectionPoint[][], fitness: number[], size: number): WasteCollectionPoint[][] {
    const selected: WasteCollectionPoint[][] = [];

    for (let i = 0; i < size; i++) {
      const tournament = [];
      for (let j = 0; j < 3; j++) {
        const index = Math.floor(Math.random() * population.length);
        tournament.push({ route: population[index], fitness: fitness[index] });
      }

      tournament.sort((a, b) => b.fitness - a.fitness);
      selected.push(tournament[0].route);
    }

    return selected;
  }

  private crossover(population: WasteCollectionPoint[][], rate: number): WasteCollectionPoint[][] {
    const offspring: WasteCollectionPoint[][] = [];

    for (let i = 0; i < population.length; i += 2) {
      if (Math.random() < rate && i + 1 < population.length) {
        const [child1, child2] = this.orderCrossover(population[i], population[i + 1]);
        offspring.push(child1, child2);
      } else {
        offspring.push(population[i]);
        if (i + 1 < population.length) offspring.push(population[i + 1]);
      }
    }

    return offspring;
  }

  private orderCrossover(parent1: WasteCollectionPoint[], parent2: WasteCollectionPoint[]): [WasteCollectionPoint[], WasteCollectionPoint[]] {
    const length = parent1.length;
    const start = Math.floor(Math.random() * length);
    const end = Math.floor(Math.random() * (length - start)) + start;

    const child1 = new Array(length);
    const child2 = new Array(length);

    // Copier la section
    for (let i = start; i <= end; i++) {
      child1[i] = parent1[i];
      child2[i] = parent2[i];
    }

    // Remplir le reste
    this.fillRemainingPositions(child1, parent2, start, end);
    this.fillRemainingPositions(child2, parent1, start, end);

    return [child1, child2];
  }

  private fillRemainingPositions(child: WasteCollectionPoint[], parent: WasteCollectionPoint[], start: number, end: number): void {
    const used = new Set(child.slice(start, end + 1).map(p => p?.id).filter(Boolean));
    let parentIndex = 0;

    for (let i = 0; i < child.length; i++) {
      if (i >= start && i <= end) continue;

      while (used.has(parent[parentIndex].id)) {
        parentIndex++;
      }

      child[i] = parent[parentIndex];
      used.add(parent[parentIndex].id);
      parentIndex++;
    }
  }

  private mutate(population: WasteCollectionPoint[][], rate: number): WasteCollectionPoint[][] {
    return population.map(route => {
      if (Math.random() < rate) {
        const newRoute = [...route];
        const i = Math.floor(Math.random() * newRoute.length);
        const j = Math.floor(Math.random() * newRoute.length);
        [newRoute[i], newRoute[j]] = [newRoute[j], newRoute[i]];
        return newRoute;
      }
      return route;
    });
  }

  // Algorithme A* pour pathfinding
  private aStarPathfinding(start: GeoPoint, end: GeoPoint, obstacles: GeoPoint[]): GeoPoint[] {
    // Implémentation simplifiée d'A*
    const openSet: Array<{ point: GeoPoint; f: number; g: number; h: number; parent?: GeoPoint }> = [];
    const closedSet: GeoPoint[] = [];

    const startNode = {
      point: start,
      f: 0,
      g: 0,
      h: this.calculateDistance(start, end),
      parent: undefined
    };

    openSet.push(startNode);

    while (openSet.length > 0) {
      // Trouver le nœud avec le plus petit f
      openSet.sort((a, b) => a.f - b.f);
      const current = openSet.shift()!;

      // Si on a atteint la destination
      if (this.calculateDistance(current.point, end) < 0.001) {
        return this.reconstructPath(current);
      }

      closedSet.push(current.point);

      // Explorer les voisins
      const neighbors = this.getNeighbors(current.point);

      for (const neighbor of neighbors) {
        if (this.isInClosedSet(neighbor, closedSet) || this.isObstacle(neighbor, obstacles)) {
          continue;
        }

        const g = current.g + this.calculateDistance(current.point, neighbor);
        const h = this.calculateDistance(neighbor, end);
        const f = g + h;

        const existingNode = openSet.find(node =>
          this.calculateDistance(node.point, neighbor) < 0.001
        );

        if (!existingNode || g < existingNode.g) {
          const neighborNode = {
            point: neighbor,
            f,
            g,
            h,
            parent: current.point
          };

          if (!existingNode) {
            openSet.push(neighborNode);
          } else {
            Object.assign(existingNode, neighborNode);
          }
        }
      }
    }

    return []; // Pas de chemin trouvé
  }

  private reconstructPath(node: { point: GeoPoint; parent?: GeoPoint }): GeoPoint[] {
    const path: GeoPoint[] = [];
    let current: { point: GeoPoint; parent?: GeoPoint } | undefined = node;

    while (current) {
      path.unshift(current.point);
      // Trouver le parent dans la structure (simplification)
      current = undefined; // Dans une vraie implémentation, on garderait la référence
    }

    return path;
  }

  private getNeighbors(point: GeoPoint): GeoPoint[] {
    const step = 0.001; // ~100m
    return [
      { lat: point.lat + step, lng: point.lng },
      { lat: point.lat - step, lng: point.lng },
      { lat: point.lat, lng: point.lng + step },
      { lat: point.lat, lng: point.lng - step },
      { lat: point.lat + step, lng: point.lng + step },
      { lat: point.lat + step, lng: point.lng - step },
      { lat: point.lat - step, lng: point.lng + step },
      { lat: point.lat - step, lng: point.lng - step }
    ];
  }

  private isInClosedSet(point: GeoPoint, closedSet: GeoPoint[]): boolean {
    return closedSet.some(p => this.calculateDistance(p, point) < 0.001);
  }

  private isObstacle(point: GeoPoint, obstacles: GeoPoint[]): boolean {
    return obstacles.some(obstacle => this.calculateDistance(obstacle, point) < 0.01);
  }

  // Utilitaires de calcul
  private calculateDistance(point1: GeoPoint, point2: GeoPoint): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRadians(point2.lat - point1.lat);
    const dLng = this.toRadians(point2.lng - point1.lng);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private calculateTotalDistance(route: WasteCollectionPoint[]): number {
    let total = 0;
    for (let i = 0; i < route.length - 1; i++) {
      total += this.calculateDistance(route[i].position, route[i + 1].position);
    }
    return total;
  }

  private calculateTotalTime(route: WasteCollectionPoint[]): number {
    const distance = this.calculateTotalDistance(route);
    const averageSpeed = 30; // km/h en ville
    const serviceTime = route.length * 10; // 10 minutes par point
    return (distance / averageSpeed) * 60 + serviceTime; // en minutes
  }

  private calculateTotalCapacity(route: WasteCollectionPoint[]): number {
    return route.reduce((sum, point) => sum + point.capacity.current, 0);
  }

  // Méthodes publiques
  public optimizeRoute(
    points: WasteCollectionPoint[],
    constraints: OptimizedRoute['constraints']
  ): OptimizedRoute {
    const originalDistance = this.calculateTotalDistance(points);
    const originalTime = this.calculateTotalTime(points);

    const optimizedPoints = this.geneticAlgorithmRouteOptimization(points, constraints);

    const optimizedDistance = this.calculateTotalDistance(optimizedPoints);
    const optimizedTime = this.calculateTotalTime(optimizedPoints);

    const fuelConsumption = optimizedDistance * 0.08; // 8L/100km
    const co2Emissions = fuelConsumption * 2.31; // 2.31 kg CO2/L

    return {
      id: `route_${Date.now()}`,
      vehicleId: 'vehicle_001',
      points: optimizedPoints,
      totalDistance: optimizedDistance,
      estimatedTime: optimizedTime,
      fuelConsumption,
      co2Emissions,
      efficiency: 1 - (optimizedDistance / originalDistance),
      constraints,
      optimizationMetrics: {
        distanceReduction: ((originalDistance - optimizedDistance) / originalDistance) * 100,
        timeReduction: ((originalTime - optimizedTime) / originalTime) * 100,
        fuelSavings: ((originalDistance - optimizedDistance) / originalDistance) * 100,
        co2Reduction: ((originalDistance - optimizedDistance) / originalDistance) * 100
      }
    };
  }

  public generateHeatmap(points: WasteCollectionPoint[]): HeatmapData[] {
    return points.map(point => ({
      position: point.position,
      intensity: point.capacity.current / point.capacity.maximum,
      wasteType: point.wasteTypes[0] || 'mixed',
      volume: point.capacity.current,
      timestamp: new Date(),
      predictedGrowth: 2 + Math.random() * 8 // 2-10% growth
    }));
  }

  public performClusterAnalysis(points: WasteCollectionPoint[]): ClusterAnalysis[] {
    // K-means clustering simplifié
    const k = Math.min(5, Math.floor(points.length / 3));
    const clusters: ClusterAnalysis[] = [];

    // Initialiser les centres de clusters
    const centers = points.slice(0, k).map(p => p.position);

    for (let iteration = 0; iteration < 10; iteration++) {
      // Assigner les points aux clusters
      const assignments = points.map(point => {
        let minDistance = Infinity;
        let clusterIndex = 0;

        centers.forEach((center, index) => {
          const distance = this.calculateDistance(point.position, center);
          if (distance < minDistance) {
            minDistance = distance;
            clusterIndex = index;
          }
        });

        return clusterIndex;
      });

      // Recalculer les centres
      for (let i = 0; i < k; i++) {
        const clusterPoints = points.filter((_, index) => assignments[index] === i);
        if (clusterPoints.length > 0) {
          centers[i] = {
            lat: clusterPoints.reduce((sum, p) => sum + p.position.lat, 0) / clusterPoints.length,
            lng: clusterPoints.reduce((sum, p) => sum + p.position.lng, 0) / clusterPoints.length
          };
        }
      }
    }

    // Créer les clusters finaux
    for (let i = 0; i < k; i++) {
      const clusterPoints = points.filter((_, index) => {
        let minDistance = Infinity;
        let clusterIndex = 0;

        centers.forEach((center, centerIndex) => {
          const distance = this.calculateDistance(points[index].position, center);
          if (distance < minDistance) {
            minDistance = distance;
            clusterIndex = centerIndex;
          }
        });

        return clusterIndex === i;
      });

      if (clusterPoints.length > 0) {
        const avgCapacity = clusterPoints.reduce((sum, p) => sum + p.capacity.maximum, 0) / clusterPoints.length;
        const avgFillRate = clusterPoints.reduce((sum, p) => sum + (p.capacity.current / p.capacity.maximum), 0) / clusterPoints.length;

        clusters.push({
          id: `cluster_${i}`,
          center: centers[i],
          radius: Math.max(...clusterPoints.map(p => this.calculateDistance(p.position, centers[i]))) * 1000,
          points: clusterPoints,
          characteristics: {
            averageCapacity: avgCapacity,
            averageFillRate: avgFillRate,
            dominantWasteType: this.findDominantWasteType(clusterPoints),
            collectionFrequency: 7 // Default 7 days
          },
          recommendations: {
            optimizeRoutes: avgFillRate > 0.8,
            addCapacity: avgFillRate > 0.9,
            changeFrequency: avgFillRate < 0.3 || avgFillRate > 0.9,
            relocatePoints: clusterPoints.length < 3
          }
        });
      }
    }

    return clusters;
  }

  private findDominantWasteType(points: WasteCollectionPoint[]): string {
    const typeCount: Record<string, number> = {};

    points.forEach(point => {
      point.wasteTypes.forEach(type => {
        typeCount[type] = (typeCount[type] || 0) + 1;
      });
    });

    return Object.entries(typeCount).reduce((a, b) => typeCount[a[0]] > typeCount[b[0]] ? a : b)[0] || 'mixed';
  }

  public predictWasteGeneration(
    bounds: PredictionZone['bounds'],
    historicalData: WasteCollectionPoint[]
  ): PredictionZone {
    const pointsInZone = historicalData.filter(point =>
      point.position.lat >= bounds.south &&
      point.position.lat <= bounds.north &&
      point.position.lng >= bounds.west &&
      point.position.lng <= bounds.east
    );

    const currentGeneration = pointsInZone.reduce((sum, point) => sum + point.capacity.current, 0) / 1000; // tonnes

    // Facteurs de prédiction
    const populationGrowth = 1.02; // 2% par an
    const economicDevelopment = 1.015; // 1.5% par an
    const seasonality = 1 + 0.1 * Math.sin(Date.now() / (1000 * 60 * 60 * 24 * 365) * 2 * Math.PI); // Variation saisonnière
    const recyclingPrograms = 0.95; // 5% de réduction grâce au recyclage

    const predicted6Months = currentGeneration * Math.pow(populationGrowth, 0.5) * Math.pow(economicDevelopment, 0.5) * seasonality * recyclingPrograms;
    const predicted12Months = currentGeneration * populationGrowth * economicDevelopment * recyclingPrograms;

    return {
      id: `zone_${Date.now()}`,
      bounds,
      wasteGeneration: {
        current: currentGeneration,
        predicted6Months,
        predicted12Months,
        confidence: 0.75 + Math.random() * 0.2 // 75-95%
      },
      factors: {
        populationGrowth: (populationGrowth - 1) * 100,
        economicDevelopment: (economicDevelopment - 1) * 100,
        seasonality: (seasonality - 1) * 100,
        recyclingPrograms: (1 - recyclingPrograms) * 100
      },
      recommendations: [
        predicted12Months > currentGeneration * 1.1 ? 'Augmenter la capacité de collecte' : '',
        predicted12Months < currentGeneration * 0.9 ? 'Optimiser les routes existantes' : '',
        'Implémenter des programmes de réduction à la source',
        'Améliorer les taux de recyclage'
      ].filter(Boolean),
      riskLevel: predicted12Months > currentGeneration * 1.2 ? 'high' :
                 predicted12Months > currentGeneration * 1.1 ? 'medium' : 'low'
    };
  }
}

// Simulateur de données géospatiales
class GeospatialDataSimulator {
  private collectionPoints: WasteCollectionPoint[] = [];
  private optimizer = new GeospatialOptimizer();

  constructor() {
    this.generateMockData();
  }

  private generateMockData(): void {
    // Génération de points de collecte autour de Paris
    const parisCenter = { lat: 48.8566, lng: 2.3522 };
    const radius = 0.1; // ~10km

    for (let i = 0; i < 50; i++) {
      const angle = Math.random() * 2 * Math.PI;
      const distance = Math.random() * radius;

      const lat = parisCenter.lat + distance * Math.cos(angle);
      const lng = parisCenter.lng + distance * Math.sin(angle);

      const wasteTypes = ['plastic', 'paper', 'glass', 'metal', 'organic'];
      const selectedTypes = wasteTypes.filter(() => Math.random() > 0.5);

      this.collectionPoints.push({
        id: `point_${i.toString().padStart(3, '0')}`,
        name: `Point de collecte ${i + 1}`,
        position: { lat, lng },
        type: ['container', 'dumpster', 'recycling_center', 'collection_point'][Math.floor(Math.random() * 4)] as any,
        capacity: {
          current: Math.random() * 1000,
          maximum: 500 + Math.random() * 1000,
          unit: 'kg'
        },
        wasteTypes: selectedTypes.length > 0 ? selectedTypes : ['mixed'],
        lastCollection: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        nextScheduledCollection: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000),
        priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        accessibility: {
          vehicleType: ['small', 'medium', 'large'][Math.floor(Math.random() * 3)] as any,
          timeRestrictions: Math.random() > 0.7 ? ['06:00-22:00'] : [],
          roadConditions: ['excellent', 'good', 'fair', 'poor'][Math.floor(Math.random() * 4)] as any
        },
        demographics: {
          populationDensity: 1000 + Math.random() * 5000,
          averageIncome: 25000 + Math.random() * 50000,
          recyclingRate: 0.3 + Math.random() * 0.4,
          wasteGeneration: 1 + Math.random() * 2
        }
      });
    }
  }

  // Méthodes publiques
  public getCollectionPoints(): WasteCollectionPoint[] {
    return this.collectionPoints;
  }

  public optimizeRoutes(constraints: OptimizedRoute['constraints']): OptimizedRoute[] {
    // Diviser les points en groupes pour différents véhicules
    const pointsPerRoute = 10;
    const routes: OptimizedRoute[] = [];

    for (let i = 0; i < this.collectionPoints.length; i += pointsPerRoute) {
      const routePoints = this.collectionPoints.slice(i, i + pointsPerRoute);
      if (routePoints.length > 0) {
        const optimizedRoute = this.optimizer.optimizeRoute(routePoints, constraints);
        optimizedRoute.vehicleId = `vehicle_${Math.floor(i / pointsPerRoute) + 1}`;
        routes.push(optimizedRoute);
      }
    }

    return routes;
  }

  public generateHeatmapData(): HeatmapData[] {
    return this.optimizer.generateHeatmap(this.collectionPoints);
  }

  public performClusterAnalysis(): ClusterAnalysis[] {
    return this.optimizer.performClusterAnalysis(this.collectionPoints);
  }

  public generatePredictionZones(): PredictionZone[] {
    const zones: PredictionZone[] = [];
    const parisCenter = { lat: 48.8566, lng: 2.3522 };
    const zoneSize = 0.02; // ~2km

    for (let i = -2; i <= 2; i++) {
      for (let j = -2; j <= 2; j++) {
        const bounds = {
          north: parisCenter.lat + (i + 1) * zoneSize,
          south: parisCenter.lat + i * zoneSize,
          east: parisCenter.lng + (j + 1) * zoneSize,
          west: parisCenter.lng + j * zoneSize
        };

        const zone = this.optimizer.predictWasteGeneration(bounds, this.collectionPoints);
        zones.push(zone);
      }
    }

    return zones;
  }

  public updateCollectionPoint(pointId: string, updates: Partial<WasteCollectionPoint>): void {
    const index = this.collectionPoints.findIndex(p => p.id === pointId);
    if (index !== -1) {
      this.collectionPoints[index] = { ...this.collectionPoints[index], ...updates };
    }
  }

  public addCollectionPoint(point: Omit<WasteCollectionPoint, 'id'>): WasteCollectionPoint {
    const newPoint: WasteCollectionPoint = {
      id: `point_${Date.now()}`,
      ...point
    };
    this.collectionPoints.push(newPoint);
    return newPoint;
  }

  public removeCollectionPoint(pointId: string): boolean {
    const index = this.collectionPoints.findIndex(p => p.id === pointId);
    if (index !== -1) {
      this.collectionPoints.splice(index, 1);
      return true;
    }
    return false;
  }

  public getStatistics() {
    const totalCapacity = this.collectionPoints.reduce((sum, p) => sum + p.capacity.maximum, 0);
    const currentLoad = this.collectionPoints.reduce((sum, p) => sum + p.capacity.current, 0);
    const averageFillRate = currentLoad / totalCapacity;

    const priorityCounts = this.collectionPoints.reduce((counts, point) => {
      counts[point.priority] = (counts[point.priority] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    const wasteTypeCounts = this.collectionPoints.reduce((counts, point) => {
      point.wasteTypes.forEach(type => {
        counts[type] = (counts[type] || 0) + 1;
      });
      return counts;
    }, {} as Record<string, number>);

    return {
      totalPoints: this.collectionPoints.length,
      totalCapacity,
      currentLoad,
      averageFillRate,
      priorityCounts,
      wasteTypeCounts,
      averageRecyclingRate: this.collectionPoints.reduce((sum, p) => sum + p.demographics.recyclingRate, 0) / this.collectionPoints.length
    };
  }
}

// Instance singleton du simulateur géospatial
export const geospatialSimulator = new GeospatialDataSimulator();

// Hook React pour utiliser l'intelligence géospatiale
export function useGeospatialIntelligence() {
  return {
    getCollectionPoints: () => geospatialSimulator.getCollectionPoints(),
    optimizeRoutes: (constraints: OptimizedRoute['constraints']) => geospatialSimulator.optimizeRoutes(constraints),
    generateHeatmapData: () => geospatialSimulator.generateHeatmapData(),
    performClusterAnalysis: () => geospatialSimulator.performClusterAnalysis(),
    generatePredictionZones: () => geospatialSimulator.generatePredictionZones(),
    updateCollectionPoint: (pointId: string, updates: Partial<WasteCollectionPoint>) =>
      geospatialSimulator.updateCollectionPoint(pointId, updates),
    addCollectionPoint: (point: Omit<WasteCollectionPoint, 'id'>) =>
      geospatialSimulator.addCollectionPoint(point),
    removeCollectionPoint: (pointId: string) => geospatialSimulator.removeCollectionPoint(pointId),
    getStatistics: () => geospatialSimulator.getStatistics()
  };
}
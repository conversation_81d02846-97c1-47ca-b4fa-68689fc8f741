"use client";

import dynamic from 'next/dynamic';
import React, { ComponentType, ReactNode } from 'react';

// Types pour les imports sécurisés
interface SafeImportOptions {
  loading?: () => ReactNode;
  ssr?: boolean;
  onError?: (error: Error) => void;
  fallback?: ComponentType<any>;
  retryCount?: number;
  timeout?: number;
}

interface ComponentSkeleton {
  height?: string;
  width?: string;
  className?: string;
}

// Composant de skeleton générique
export function ComponentSkeleton({ 
  height = "h-20", 
  width = "w-full", 
  className = "" 
}: ComponentSkeleton) {
  return (
    <div className={`animate-pulse bg-gray-200 rounded ${height} ${width} ${className}`}>
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-400 text-sm">Loading...</div>
      </div>
    </div>
  );
}

// Fallback d'erreur intelligent
export function ErrorFallback({ 
  error, 
  componentName, 
  retry 
}: { 
  error: Error; 
  componentName: string; 
  retry?: () => void; 
}) {
  return (
    <div className="border border-red-200 bg-red-50 rounded-lg p-4 m-2">
      <div className="flex items-center mb-2">
        <svg className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 className="text-sm font-medium text-red-800">
          Component Failed: {componentName}
        </h3>
      </div>
      
      <p className="text-sm text-red-700 mb-3">
        {error.message || 'Unknown error occurred'}
      </p>
      
      {retry && (
        <button
          onClick={retry}
          className="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
        >
          Retry Loading
        </button>
      )}
      
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-2">
          <summary className="text-xs text-red-600 cursor-pointer">Technical Details</summary>
          <pre className="text-xs text-red-600 mt-1 overflow-auto">
            {error.stack}
          </pre>
        </details>
      )}
    </div>
  );
}

// Logger d'erreurs centralisé
class ErrorLogger {
  private static instance: ErrorLogger;
  private errors: Array<{ timestamp: Date; error: Error; context: string }> = [];

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  log(error: Error, context: string) {
    const errorEntry = {
      timestamp: new Date(),
      error,
      context
    };
    
    this.errors.push(errorEntry);
    
    // Garder seulement les 100 dernières erreurs
    if (this.errors.length > 100) {
      this.errors = this.errors.slice(-100);
    }

    // Log en console en développement
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Safe Import Error: ${context}`);
      console.error('Error:', error);
      console.error('Stack:', error.stack);
      console.error('Timestamp:', errorEntry.timestamp.toISOString());
      console.groupEnd();
    }

    // Ici vous pourriez envoyer à un service de monitoring
    // comme Sentry, LogRocket, etc.
  }

  getErrors() {
    return [...this.errors];
  }

  clearErrors() {
    this.errors = [];
  }
}

// Fonction principale pour créer des imports sécurisés
export function createSafeImport<T = any>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  componentName: string,
  options: SafeImportOptions = {}
) {
  const {
    loading = () => <ComponentSkeleton />,
    ssr = false,
    onError,
    fallback: CustomFallback,
    retryCount = 3,
    timeout = 10000
  } = options;

  let retryAttempts = 0;
  const logger = ErrorLogger.getInstance();

  const SafeComponent = dynamic(
    async () => {
      try {
        // Timeout pour l'import
        const importPromise = importFn();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`Import timeout for ${componentName}`)), timeout);
        });

        const result = await Promise.race([importPromise, timeoutPromise]) as { default: ComponentType<T> };
        
        // Reset retry count on success
        retryAttempts = 0;
        
        return result;
      } catch (error) {
        const err = error as Error;
        logger.log(err, `Safe import failed for ${componentName}`);
        
        if (onError) {
          onError(err);
        }

        // Retry logic
        if (retryAttempts < retryCount) {
          retryAttempts++;
          console.warn(`Retrying import for ${componentName} (attempt ${retryAttempts}/${retryCount})`);
          
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryAttempts) * 1000));
          
          return importFn();
        }

        // Si on a un fallback personnalisé, l'utiliser
        if (CustomFallback) {
          return { default: CustomFallback };
        }

        // Sinon, retourner un composant d'erreur
        return {
          default: (props: T) => (
            <ErrorFallback 
              error={err} 
              componentName={componentName}
              retry={() => window.location.reload()}
            />
          )
        };
      }
    },
    {
      loading,
      ssr
    }
  );

  return SafeComponent;
}

// Hooks utilitaires
export function useErrorLogger() {
  return ErrorLogger.getInstance();
}

// Exports de composants sécurisés pré-configurés
export const SafeAuthProvider = createSafeImport(
  () => import('@/components/providers/AuthProvider'),
  'AuthProvider',
  {
    loading: () => <ComponentSkeleton height="h-screen" />,
    ssr: false
  }
);

export const SafeDashboardContent = createSafeImport(
  () => import('@/components/dashboard/DashboardContent'),
  'DashboardContent',
  {
    loading: () => <ComponentSkeleton height="h-96" />
  }
);

// export const SafeExportButton = createSafeImport(
//   () => import('@/components/ui/export-button').then(mod => ({ default: mod.ExportButton })),
//   'ExportButton',
//   {
//     loading: () => <ComponentSkeleton height="h-10" width="w-24" />
//   }
// );

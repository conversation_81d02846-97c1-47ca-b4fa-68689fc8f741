// CircuLab Blockchain Traceability System
// Système de traçabilité blockchain pour les déchets

// Fonction de hash compatible navigateur/Node.js
function createSHA256Hash(data: string): string {
  if (typeof window !== 'undefined') {
    // Version navigateur - simulation simple
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  } else {
    // Version Node.js - simulation pour éviter require()
    // En production, utiliser une vraie librairie crypto
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16).padStart(64, '0');
  }
}

export interface WasteTransaction {
  id: string;
  wasteId: string;
  type: 'collection' | 'transport' | 'processing' | 'recycling' | 'disposal' | 'certification';
  timestamp: Date;
  location: {
    name: string;
    coordinates: { lat: number; lng: number };
    address: string;
  };
  actor: {
    id: string;
    name: string;
    type: 'collector' | 'transporter' | 'processor' | 'recycler' | 'certifier';
    certification: string;
  };
  data: {
    quantity: number;
    quality: number;
    composition?: Record<string, number>;
    treatment?: string;
    destination?: string;
    certificates?: string[];
  };
  metadata: {
    temperature?: number;
    humidity?: number;
    photos?: string[];
    documents?: string[];
    signatures?: string[];
  };
  previousHash: string;
  hash: string;
  nonce: number;
  verified: boolean;
}

export interface Block {
  index: number;
  timestamp: Date;
  transactions: WasteTransaction[];
  previousHash: string;
  hash: string;
  nonce: number;
  merkleRoot: string;
  validator: string;
}

export interface SmartContract {
  id: string;
  name: string;
  type: 'recycling_certificate' | 'carbon_credit' | 'quality_guarantee' | 'payment_escrow';
  wasteId: string;
  conditions: Record<string, any>;
  status: 'pending' | 'active' | 'completed' | 'failed';
  createdAt: Date;
  executedAt?: Date;
  result?: any;
}

export interface TraceabilityReport {
  wasteId: string;
  totalTransactions: number;
  journey: WasteTransaction[];
  carbonFootprint: number;
  recyclingRate: number;
  certifications: string[];
  smartContracts: SmartContract[];
  verificationScore: number;
  sustainability: {
    score: number;
    factors: Record<string, number>;
  };
}

// Simulateur de blockchain pour la traçabilité
class TraceabilityBlockchain {
  private chain: Block[] = [];
  private pendingTransactions: WasteTransaction[] = [];
  private smartContracts: Map<string, SmartContract> = new Map();
  private difficulty = 2; // Difficulté de minage simplifiée
  private miningReward = 10;
  private validators = ['CircuLab_Validator_1', 'CircuLab_Validator_2', 'CircuLab_Validator_3'];

  constructor() {
    this.createGenesisBlock();
  }

  private createGenesisBlock(): void {
    const genesisBlock: Block = {
      index: 0,
      timestamp: new Date('2024-01-01'),
      transactions: [],
      previousHash: '0',
      hash: this.calculateHash(0, new Date('2024-01-01'), [], '0', 0),
      nonce: 0,
      merkleRoot: '0',
      validator: 'CircuLab_Genesis'
    };
    this.chain.push(genesisBlock);
  }

  // Créer une nouvelle transaction
  createTransaction(transactionData: Omit<WasteTransaction, 'id' | 'hash' | 'previousHash' | 'nonce' | 'verified'>): WasteTransaction {
    const previousTransaction = this.getLastTransactionForWaste(transactionData.wasteId);
    const previousHash = previousTransaction ? previousTransaction.hash : '0';

    const transaction: WasteTransaction = {
      id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...transactionData,
      previousHash,
      hash: '',
      nonce: 0,
      verified: false
    };

    // Calculer le hash de la transaction
    transaction.hash = this.calculateTransactionHash(transaction);
    
    // Vérification automatique (simulation)
    transaction.verified = this.verifyTransaction(transaction);

    this.pendingTransactions.push(transaction);
    
    // Vérifier et exécuter les smart contracts
    this.checkSmartContracts(transaction);

    return transaction;
  }

  private calculateTransactionHash(transaction: WasteTransaction): string {
    const data = `${transaction.wasteId}${transaction.type}${transaction.timestamp.toISOString()}${JSON.stringify(transaction.data)}${transaction.previousHash}${transaction.nonce}`;
    return createSHA256Hash(data);
  }

  private verifyTransaction(transaction: WasteTransaction): boolean {
    // Simulation de vérification
    // En réalité, cela inclurait la vérification des signatures, certificats, etc.
    
    // Vérifier la cohérence des données
    if (transaction.data.quantity <= 0) return false;
    if (transaction.data.quality < 0 || transaction.data.quality > 1) return false;
    
    // Vérifier la chaîne de hash
    const expectedHash = this.calculateTransactionHash(transaction);
    if (transaction.hash !== expectedHash) return false;

    // Vérifier l'acteur
    if (!transaction.actor.certification) return false;

    return true;
  }

  // Miner un nouveau bloc
  mineBlock(): Block | null {
    if (this.pendingTransactions.length === 0) return null;

    const index = this.chain.length;
    const timestamp = new Date();
    const previousHash = this.getLatestBlock().hash;
    const transactions = [...this.pendingTransactions];
    const validator = this.validators[Math.floor(Math.random() * this.validators.length)];
    
    // Calculer le Merkle Root
    const merkleRoot = this.calculateMerkleRoot(transactions);

    let nonce = 0;
    let hash = '';

    // Proof of Work simplifié
    do {
      nonce++;
      hash = this.calculateHash(index, timestamp, transactions, previousHash, nonce);
    } while (!hash.startsWith('0'.repeat(this.difficulty)));

    const newBlock: Block = {
      index,
      timestamp,
      transactions,
      previousHash,
      hash,
      nonce,
      merkleRoot,
      validator
    };

    this.chain.push(newBlock);
    this.pendingTransactions = [];

    console.log(`⛏️ Nouveau bloc miné: ${newBlock.hash.substring(0, 10)}... par ${validator}`);
    return newBlock;
  }

  private calculateHash(index: number, timestamp: Date, transactions: WasteTransaction[], previousHash: string, nonce: number): string {
    const data = `${index}${timestamp.toISOString()}${JSON.stringify(transactions)}${previousHash}${nonce}`;
    return createSHA256Hash(data);
  }

  private calculateMerkleRoot(transactions: WasteTransaction[]): string {
    if (transactions.length === 0) return '0';
    
    let hashes = transactions.map(tx => tx.hash);
    
    while (hashes.length > 1) {
      const newHashes: string[] = [];
      for (let i = 0; i < hashes.length; i += 2) {
        const left = hashes[i];
        const right = hashes[i + 1] || left;
        const combined = createSHA256Hash(left + right);
        newHashes.push(combined);
      }
      hashes = newHashes;
    }
    
    return hashes[0];
  }

  // Smart Contracts
  createSmartContract(contractData: Omit<SmartContract, 'id' | 'status' | 'createdAt'>): SmartContract {
    const contract: SmartContract = {
      id: `contract_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'pending',
      createdAt: new Date(),
      ...contractData
    };

    this.smartContracts.set(contract.id, contract);
    return contract;
  }

  private checkSmartContracts(transaction: WasteTransaction): void {
    this.smartContracts.forEach(contract => {
      if (contract.wasteId === transaction.wasteId && contract.status === 'pending') {
        this.executeSmartContract(contract, transaction);
      }
    });
  }

  private executeSmartContract(contract: SmartContract, trigger: WasteTransaction): void {
    let shouldExecute = false;
    let result: any = null;

    switch (contract.type) {
      case 'recycling_certificate':
        if (trigger.type === 'recycling' && trigger.data.quality >= contract.conditions.minQuality) {
          shouldExecute = true;
          result = {
            certificateId: `CERT_${Date.now()}`,
            recyclingRate: trigger.data.quality,
            issuedBy: trigger.actor.name,
            validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 an
          };
        }
        break;

      case 'carbon_credit':
        if (trigger.type === 'recycling') {
          const carbonSaved = this.calculateCarbonSavings(trigger);
          if (carbonSaved >= contract.conditions.minCarbonSavings) {
            shouldExecute = true;
            result = {
              creditId: `CARBON_${Date.now()}`,
              carbonSaved,
              creditValue: carbonSaved * 25, // 25€ par tonne CO2
              issuedBy: 'CircuLab_Carbon_Authority'
            };
          }
        }
        break;

      case 'quality_guarantee':
        if (trigger.data.quality >= contract.conditions.guaranteedQuality) {
          shouldExecute = true;
          result = {
            guaranteeId: `QUAL_${Date.now()}`,
            qualityLevel: trigger.data.quality,
            guarantor: trigger.actor.name,
            coverage: contract.conditions.coverageAmount
          };
        }
        break;

      case 'payment_escrow':
        if (trigger.type === 'processing' && trigger.verified) {
          shouldExecute = true;
          result = {
            paymentId: `PAY_${Date.now()}`,
            amount: contract.conditions.amount,
            recipient: trigger.actor.id,
            releaseCondition: 'processing_completed'
          };
        }
        break;
    }

    if (shouldExecute) {
      contract.status = 'completed';
      contract.executedAt = new Date();
      contract.result = result;
      console.log(`📜 Smart contract exécuté: ${contract.type} pour ${contract.wasteId}`);
    }
  }

  private calculateCarbonSavings(transaction: WasteTransaction): number {
    // Simulation du calcul d'économies carbone
    const carbonFactors: Record<string, number> = {
      'plastic': 2.1, // kg CO2 évités par kg recyclé
      'paper': 1.5,
      'metal': 3.2,
      'glass': 0.8,
      'organic': 0.5
    };

    let totalSavings = 0;
    if (transaction.data.composition) {
      Object.entries(transaction.data.composition).forEach(([material, percentage]) => {
        const factor = carbonFactors[material] || 1.0;
        totalSavings += transaction.data.quantity * percentage * factor;
      });
    } else {
      // Facteur par défaut
      totalSavings = transaction.data.quantity * 1.5;
    }

    return Math.round(totalSavings * 100) / 100;
  }

  // Générer un rapport de traçabilité
  generateTraceabilityReport(wasteId: string): TraceabilityReport {
    const journey = this.getWasteJourney(wasteId);
    const contracts = Array.from(this.smartContracts.values()).filter(c => c.wasteId === wasteId);
    
    const carbonFootprint = this.calculateTotalCarbonFootprint(journey);
    const recyclingRate = this.calculateRecyclingRate(journey);
    const certifications = this.extractCertifications(contracts);
    const verificationScore = this.calculateVerificationScore(journey);
    const sustainability = this.calculateSustainabilityScore(journey, contracts);

    return {
      wasteId,
      totalTransactions: journey.length,
      journey,
      carbonFootprint,
      recyclingRate,
      certifications,
      smartContracts: contracts,
      verificationScore,
      sustainability
    };
  }

  private getWasteJourney(wasteId: string): WasteTransaction[] {
    const allTransactions: WasteTransaction[] = [];
    
    // Récupérer toutes les transactions de tous les blocs
    this.chain.forEach(block => {
      block.transactions.forEach(tx => {
        if (tx.wasteId === wasteId) {
          allTransactions.push(tx);
        }
      });
    });

    // Ajouter les transactions en attente
    this.pendingTransactions.forEach(tx => {
      if (tx.wasteId === wasteId) {
        allTransactions.push(tx);
      }
    });

    // Trier par timestamp
    return allTransactions.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  private calculateTotalCarbonFootprint(journey: WasteTransaction[]): number {
    return journey.reduce((total, tx) => {
      const savings = this.calculateCarbonSavings(tx);
      return total + (tx.type === 'recycling' ? -savings : savings * 0.1);
    }, 0);
  }

  private calculateRecyclingRate(journey: WasteTransaction[]): number {
    const totalQuantity = journey.reduce((sum, tx) => sum + tx.data.quantity, 0);
    const recycledQuantity = journey
      .filter(tx => tx.type === 'recycling')
      .reduce((sum, tx) => sum + tx.data.quantity, 0);
    
    return totalQuantity > 0 ? (recycledQuantity / totalQuantity) * 100 : 0;
  }

  private extractCertifications(contracts: SmartContract[]): string[] {
    return contracts
      .filter(c => c.status === 'completed' && c.result)
      .map(c => c.result.certificateId || c.result.creditId || c.result.guaranteeId)
      .filter(Boolean);
  }

  private calculateVerificationScore(journey: WasteTransaction[]): number {
    if (journey.length === 0) return 0;
    
    const verifiedCount = journey.filter(tx => tx.verified).length;
    return (verifiedCount / journey.length) * 100;
  }

  private calculateSustainabilityScore(journey: WasteTransaction[], contracts: SmartContract[]): { score: number; factors: Record<string, number> } {
    const factors = {
      traceability: this.calculateVerificationScore(journey),
      recycling: this.calculateRecyclingRate(journey),
      carbonImpact: Math.max(0, 100 - Math.abs(this.calculateTotalCarbonFootprint(journey))),
      certifications: (contracts.filter(c => c.status === 'completed').length / Math.max(1, contracts.length)) * 100,
      transparency: journey.length > 0 ? 100 : 0
    };

    const score = Object.values(factors).reduce((sum, value) => sum + value, 0) / Object.keys(factors).length;

    return {
      score: Math.round(score),
      factors
    };
  }

  // Méthodes utilitaires
  getLatestBlock(): Block {
    return this.chain[this.chain.length - 1];
  }

  getLastTransactionForWaste(wasteId: string): WasteTransaction | null {
    // Chercher dans l'ordre inverse pour trouver la dernière transaction
    for (let i = this.chain.length - 1; i >= 0; i--) {
      const block = this.chain[i];
      for (let j = block.transactions.length - 1; j >= 0; j--) {
        if (block.transactions[j].wasteId === wasteId) {
          return block.transactions[j];
        }
      }
    }
    return null;
  }

  getChainLength(): number {
    return this.chain.length;
  }

  getPendingTransactionsCount(): number {
    return this.pendingTransactions.length;
  }

  getSmartContracts(): SmartContract[] {
    return Array.from(this.smartContracts.values());
  }

  // Validation de la chaîne
  isChainValid(): boolean {
    for (let i = 1; i < this.chain.length; i++) {
      const currentBlock = this.chain[i];
      const previousBlock = this.chain[i - 1];

      // Vérifier le hash du bloc
      const calculatedHash = this.calculateHash(
        currentBlock.index,
        currentBlock.timestamp,
        currentBlock.transactions,
        currentBlock.previousHash,
        currentBlock.nonce
      );

      if (currentBlock.hash !== calculatedHash) {
        return false;
      }

      // Vérifier le lien avec le bloc précédent
      if (currentBlock.previousHash !== previousBlock.hash) {
        return false;
      }

      // Vérifier la difficulté
      if (!currentBlock.hash.startsWith('0'.repeat(this.difficulty))) {
        return false;
      }
    }

    return true;
  }
}

// Instance singleton de la blockchain
export const traceabilityChain = new TraceabilityBlockchain();

// Hook React pour utiliser la blockchain
export function useTraceabilityBlockchain() {
  return {
    createTransaction: (data: Omit<WasteTransaction, 'id' | 'hash' | 'previousHash' | 'nonce' | 'verified'>) => 
      traceabilityChain.createTransaction(data),
    mineBlock: () => traceabilityChain.mineBlock(),
    createSmartContract: (data: Omit<SmartContract, 'id' | 'status' | 'createdAt'>) => 
      traceabilityChain.createSmartContract(data),
    generateReport: (wasteId: string) => traceabilityChain.generateTraceabilityReport(wasteId),
    getChainLength: () => traceabilityChain.getChainLength(),
    getPendingTransactionsCount: () => traceabilityChain.getPendingTransactionsCount(),
    getSmartContracts: () => traceabilityChain.getSmartContracts(),
    isChainValid: () => traceabilityChain.isChainValid()
  };
}

// CircuLab IoT Sensor Simulator
// Simulation avancée de capteurs IoT pour monitoring des déchets

export interface SensorReading {
  id: string;
  sensorId: string;
  timestamp: Date;
  value: number;
  unit: string;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  batteryLevel?: number;
  signalStrength?: number;
}

export interface SensorConfig {
  id: string;
  name: string;
  type: 'temperature' | 'humidity' | 'level' | 'weight' | 'gas' | 'ph' | 'pressure' | 'vibration';
  location: string;
  coordinates: { lat: number; lng: number };
  unit: string;
  minValue: number;
  maxValue: number;
  normalRange: { min: number; max: number };
  alertThresholds: { low: number; high: number };
  samplingInterval: number; // en secondes
  isActive: boolean;
  lastMaintenance: Date;
  batteryLevel: number;
  signalStrength: number;
}

export interface Alert {
  id: string;
  sensorId: string;
  type: 'threshold_exceeded' | 'sensor_offline' | 'low_battery' | 'maintenance_required';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
}

export interface IoTDashboardData {
  totalSensors: number;
  activeSensors: number;
  alertsCount: number;
  averageBatteryLevel: number;
  dataPointsToday: number;
  systemHealth: number;
}

// Simulateur de capteurs IoT
class IoTSensorSimulator {
  private sensors: Map<string, SensorConfig> = new Map();
  private readings: Map<string, SensorReading[]> = new Map();
  private alerts: Alert[] = [];
  private isRunning = false;
  private intervals: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.initializeDefaultSensors();
  }

  private initializeDefaultSensors(): void {
    const defaultSensors: SensorConfig[] = [
      {
        id: 'temp_001',
        name: 'Température Composteur A',
        type: 'temperature',
        location: 'Site de compostage - Zone A',
        coordinates: { lat: 48.8566, lng: 2.3522 },
        unit: '°C',
        minValue: -10,
        maxValue: 80,
        normalRange: { min: 45, max: 65 },
        alertThresholds: { low: 35, high: 75 },
        samplingInterval: 300, // 5 minutes
        isActive: true,
        lastMaintenance: new Date('2024-01-15'),
        batteryLevel: 85,
        signalStrength: 92
      },
      {
        id: 'hum_001',
        name: 'Humidité Composteur A',
        type: 'humidity',
        location: 'Site de compostage - Zone A',
        coordinates: { lat: 48.8566, lng: 2.3522 },
        unit: '%',
        minValue: 0,
        maxValue: 100,
        normalRange: { min: 50, max: 70 },
        alertThresholds: { low: 40, high: 80 },
        samplingInterval: 300,
        isActive: true,
        lastMaintenance: new Date('2024-01-15'),
        batteryLevel: 78,
        signalStrength: 89
      },
      {
        id: 'level_001',
        name: 'Niveau Benne Plastique',
        type: 'level',
        location: 'Centre de tri - Benne 1',
        coordinates: { lat: 48.8606, lng: 2.3376 },
        unit: '%',
        minValue: 0,
        maxValue: 100,
        normalRange: { min: 0, max: 85 },
        alertThresholds: { low: 10, high: 90 },
        samplingInterval: 600, // 10 minutes
        isActive: true,
        lastMaintenance: new Date('2024-01-10'),
        batteryLevel: 92,
        signalStrength: 95
      },
      {
        id: 'weight_001',
        name: 'Poids Benne Métaux',
        type: 'weight',
        location: 'Centre de tri - Benne 2',
        coordinates: { lat: 48.8606, lng: 2.3376 },
        unit: 'kg',
        minValue: 0,
        maxValue: 5000,
        normalRange: { min: 0, max: 4500 },
        alertThresholds: { low: 100, high: 4800 },
        samplingInterval: 900, // 15 minutes
        isActive: true,
        lastMaintenance: new Date('2024-01-12'),
        batteryLevel: 67,
        signalStrength: 88
      },
      {
        id: 'gas_001',
        name: 'Méthane Décharge',
        type: 'gas',
        location: 'Décharge - Zone de surveillance',
        coordinates: { lat: 48.8496, lng: 2.3712 },
        unit: 'ppm',
        minValue: 0,
        maxValue: 1000,
        normalRange: { min: 0, max: 50 },
        alertThresholds: { low: 0, high: 100 },
        samplingInterval: 180, // 3 minutes
        isActive: true,
        lastMaintenance: new Date('2024-01-08'),
        batteryLevel: 45,
        signalStrength: 76
      },
      {
        id: 'ph_001',
        name: 'pH Lixiviats',
        type: 'ph',
        location: 'Station de traitement - Bassin 1',
        coordinates: { lat: 48.8396, lng: 2.3812 },
        unit: 'pH',
        minValue: 0,
        maxValue: 14,
        normalRange: { min: 6.5, max: 8.5 },
        alertThresholds: { low: 6, high: 9 },
        samplingInterval: 1800, // 30 minutes
        isActive: true,
        lastMaintenance: new Date('2024-01-20'),
        batteryLevel: 88,
        signalStrength: 91
      }
    ];

    defaultSensors.forEach(sensor => {
      this.sensors.set(sensor.id, sensor);
      this.readings.set(sensor.id, []);
    });
  }

  // Démarrer la simulation
  startSimulation(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('🚀 Démarrage de la simulation IoT...');

    this.sensors.forEach((sensor, sensorId) => {
      if (sensor.isActive) {
        this.startSensorSimulation(sensorId);
      }
    });

    // Simulation de dégradation de batterie et maintenance
    setInterval(() => {
      this.simulateBatteryDrain();
      this.checkMaintenanceNeeds();
    }, 60000); // Chaque minute
  }

  // Arrêter la simulation
  stopSimulation(): void {
    this.isRunning = false;
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
    console.log('⏹️ Simulation IoT arrêtée');
  }

  private startSensorSimulation(sensorId: string): void {
    const sensor = this.sensors.get(sensorId);
    if (!sensor) return;

    const interval = setInterval(() => {
      const reading = this.generateSensorReading(sensor);
      this.addReading(sensorId, reading);
      this.checkAlerts(sensor, reading);
    }, sensor.samplingInterval * 1000);

    this.intervals.set(sensorId, interval);
  }

  private generateSensorReading(sensor: SensorConfig): SensorReading {
    let value: number;
    let quality: SensorReading['quality'] = 'excellent';

    // Simulation de valeurs réalistes selon le type de capteur
    switch (sensor.type) {
      case 'temperature':
        value = this.generateTemperatureReading(sensor);
        break;
      case 'humidity':
        value = this.generateHumidityReading(sensor);
        break;
      case 'level':
        value = this.generateLevelReading(sensor);
        break;
      case 'weight':
        value = this.generateWeightReading(sensor);
        break;
      case 'gas':
        value = this.generateGasReading(sensor);
        break;
      case 'ph':
        value = this.generatePHReading(sensor);
        break;
      default:
        value = sensor.normalRange.min + Math.random() * (sensor.normalRange.max - sensor.normalRange.min);
    }

    // Simulation de la qualité du signal
    if (sensor.signalStrength < 50) quality = 'poor';
    else if (sensor.signalStrength < 70) quality = 'fair';
    else if (sensor.signalStrength < 85) quality = 'good';

    // Ajout de bruit selon la qualité
    const noise = this.addNoise(value, quality);
    value = Math.max(sensor.minValue, Math.min(sensor.maxValue, value + noise));

    return {
      id: `reading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sensorId: sensor.id,
      timestamp: new Date(),
      value: Math.round(value * 100) / 100,
      unit: sensor.unit,
      quality,
      batteryLevel: sensor.batteryLevel,
      signalStrength: sensor.signalStrength
    };
  }

  private generateTemperatureReading(sensor: SensorConfig): number {
    const now = new Date();
    const hour = now.getHours();
    
    // Simulation de cycle journalier pour la température
    const dailyCycle = Math.sin((hour - 6) * Math.PI / 12) * 10;
    const baseTemp = (sensor.normalRange.min + sensor.normalRange.max) / 2;
    
    return baseTemp + dailyCycle + (Math.random() - 0.5) * 5;
  }

  private generateHumidityReading(sensor: SensorConfig): number {
    const baseHumidity = (sensor.normalRange.min + sensor.normalRange.max) / 2;
    const variation = (Math.random() - 0.5) * 20;
    
    return Math.max(0, Math.min(100, baseHumidity + variation));
  }

  private generateLevelReading(sensor: SensorConfig): number {
    const readings = this.readings.get(sensor.id) || [];
    const lastReading = readings[readings.length - 1];
    
    let currentLevel = lastReading ? lastReading.value : 20;
    
    // Simulation de remplissage progressif
    const fillRate = Math.random() * 2; // 0-2% par lecture
    currentLevel += fillRate;
    
    // Simulation de vidange occasionnelle
    if (currentLevel > 85 && Math.random() < 0.1) {
      currentLevel = Math.random() * 20; // Vidange
    }
    
    return Math.max(0, Math.min(100, currentLevel));
  }

  private generateWeightReading(sensor: SensorConfig): number {
    const readings = this.readings.get(sensor.id) || [];
    const lastReading = readings[readings.length - 1];
    
    let currentWeight = lastReading ? lastReading.value : 500;
    
    // Simulation d'ajout de déchets
    const addedWeight = Math.random() * 50;
    currentWeight += addedWeight;
    
    // Simulation de collecte
    if (currentWeight > 4500 && Math.random() < 0.05) {
      currentWeight = Math.random() * 200;
    }
    
    return Math.max(0, Math.min(sensor.maxValue, currentWeight));
  }

  private generateGasReading(sensor: SensorConfig): number {
    const baseLevel = 20;
    const spike = Math.random() < 0.05 ? Math.random() * 80 : 0; // 5% de chance de pic
    const variation = (Math.random() - 0.5) * 10;
    
    return Math.max(0, baseLevel + spike + variation);
  }

  private generatePHReading(sensor: SensorConfig): number {
    const basePH = 7.2;
    const variation = (Math.random() - 0.5) * 1.5;
    
    return Math.max(0, Math.min(14, basePH + variation));
  }

  private addNoise(value: number, quality: SensorReading['quality']): number {
    const noiseFactors = {
      excellent: 0.01,
      good: 0.02,
      fair: 0.05,
      poor: 0.1
    };
    
    const noiseFactor = noiseFactors[quality];
    return (Math.random() - 0.5) * value * noiseFactor;
  }

  private addReading(sensorId: string, reading: SensorReading): void {
    const readings = this.readings.get(sensorId) || [];
    readings.push(reading);
    
    // Garder seulement les 1000 dernières lectures
    if (readings.length > 1000) {
      readings.splice(0, readings.length - 1000);
    }
    
    this.readings.set(sensorId, readings);
  }

  private checkAlerts(sensor: SensorConfig, reading: SensorReading): void {
    // Vérifier les seuils
    if (reading.value < sensor.alertThresholds.low || reading.value > sensor.alertThresholds.high) {
      this.createAlert({
        sensorId: sensor.id,
        type: 'threshold_exceeded',
        severity: reading.value < sensor.alertThresholds.low ? 'medium' : 'high',
        message: `${sensor.name}: Valeur ${reading.value}${reading.unit} hors limites (${sensor.alertThresholds.low}-${sensor.alertThresholds.high}${reading.unit})`
      });
    }

    // Vérifier la batterie
    if (sensor.batteryLevel < 20) {
      this.createAlert({
        sensorId: sensor.id,
        type: 'low_battery',
        severity: sensor.batteryLevel < 10 ? 'critical' : 'medium',
        message: `${sensor.name}: Batterie faible (${sensor.batteryLevel}%)`
      });
    }

    // Vérifier la qualité du signal
    if (reading.quality === 'poor') {
      this.createAlert({
        sensorId: sensor.id,
        type: 'sensor_offline',
        severity: 'medium',
        message: `${sensor.name}: Qualité de signal dégradée`
      });
    }
  }

  private createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'acknowledged'>): void {
    // Éviter les doublons d'alertes récentes
    const recentAlert = this.alerts.find(alert => 
      alert.sensorId === alertData.sensorId &&
      alert.type === alertData.type &&
      !alert.acknowledged &&
      Date.now() - alert.timestamp.getTime() < 300000 // 5 minutes
    );

    if (recentAlert) return;

    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      acknowledged: false,
      ...alertData
    };

    this.alerts.unshift(alert);
    
    // Garder seulement les 100 dernières alertes
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    console.log(`🚨 Nouvelle alerte: ${alert.message}`);
  }

  private simulateBatteryDrain(): void {
    this.sensors.forEach(sensor => {
      if (sensor.isActive && sensor.batteryLevel > 0) {
        // Drain de 0.1% à 0.5% par minute selon l'activité
        const drainRate = 0.001 + Math.random() * 0.004;
        sensor.batteryLevel = Math.max(0, sensor.batteryLevel - drainRate);
        
        // Simulation de dégradation du signal avec batterie faible
        if (sensor.batteryLevel < 30) {
          sensor.signalStrength = Math.max(20, sensor.signalStrength - Math.random() * 2);
        }
      }
    });
  }

  private checkMaintenanceNeeds(): void {
    this.sensors.forEach(sensor => {
      const daysSinceMaintenace = (Date.now() - sensor.lastMaintenance.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceMaintenace > 30) { // Maintenance tous les 30 jours
        this.createAlert({
          sensorId: sensor.id,
          type: 'maintenance_required',
          severity: daysSinceMaintenace > 45 ? 'high' : 'medium',
          message: `${sensor.name}: Maintenance requise (${Math.floor(daysSinceMaintenace)} jours)`
        });
      }
    });
  }

  // Méthodes publiques
  getSensors(): SensorConfig[] {
    return Array.from(this.sensors.values());
  }

  getSensor(sensorId: string): SensorConfig | undefined {
    return this.sensors.get(sensorId);
  }

  getReadings(sensorId: string, limit = 100): SensorReading[] {
    const readings = this.readings.get(sensorId) || [];
    return readings.slice(-limit);
  }

  getAlerts(limit = 50): Alert[] {
    return this.alerts.slice(0, limit);
  }

  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
    }
  }

  getDashboardData(): IoTDashboardData {
    const sensors = Array.from(this.sensors.values());
    const activeSensors = sensors.filter(s => s.isActive);
    const unacknowledgedAlerts = this.alerts.filter(a => !a.acknowledged);
    
    const totalReadingsToday = Array.from(this.readings.values())
      .flat()
      .filter(r => {
        const today = new Date();
        const readingDate = new Date(r.timestamp);
        return readingDate.toDateString() === today.toDateString();
      }).length;

    const averageBatteryLevel = activeSensors.reduce((sum, s) => sum + s.batteryLevel, 0) / activeSensors.length;
    const averageSignalStrength = activeSensors.reduce((sum, s) => sum + s.signalStrength, 0) / activeSensors.length;
    
    const systemHealth = (averageBatteryLevel * 0.4 + averageSignalStrength * 0.4 + (activeSensors.length / sensors.length) * 100 * 0.2);

    return {
      totalSensors: sensors.length,
      activeSensors: activeSensors.length,
      alertsCount: unacknowledgedAlerts.length,
      averageBatteryLevel: Math.round(averageBatteryLevel),
      dataPointsToday: totalReadingsToday,
      systemHealth: Math.round(systemHealth)
    };
  }

  // Maintenance et configuration
  updateSensor(sensorId: string, updates: Partial<SensorConfig>): void {
    const sensor = this.sensors.get(sensorId);
    if (sensor) {
      Object.assign(sensor, updates);
    }
  }

  performMaintenance(sensorId: string): void {
    const sensor = this.sensors.get(sensorId);
    if (sensor) {
      sensor.lastMaintenance = new Date();
      sensor.batteryLevel = 100;
      sensor.signalStrength = 95;
      
      // Résoudre les alertes de maintenance
      this.alerts.forEach(alert => {
        if (alert.sensorId === sensorId && alert.type === 'maintenance_required') {
          alert.resolvedAt = new Date();
          alert.acknowledged = true;
        }
      });
    }
  }
}

// Instance singleton du simulateur IoT
export const iotSimulator = new IoTSensorSimulator();

// Hook React pour utiliser l'IoT
export function useIoTSimulator() {
  return {
    startSimulation: () => iotSimulator.startSimulation(),
    stopSimulation: () => iotSimulator.stopSimulation(),
    getSensors: () => iotSimulator.getSensors(),
    getSensor: (id: string) => iotSimulator.getSensor(id),
    getReadings: (sensorId: string, limit?: number) => iotSimulator.getReadings(sensorId, limit),
    getAlerts: (limit?: number) => iotSimulator.getAlerts(limit),
    acknowledgeAlert: (alertId: string) => iotSimulator.acknowledgeAlert(alertId),
    getDashboardData: () => iotSimulator.getDashboardData(),
    updateSensor: (sensorId: string, updates: Partial<SensorConfig>) => iotSimulator.updateSensor(sensorId, updates),
    performMaintenance: (sensorId: string) => iotSimulator.performMaintenance(sensorId)
  };
}

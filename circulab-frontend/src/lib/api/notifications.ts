import { apiClient, ApiResponse } from '../apiClient';
import { Notification, NotificationType, PaginationParams } from '../../types';

// Re-export types for convenience
export type { Notification, NotificationType } from '../../types';

export interface NotificationQuery extends PaginationParams {
  userId?: string;
  type?: NotificationType;
  isRead?: boolean;
}

export interface CreateNotificationData {
  userId: string;
  type: NotificationType;
  message: string;
  linkTo?: string;
}

export interface BulkNotificationData {
  userIds: string[];
  type: NotificationType;
  message: string;
  linkTo?: string;
}

export interface MarkAllReadData {
  userId: string;
  type?: NotificationType;
}

export interface NotificationStatistics {
  totalNotifications: number;
  unreadNotifications: number;
  notificationsByType: Array<{ type: NotificationType; count: number }>;
  notificationsByUser: Array<{ userId: string; userEmail: string; count: number; unreadCount: number }>;
  recentNotifications: Array<{ id: string; type: NotificationType; message: string; createdAt: string }>;
}

export interface NotificationsResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserNotificationsResponse extends NotificationsResponse {
  unreadCount: number;
}

export const notificationsApi = {
  // Get all notifications with filtering and pagination
  getAll: async (query?: NotificationQuery): Promise<ApiResponse<NotificationsResponse>> => {
    const params = new URLSearchParams();
    if (query?.userId) params.append('userId', query.userId);
    if (query?.type) params.append('type', query.type);
    if (query?.isRead !== undefined) params.append('isRead', query.isRead.toString());
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/notifications${queryString ? `?${queryString}` : ''}`);
  },

  // Get current user's notifications
  getMy: async (query?: Omit<NotificationQuery, 'userId'>): Promise<ApiResponse<UserNotificationsResponse>> => {
    const params = new URLSearchParams();
    if (query?.type) params.append('type', query.type);
    if (query?.isRead !== undefined) params.append('isRead', query.isRead.toString());
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/notifications/my${queryString ? `?${queryString}` : ''}`);
  },

  // Get notifications for a specific user
  getByUserId: async (userId: string, query?: Omit<NotificationQuery, 'userId'>): Promise<ApiResponse<UserNotificationsResponse>> => {
    const params = new URLSearchParams();
    if (query?.type) params.append('type', query.type);
    if (query?.isRead !== undefined) params.append('isRead', query.isRead.toString());
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/notifications/user/${userId}${queryString ? `?${queryString}` : ''}`);
  },

  // Get notification by ID
  getById: async (id: string): Promise<ApiResponse<Notification>> => {
    return apiClient.get(`/notifications/${id}`);
  },

  // Create new notification
  create: async (data: CreateNotificationData): Promise<ApiResponse<Notification>> => {
    return apiClient.post('/notifications', data);
  },

  // Create bulk notifications
  createBulk: async (data: BulkNotificationData): Promise<ApiResponse<{ count: number }>> => {
    return apiClient.post('/notifications/bulk', data);
  },

  // Update notification
  update: async (id: string, data: { isRead?: boolean }): Promise<ApiResponse<Notification>> => {
    return apiClient.put(`/notifications/${id}`, data);
  },

  // Mark notification as read
  markAsRead: async (id: string): Promise<ApiResponse<Notification>> => {
    return apiClient.put(`/notifications/${id}/read`);
  },

  // Mark all notifications as read
  markAllAsRead: async (data: MarkAllReadData): Promise<ApiResponse<{ count: number }>> => {
    return apiClient.put('/notifications/mark-all-read', data);
  },

  // Delete notification
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/notifications/${id}`);
  },

  // Get notification statistics
  getStatistics: async (): Promise<ApiResponse<NotificationStatistics>> => {
    return apiClient.get('/notifications/statistics');
  },
};

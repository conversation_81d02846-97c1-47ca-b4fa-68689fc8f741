import { apiClient, ApiResponse } from '../apiClient';
import { Company, PaginationParams } from '../../types';

export interface CompanyQuery extends PaginationParams {
  search?: string;
  industryType?: string;
}

export interface CreateCompanyData {
  name: string;
  siret?: string;
  address?: string;
  industryType?: string;
  contactPerson?: string;
  contactEmail?: string;
}

export interface UpdateCompanyData extends Partial<CreateCompanyData> {}

export interface CompanyStatistics {
  totalCompanies: number;
  companiesByIndustry: Array<{ industryType: string; count: number }>;
  topProducers: Array<{ name: string; wasteCount: number }>;
  topProcessors: Array<{ name: string; processedCount: number }>;
  recentlyJoined: Array<{ name: string; createdAt: string }>;
}

export interface CompaniesResponse {
  companies: Company[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const companiesApi = {
  // Get all companies with filtering and pagination
  getAll: async (query?: CompanyQuery): Promise<ApiResponse<CompaniesResponse>> => {
    const params = new URLSearchParams();
    if (query?.search) params.append('search', query.search);
    if (query?.industryType) params.append('industryType', query.industryType);
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/companies${queryString ? `?${queryString}` : ''}`);
  },

  // Get company by ID
  getById: async (id: string): Promise<ApiResponse<Company>> => {
    return apiClient.get(`/companies/${id}`);
  },

  // Create new company
  create: async (data: CreateCompanyData): Promise<ApiResponse<Company>> => {
    return apiClient.post('/companies', data);
  },

  // Update company
  update: async (id: string, data: UpdateCompanyData): Promise<ApiResponse<Company>> => {
    return apiClient.put(`/companies/${id}`, data);
  },

  // Delete company
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/companies/${id}`);
  },

  // Get company statistics
  getStatistics: async (): Promise<ApiResponse<CompanyStatistics>> => {
    return apiClient.get('/companies/statistics');
  },
};

import { apiClient, ApiResponse } from '../apiClient';
import { ProcessingHistory, PaginationParams } from '../../types';

// Re-export types for convenience
export type { ProcessingHistory } from '../../types';

export interface ProcessingHistoryQuery extends PaginationParams {
  wasteMaterialId?: string;
  processedByUserId?: string;
  processorCompanyId?: string;
  treatmentMethodId?: string;
  wasteType?: string;
  treatmentMethod?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface CreateProcessingHistoryData {
  wasteMaterialId: string;
  processedByUserId?: string;
  processorCompanyId: string;
  treatmentMethodId: string;
  dateProcessed: string;
  inputQuantity?: number;
  outputQuantity?: number;
  outputMaterialType?: string;
  environmentalImpactAchieved?: Record<string, any>;
  notes?: string;
}

export interface UpdateProcessingHistoryData {
  processedByUserId?: string;
  treatmentMethodId?: string;
  dateProcessed?: string;
  inputQuantity?: number;
  outputQuantity?: number;
  outputMaterialType?: string;
  environmentalImpactAchieved?: Record<string, any>;
  notes?: string;
}

export interface ProcessingEfficiencyQuery {
  companyId?: string;
  treatmentMethodId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ProcessingHistoryStatistics {
  totalProcessed: number;
  totalQuantityProcessed: number;
  processingByMethod: Array<{ method: string; count: number; totalQuantity: number }>;
  processingByCompany: Array<{ company: string; count: number; totalQuantity: number }>;
  processingTrends: Array<{ month: string; count: number; quantity: number }>;
  environmentalImpact: {
    totalCO2Saved: number;
    totalEnergySaved: number;
    totalWaterSaved: number;
  };
}

export interface ProcessingEfficiencyMetrics {
  averageEfficiency: number;
  efficiencyByMethod: Array<{ method: string; efficiency: number; processedCount: number }>;
  efficiencyTrends: Array<{ month: string; efficiency: number }>;
}

export interface ProcessingHistoryResponse {
  processingHistory: ProcessingHistory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const processingHistoryApi = {
  // Get all processing history with filtering and pagination
  getAll: async (query?: ProcessingHistoryQuery): Promise<ApiResponse<ProcessingHistoryResponse>> => {
    const params = new URLSearchParams();
    if (query?.wasteMaterialId) params.append('wasteMaterialId', query.wasteMaterialId);
    if (query?.processedByUserId) params.append('processedByUserId', query.processedByUserId);
    if (query?.processorCompanyId) params.append('processorCompanyId', query.processorCompanyId);
    if (query?.treatmentMethodId) params.append('treatmentMethodId', query.treatmentMethodId);
    if (query?.wasteType) params.append('wasteType', query.wasteType);
    if (query?.treatmentMethod) params.append('treatmentMethod', query.treatmentMethod);
    if (query?.dateFrom) params.append('dateFrom', query.dateFrom);
    if (query?.dateTo) params.append('dateTo', query.dateTo);
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/processing-history${queryString ? `?${queryString}` : ''}`);
  },

  // Get processing history by ID
  getById: async (id: string): Promise<ApiResponse<ProcessingHistory>> => {
    return apiClient.get(`/processing-history/${id}`);
  },

  // Create new processing history record
  create: async (data: CreateProcessingHistoryData): Promise<ApiResponse<ProcessingHistory>> => {
    return apiClient.post('/processing-history', data);
  },

  // Update processing history
  update: async (id: string, data: UpdateProcessingHistoryData): Promise<ApiResponse<ProcessingHistory>> => {
    return apiClient.put(`/processing-history/${id}`, data);
  },

  // Delete processing history
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/processing-history/${id}`);
  },

  // Get processing history statistics
  getStatistics: async (): Promise<ApiResponse<ProcessingHistoryStatistics>> => {
    return apiClient.get('/processing-history/statistics');
  },

  // Get processing efficiency metrics
  getEfficiency: async (query?: ProcessingEfficiencyQuery): Promise<ApiResponse<ProcessingEfficiencyMetrics>> => {
    const params = new URLSearchParams();
    if (query?.companyId) params.append('companyId', query.companyId);
    if (query?.treatmentMethodId) params.append('treatmentMethodId', query.treatmentMethodId);
    if (query?.dateFrom) params.append('dateFrom', query.dateFrom);
    if (query?.dateTo) params.append('dateTo', query.dateTo);

    const queryString = params.toString();
    return apiClient.get(`/processing-history/efficiency${queryString ? `?${queryString}` : ''}`);
  },
};

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { mockApi, shouldUseMockApi } from './mockApi';

// API Configuration
// In development, use relative URLs to leverage Next.js rewrites
// In production, use the full API URL
const API_BASE_URL = process.env.NODE_ENV === 'development'
  ? '/api'
  : (process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api');

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { accessToken } = response.data.data.tokens;
          localStorage.setItem('accessToken', accessToken);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: any;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  timestamp: string;
}

// Mock API route mapping
const mockRoutes: Record<string, (data?: any) => Promise<any>> = {
  'POST:/auth/login': (data) => mockApi.login(data),
  'POST:/auth/register': (data) => mockApi.register(data),
  'GET:/auth/profile': () => mockApi.getProfile(),
  'POST:/auth/refresh': () => mockApi.refreshToken(),
  'GET:/waste-materials': () => mockApi.getMaterials(),
  'GET:/waste-materials/statistics': () => mockApi.getStatistics(),
};

// Generic API methods (legacy)
export const legacyApiClient = {
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    if (shouldUseMockApi()) {
      console.log('🔄 Using Mock API for GET:', url);
      const mockRoute = `GET:${url.split('?')[0]}`;
      const mockHandler = mockRoutes[mockRoute];
      if (mockHandler) {
        try {
          return await mockHandler();
        } catch (error: any) {
          throw new Error(error.message || 'Mock API error');
        }
      }
    }
    console.log('🌐 Using Real API for GET:', url);
    return api.get(url, config).then((response) => response.data);
  },

  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    if (shouldUseMockApi()) {
      console.log('🔄 Using Mock API for POST:', url);
      const mockRoute = `POST:${url}`;
      const mockHandler = mockRoutes[mockRoute];
      if (mockHandler) {
        try {
          return await mockHandler(data);
        } catch (error: any) {
          throw new Error(error.message || 'Mock API error');
        }
      }
    }
    console.log('🌐 Using Real API for POST:', url);
    return api.post(url, data, config).then((response) => response.data);
  },

  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    if (shouldUseMockApi()) {
      // For now, just return success for PUT requests in mock mode
      return {
        success: true,
        data: data,
        message: 'Mock update successful',
        timestamp: new Date().toISOString(),
      } as ApiResponse<T>;
    }
    return api.put(url, data, config).then((response) => response.data);
  },

  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    if (shouldUseMockApi()) {
      // For now, just return success for DELETE requests in mock mode
      return {
        success: true,
        message: 'Mock delete successful',
        timestamp: new Date().toISOString(),
      } as ApiResponse<T>;
    }
    return api.delete(url, config).then((response) => response.data);
  },

  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    if (shouldUseMockApi()) {
      // For now, just return success for PATCH requests in mock mode
      return {
        success: true,
        data: data,
        message: 'Mock patch successful',
        timestamp: new Date().toISOString(),
      } as ApiResponse<T>;
    }
    return api.patch(url, data, config).then((response) => response.data);
  },
};

export default api;

// Export all new API services
export * from './api/index';

// CircuLab 3D Site Visualization Engine
// Moteur de visualisation 3D pour sites de déchets

export interface Site3DData {
  id: string;
  name: string;
  type: 'collection' | 'processing' | 'recycling' | 'disposal' | 'storage';
  position: { x: number; y: number; z: number };
  dimensions: { width: number; height: number; depth: number };
  capacity: {
    current: number;
    maximum: number;
    unit: string;
  };
  status: 'operational' | 'maintenance' | 'full' | 'empty' | 'error';
  materials: Array<{
    type: string;
    quantity: number;
    color: string;
    position: { x: number; y: number; z: number };
  }>;
  equipment: Array<{
    id: string;
    type: 'conveyor' | 'crusher' | 'separator' | 'compactor' | 'crane';
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    status: 'active' | 'idle' | 'maintenance';
    efficiency: number;
  }>;
  sensors: Array<{
    id: string;
    type: string;
    position: { x: number; y: number; z: number };
    value: number;
    status: 'online' | 'offline' | 'warning';
  }>;
  environmental: {
    temperature: number;
    humidity: number;
    airQuality: number;
    noiseLevel: number;
  };
  safety: {
    zones: Array<{
      type: 'safe' | 'caution' | 'danger';
      bounds: { min: { x: number; y: number; z: number }; max: { x: number; y: number; z: number } };
      description: string;
    }>;
    emergencyExits: Array<{ x: number; y: number; z: number }>;
  };
}

export interface DigitalTwinConfig {
  updateInterval: number; // ms
  qualityLevel: 'low' | 'medium' | 'high' | 'ultra';
  enablePhysics: boolean;
  enableParticles: boolean;
  enableLighting: boolean;
  enableShadows: boolean;
  enablePostProcessing: boolean;
}

export interface ViewerControls {
  camera: {
    position: { x: number; y: number; z: number };
    target: { x: number; y: number; z: number };
    fov: number;
  };
  navigation: {
    mode: 'orbit' | 'fly' | 'walk' | 'inspect';
    speed: number;
    smoothing: number;
  };
  interaction: {
    enableSelection: boolean;
    enableMeasurement: boolean;
    enableAnnotations: boolean;
    enableCrossSections: boolean;
  };
}

export interface ARScanResult {
  id: string;
  timestamp: Date;
  confidence: number;
  wasteType: string;
  estimatedWeight: number;
  composition: Record<string, number>;
  recyclability: number;
  recommendations: string[];
  position: { x: number; y: number; z: number };
  boundingBox: {
    min: { x: number; y: number; z: number };
    max: { x: number; y: number; z: number };
  };
}

// Simulateur de données 3D pour sites
class Site3DSimulator {
  private sites: Map<string, Site3DData> = new Map();
  private digitalTwins: Map<string, DigitalTwinConfig> = new Map();
  private isRunning = false;
  private updateInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeDefaultSites();
  }

  private initializeDefaultSites(): void {
    const defaultSites: Site3DData[] = [
      {
        id: 'site_processing_01',
        name: 'Centre de Tri Principal',
        type: 'processing',
        position: { x: 0, y: 0, z: 0 },
        dimensions: { width: 100, height: 15, depth: 80 },
        capacity: { current: 750, maximum: 1000, unit: 'tonnes' },
        status: 'operational',
        materials: [
          { type: 'plastic', quantity: 250, color: '#3B82F6', position: { x: -30, y: 2, z: -20 } },
          { type: 'paper', quantity: 200, color: '#F59E0B', position: { x: -10, y: 2, z: -20 } },
          { type: 'metal', quantity: 150, color: '#6B7280', position: { x: 10, y: 2, z: -20 } },
          { type: 'glass', quantity: 150, color: '#10B981', position: { x: 30, y: 2, z: -20 } }
        ],
        equipment: [
          {
            id: 'conveyor_01',
            type: 'conveyor',
            position: { x: 0, y: 3, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            status: 'active',
            efficiency: 0.92
          },
          {
            id: 'separator_01',
            type: 'separator',
            position: { x: 20, y: 5, z: 10 },
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            status: 'active',
            efficiency: 0.88
          },
          {
            id: 'compactor_01',
            type: 'compactor',
            position: { x: -20, y: 4, z: 15 },
            rotation: { x: 0, y: -Math.PI / 3, z: 0 },
            status: 'idle',
            efficiency: 0.95
          }
        ],
        sensors: [
          { id: 'temp_01', type: 'temperature', position: { x: -40, y: 8, z: -30 }, value: 22.5, status: 'online' },
          { id: 'weight_01', type: 'weight', position: { x: 0, y: 1, z: -25 }, value: 750, status: 'online' },
          { id: 'air_01', type: 'air_quality', position: { x: 40, y: 10, z: 30 }, value: 85, status: 'online' }
        ],
        environmental: {
          temperature: 22.5,
          humidity: 45,
          airQuality: 85,
          noiseLevel: 65
        },
        safety: {
          zones: [
            {
              type: 'safe',
              bounds: { min: { x: -50, y: 0, z: -40 }, max: { x: 50, y: 20, z: 40 } },
              description: 'Zone de circulation générale'
            },
            {
              type: 'caution',
              bounds: { min: { x: -25, y: 0, z: 5 }, max: { x: 25, y: 10, z: 25 } },
              description: 'Zone de machinerie - Port EPI obligatoire'
            },
            {
              type: 'danger',
              bounds: { min: { x: 15, y: 0, z: 5 }, max: { x: 25, y: 8, z: 15 } },
              description: 'Zone de compactage - Accès restreint'
            }
          ],
          emergencyExits: [
            { x: -45, y: 0, z: -35 },
            { x: 45, y: 0, z: -35 },
            { x: -45, y: 0, z: 35 },
            { x: 45, y: 0, z: 35 }
          ]
        }
      },
      {
        id: 'site_storage_01',
        name: 'Entrepôt de Stockage',
        type: 'storage',
        position: { x: 150, y: 0, z: 0 },
        dimensions: { width: 60, height: 12, depth: 40 },
        capacity: { current: 450, maximum: 600, unit: 'tonnes' },
        status: 'operational',
        materials: [
          { type: 'plastic', quantity: 180, color: '#3B82F6', position: { x: 135, y: 3, z: -10 } },
          { type: 'paper', quantity: 120, color: '#F59E0B', position: { x: 155, y: 3, z: -10 } },
          { type: 'metal', quantity: 90, color: '#6B7280', position: { x: 145, y: 3, z: 10 } },
          { type: 'organic', quantity: 60, color: '#22C55E', position: { x: 165, y: 3, z: 10 } }
        ],
        equipment: [
          {
            id: 'crane_01',
            type: 'crane',
            position: { x: 150, y: 8, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            status: 'active',
            efficiency: 0.85
          }
        ],
        sensors: [
          { id: 'level_01', type: 'level', position: { x: 150, y: 6, z: 0 }, value: 75, status: 'online' },
          { id: 'humidity_01', type: 'humidity', position: { x: 140, y: 8, z: -15 }, value: 42, status: 'online' }
        ],
        environmental: {
          temperature: 18.2,
          humidity: 42,
          airQuality: 92,
          noiseLevel: 45
        },
        safety: {
          zones: [
            {
              type: 'safe',
              bounds: { min: { x: 120, y: 0, z: -20 }, max: { x: 180, y: 15, z: 20 } },
              description: 'Zone de stockage sécurisée'
            }
          ],
          emergencyExits: [
            { x: 125, y: 0, z: -18 },
            { x: 175, y: 0, z: 18 }
          ]
        }
      }
    ];

    defaultSites.forEach(site => {
      this.sites.set(site.id, site);
      this.digitalTwins.set(site.id, {
        updateInterval: 1000,
        qualityLevel: 'high',
        enablePhysics: true,
        enableParticles: true,
        enableLighting: true,
        enableShadows: true,
        enablePostProcessing: true
      });
    });
  }

  // Démarrer la simulation temps réel
  startDigitalTwin(siteId: string): void {
    if (this.isRunning) return;

    this.isRunning = true;
    const config = this.digitalTwins.get(siteId);
    if (!config) return;

    this.updateInterval = setInterval(() => {
      this.updateSiteData(siteId);
    }, config.updateInterval);

    console.log(`🏭 Digital Twin démarré pour ${siteId}`);
  }

  stopDigitalTwin(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.isRunning = false;
    console.log('⏹️ Digital Twin arrêté');
  }

  private updateSiteData(siteId: string): void {
    const site = this.sites.get(siteId);
    if (!site) return;

    // Simulation de mise à jour des données en temps réel
    
    // Mise à jour des capteurs
    site.sensors.forEach(sensor => {
      switch (sensor.type) {
        case 'temperature':
          sensor.value += (Math.random() - 0.5) * 2;
          sensor.value = Math.max(15, Math.min(35, sensor.value));
          break;
        case 'weight':
          sensor.value += (Math.random() - 0.5) * 10;
          sensor.value = Math.max(0, Math.min(site.capacity.maximum, sensor.value));
          break;
        case 'air_quality':
          sensor.value += (Math.random() - 0.5) * 5;
          sensor.value = Math.max(0, Math.min(100, sensor.value));
          break;
        case 'level':
          sensor.value += (Math.random() - 0.5) * 3;
          sensor.value = Math.max(0, Math.min(100, sensor.value));
          break;
        case 'humidity':
          sensor.value += (Math.random() - 0.5) * 2;
          sensor.value = Math.max(20, Math.min(80, sensor.value));
          break;
      }
    });

    // Mise à jour de l'efficacité des équipements
    site.equipment.forEach(equipment => {
      if (equipment.status === 'active') {
        equipment.efficiency += (Math.random() - 0.5) * 0.02;
        equipment.efficiency = Math.max(0.7, Math.min(1.0, equipment.efficiency));
      }
    });

    // Mise à jour des matériaux (simulation de flux)
    site.materials.forEach(material => {
      if (site.type === 'processing') {
        // Simulation de traitement
        material.quantity += (Math.random() - 0.6) * 5; // Légère diminution
        material.quantity = Math.max(0, material.quantity);
      } else if (site.type === 'storage') {
        // Simulation de stockage
        material.quantity += (Math.random() - 0.5) * 2;
        material.quantity = Math.max(0, material.quantity);
      }
    });

    // Mise à jour de la capacité actuelle
    site.capacity.current = site.materials.reduce((sum, material) => sum + material.quantity, 0);

    // Mise à jour du statut
    const capacityRatio = site.capacity.current / site.capacity.maximum;
    if (capacityRatio > 0.95) {
      site.status = 'full';
    } else if (capacityRatio < 0.05) {
      site.status = 'empty';
    } else {
      site.status = 'operational';
    }
  }

  // Simulation de scan AR
  simulateARScan(position: { x: number; y: number; z: number }): ARScanResult {
    const wasteTypes = ['plastic_pet', 'cardboard', 'aluminum', 'glass', 'organic'];
    const selectedType = wasteTypes[Math.floor(Math.random() * wasteTypes.length)];
    
    const compositions: Record<string, Record<string, number>> = {
      plastic_pet: { pet: 0.95, contaminants: 0.05 },
      cardboard: { cardboard: 0.92, plastic: 0.03, other: 0.05 },
      aluminum: { aluminum: 0.98, other: 0.02 },
      glass: { glass: 0.96, labels: 0.04 },
      organic: { organic: 0.88, plastic: 0.07, other: 0.05 }
    };

    const recyclabilityScores: Record<string, number> = {
      plastic_pet: 0.85,
      cardboard: 0.92,
      aluminum: 0.98,
      glass: 0.88,
      organic: 0.45
    };

    const recommendations: Record<string, string[]> = {
      plastic_pet: ['Retirer les étiquettes', 'Rincer le contenant', 'Compacter si possible'],
      cardboard: ['Retirer les agrafes', 'Aplatir les cartons', 'Éviter l\'humidité'],
      aluminum: ['Nettoyer les résidus', 'Compacter pour optimiser l\'espace'],
      glass: ['Retirer les bouchons', 'Séparer par couleur', 'Attention aux éclats'],
      organic: ['Composter si possible', 'Éviter les plastiques', 'Contrôler l\'humidité']
    };

    return {
      id: `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      confidence: 0.8 + Math.random() * 0.15, // 80-95%
      wasteType: selectedType,
      estimatedWeight: 0.1 + Math.random() * 2, // 0.1-2.1 kg
      composition: compositions[selectedType],
      recyclability: recyclabilityScores[selectedType],
      recommendations: recommendations[selectedType],
      position,
      boundingBox: {
        min: { x: position.x - 0.1, y: position.y - 0.1, z: position.z - 0.1 },
        max: { x: position.x + 0.1, y: position.y + 0.1, z: position.z + 0.1 }
      }
    };
  }

  // Méthodes publiques
  getSites(): Site3DData[] {
    return Array.from(this.sites.values());
  }

  getSite(siteId: string): Site3DData | undefined {
    return this.sites.get(siteId);
  }

  getDigitalTwinConfig(siteId: string): DigitalTwinConfig | undefined {
    return this.digitalTwins.get(siteId);
  }

  updateDigitalTwinConfig(siteId: string, config: Partial<DigitalTwinConfig>): void {
    const currentConfig = this.digitalTwins.get(siteId);
    if (currentConfig) {
      this.digitalTwins.set(siteId, { ...currentConfig, ...config });
    }
  }

  getViewerControls(): ViewerControls {
    return {
      camera: {
        position: { x: 50, y: 30, z: 50 },
        target: { x: 0, y: 0, z: 0 },
        fov: 75
      },
      navigation: {
        mode: 'orbit',
        speed: 1.0,
        smoothing: 0.1
      },
      interaction: {
        enableSelection: true,
        enableMeasurement: true,
        enableAnnotations: true,
        enableCrossSections: false
      }
    };
  }

  // Calculs de performance 3D
  calculateLOD(distance: number): 'low' | 'medium' | 'high' {
    if (distance > 100) return 'low';
    if (distance > 50) return 'medium';
    return 'high';
  }

  optimizeForMobile(): DigitalTwinConfig {
    return {
      updateInterval: 2000,
      qualityLevel: 'medium',
      enablePhysics: false,
      enableParticles: false,
      enableLighting: true,
      enableShadows: false,
      enablePostProcessing: false
    };
  }
}

// Instance singleton du simulateur 3D
export const site3DSimulator = new Site3DSimulator();

// Hook React pour utiliser la visualisation 3D
export function useSite3DVisualization() {
  return {
    getSites: () => site3DSimulator.getSites(),
    getSite: (siteId: string) => site3DSimulator.getSite(siteId),
    startDigitalTwin: (siteId: string) => site3DSimulator.startDigitalTwin(siteId),
    stopDigitalTwin: () => site3DSimulator.stopDigitalTwin(),
    getDigitalTwinConfig: (siteId: string) => site3DSimulator.getDigitalTwinConfig(siteId),
    updateDigitalTwinConfig: (siteId: string, config: Partial<DigitalTwinConfig>) => 
      site3DSimulator.updateDigitalTwinConfig(siteId, config),
    getViewerControls: () => site3DSimulator.getViewerControls(),
    simulateARScan: (position: { x: number; y: number; z: number }) => 
      site3DSimulator.simulateARScan(position),
    calculateLOD: (distance: number) => site3DSimulator.calculateLOD(distance),
    optimizeForMobile: () => site3DSimulator.optimizeForMobile()
  };
}

name: CircuLab Health Check & Resilience Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run health check daily at 6 AM UTC
    - cron: '0 6 * * *'

jobs:
  health-check:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: circulab-frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./circulab-frontend
      run: npm ci
    
    - name: Run development health check
      working-directory: ./circulab-frontend
      run: |
        # Make scripts executable
        chmod +x scripts/check-dev-health.sh
        
        # Run health check
        ./scripts/check-dev-health.sh
        
        # Check if confidence score is acceptable
        if command -v jq &> /dev/null; then
          CONFIDENCE=$(jq -r '.confidence // 0' logs/health-report.json)
          echo "Health confidence score: $CONFIDENCE%"
          
          if [ "$CONFIDENCE" -lt 60 ]; then
            echo "❌ Health confidence score too low: $CONFIDENCE%"
            echo "Failing build due to poor system health"
            exit 1
          else
            echo "✅ Health confidence score acceptable: $CONFIDENCE%"
          fi
        fi
    
    - name: Build application
      working-directory: ./circulab-frontend
      run: npm run build
    
    - name: Start application for testing
      working-directory: ./circulab-frontend
      run: |
        # Start the application in background
        npm start &
        APP_PID=$!
        echo $APP_PID > app.pid
        
        # Wait for application to be ready
        for i in {1..30}; do
          if curl -f http://localhost:3000/health &> /dev/null; then
            echo "✅ Application is ready"
            break
          fi
          echo "Waiting for application... ($i/30)"
          sleep 2
        done
    
    - name: Run resilience tests
      working-directory: ./circulab-frontend
      run: |
        # Make test scripts executable
        chmod +x scripts/test-extension-conflict.sh
        chmod +x scripts/test-api-failure.sh
        
        echo "🧪 Running extension conflict test..."
        ./scripts/test-extension-conflict.sh run || echo "Extension test completed with warnings"
        
        echo "🧪 Running API failure test..."
        ./scripts/test-api-failure.sh run || echo "API failure test completed with warnings"
    
    - name: Validate health monitoring
      working-directory: ./circulab-frontend
      run: |
        # Test health endpoint
        HEALTH_RESPONSE=$(curl -s http://localhost:3000/health || echo "ERROR")
        
        if [[ "$HEALTH_RESPONSE" == *"Health Dashboard"* ]]; then
          echo "✅ Health dashboard accessible"
        else
          echo "❌ Health dashboard not accessible"
          exit 1
        fi
        
        # Check if error logging is working
        if [ -f "logs/health-check.log" ]; then
          echo "✅ Health check logging is working"
        else
          echo "⚠️ No health check logs found"
        fi
    
    - name: Generate test report
      working-directory: ./circulab-frontend
      if: always()
      run: |
        # Create comprehensive test report
        cat > test-report.json << EOF
        {
          "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
          "node_version": "${{ matrix.node-version }}",
          "build_status": "$([ -d .next ] && echo 'success' || echo 'failed')",
          "health_check": {
            "confidence_score": $(jq -r '.confidence // 0' logs/health-report.json 2>/dev/null || echo '0'),
            "status": "$(jq -r '.overall_status // "unknown"' logs/health-report.json 2>/dev/null || echo 'unknown')"
          },
          "resilience_tests": {
            "extension_conflict": "$([ -f logs/extension-test.log ] && echo 'completed' || echo 'skipped')",
            "api_failure": "$([ -f logs/api-test.log ] && echo 'completed' || echo 'skipped')"
          }
        }
        EOF
        
        echo "📊 Test Report Generated:"
        cat test-report.json
    
    - name: Upload test artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: health-check-results-node-${{ matrix.node-version }}
        path: |
          circulab-frontend/logs/
          circulab-frontend/test-report.json
        retention-days: 30
    
    - name: Cleanup
      working-directory: ./circulab-frontend
      if: always()
      run: |
        # Stop application if running
        if [ -f app.pid ]; then
          APP_PID=$(cat app.pid)
          kill $APP_PID 2>/dev/null || true
          rm app.pid
        fi
        
        # Clean up any test artifacts
        rm -f public/extension-conflict.js
        
        # Restore any backed up files
        if [ -f src/app/layout.tsx.backup ]; then
          mv src/app/layout.tsx.backup src/app/layout.tsx
        fi

  notify-results:
    needs: health-check
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.health-check.result == 'success'
      run: |
        echo "✅ All health checks passed successfully!"
        echo "System is ready for deployment."
    
    - name: Notify on failure
      if: needs.health-check.result == 'failure'
      run: |
        echo "❌ Health checks failed!"
        echo "Please review the test results and fix issues before deployment."
        exit 1

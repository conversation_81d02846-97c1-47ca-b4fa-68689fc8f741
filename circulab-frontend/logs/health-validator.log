Timestamp: Fri Jun  6 19:36:32 CEST 2025
[0;35m[SECTION][0m 1. Build System Validation
[0;34m[VALIDATOR][0m Testing clean build...
[0;31m[ERROR][0m Build failed
[0;34m[VALIDATOR][0m Testing TypeScript compilation...
[1;33m[WARNING][0m TypeScript compilation issues
[0;34m[VALIDATOR][0m Testing ESLint...
[1;33m[WARNING][0m ESLint issues found
[0;34m[VALIDATOR][0m Build validation score: 0/100
[0;35m[SECTION][0m 2. Dependencies Validation
[0;34m[VALIDATOR][0m Checking security vulnerabilities...
[1;33m[WARNING][0m 2 high/critical vulnerabilities found
[0;34m[VALIDATOR][0m Checking outdated packages...
[0;34m[VALIDATOR][0m 11 packages have updates available
[1;33m[WARNING][0m package-lock.json missing
[0;34m[VALIDATOR][0m Dependencies validation score: 20/100
[0;35m[SECTION][0m 3. Performance Validation
[0;34m[VALIDATOR][0m Analyzing bundle size...
[0;34m[VALIDATOR][0m Bundle size: 242M
[0;32m[SUCCESS][0m No large chunks detected
[0;34m[VALIDATOR][0m Checking for performance anti-patterns...
[1;33m[WARNING][0m Excessive console.log usage: 78
[0;34m[VALIDATOR][0m Performance validation score: 75/100
[0;35m[SECTION][0m 4. Architecture Validation
[0;32m[SUCCESS][0m All required directories present
[1;33m[WARNING][0m        7 files exceed 300 lines
[0;34m[VALIDATOR][0m Architecture validation score: 75/100
[0;35m[SECTION][0m 5. Calculating Overall Score
[0;34m[VALIDATOR][0m Overall validation score: 36.0/100
✅ EXCELLENT: System is healthy
[0;35m[SECTION][0m 6. Generating Recommendations
[0;35m[SECTION][0m Validation Complete
[0;34m[VALIDATOR][0m Full report available at: /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/logs/validation-report.json
[0;34m[VALIDATOR][0m Detailed logs available at: /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/logs/health-validator.log

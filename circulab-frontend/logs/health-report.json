{"timestamp": "2025-06-06T15:22:58Z", "checks": {"node_environment": {"status": "healthy", "message": "Node.js environment compatible", "details": "v22.15.1", "timestamp": "2025-06-06T15:23:04Z"}, "dependencies": {"status": "healthy", "message": "All critical dependencies installed", "details": "", "timestamp": "2025-06-06T15:23:04Z"}, "nextjs_config": {"status": "healthy", "message": "Next.js cache healthy", "details": "Cache size: 285M", "timestamp": "2025-06-06T15:23:04Z"}, "typescript": {"status": "warning", "message": "TypeScript compilation errors", "details": "Error count: 12", "timestamp": "2025-06-06T15:23:17Z"}, "problematic_files": {"status": "warning", "message": "Problematic files detected", "details": "      16 nested node_modules directories found", "timestamp": "2025-06-06T15:23:22Z"}, "vscode_config": {"status": "healthy", "message": "No VS Code configuration", "details": "", "timestamp": "2025-06-06T15:23:22Z"}, "ports": {"status": "warning", "message": "Some ports are busy", "details": "4000", "timestamp": "2025-06-06T15:23:22Z"}}, "overall_status": "warning", "confidence": 78, "recommendations": ["Fix TypeScript errors before proceeding"]}
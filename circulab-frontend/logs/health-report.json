{"timestamp": "2025-06-06T16:54:40Z", "checks": {"node_environment": {"status": "healthy", "message": "Node.js environment compatible", "details": "v22.15.1", "timestamp": "2025-06-06T16:54:42Z"}, "dependencies": {"status": "healthy", "message": "All critical dependencies installed", "details": "", "timestamp": "2025-06-06T16:54:42Z"}, "nextjs_config": {"status": "healthy", "message": "Next.js cache healthy", "details": "Cache size: 294M", "timestamp": "2025-06-06T16:54:42Z"}, "typescript": {"status": "healthy", "message": "TypeScript compilation successful", "details": "", "timestamp": "2025-06-06T16:54:59Z"}, "problematic_files": {"status": "warning", "message": "Problematic files detected", "details": "      16 nested node_modules directories found", "timestamp": "2025-06-06T16:55:04Z"}, "vscode_config": {"status": "healthy", "message": "No VS Code configuration", "details": "", "timestamp": "2025-06-06T16:55:04Z"}, "ports": {"status": "warning", "message": "Some ports are busy", "details": "4000", "timestamp": "2025-06-06T16:55:04Z"}}, "overall_status": "warning", "confidence": 85, "recommendations": []}
{"timestamp": "2025-06-06T18:45:10Z", "validation_version": "2.0.0", "status": "completed", "overall_score": 61.0, "categories": {"build": {"score": 100, "status": "healthy", "details": ["Clean build successful", "TypeScript compilation clean", "ESLint validation clean"]}, "dependencies": {"score": 20, "status": "error", "details": ["High/critical vulnerabilities: 2", "Outdated packages: 11", "package-lock.json missing"]}, "typescript": {"score": 0, "status": "pending", "details": []}, "performance": {"score": 50, "status": "error", "details": ["Bundle size: 279M", "No large chunks (>1MB)", "Excessive console logs: 91", "High import count - review for optimization"]}, "security": {"score": 0, "status": "pending", "details": []}, "architecture": {"score": 75, "status": "warning", "details": ["All required directories present", "Large files:       17"]}}, "recommendations": ["Update dependencies and fix security vulnerabilities"], "critical_issues": [], "performance_metrics": {}}
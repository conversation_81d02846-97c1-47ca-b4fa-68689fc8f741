{"timestamp": "2025-06-06T17:36:32Z", "validation_version": "2.0.0", "status": "completed", "overall_score": 36.0, "categories": {"build": {"score": 0, "status": "error", "details": ["Build failed - check TypeScript/ESLint errors", "Build errors: 1", "TypeScript errors: 13", "ESLint errors: 5, warnings: 158"]}, "dependencies": {"score": 20, "status": "error", "details": ["High/critical vulnerabilities: 2", "Outdated packages: 11", "package-lock.json missing"]}, "typescript": {"score": 0, "status": "pending", "details": []}, "performance": {"score": 75, "status": "warning", "details": ["Bundle size: 242M", "No large chunks (>1MB)", "Excessive console logs: 78", "Import usage appears optimized"]}, "security": {"score": 0, "status": "pending", "details": []}, "architecture": {"score": 75, "status": "warning", "details": ["All required directories present", "Large files:        7"]}}, "recommendations": ["Fix TypeScript and ESLint errors to improve build reliability", "Update dependencies and fix security vulnerabilities"], "critical_issues": [], "performance_metrics": {}}
Timestamp: Fri Jun  6 17:22:58 CEST 2025
[0;35m[HEALTH CHECK][0m Checking Node.js Environment
[0;34m[INFO][0m Node.js version: v22.15.1
[0;34m[INFO][0m npm version: 10.9.2
[0;32m[SUCCESS][0m Node.js environment is compatible
[0;35m[HEALTH CHECK][0m Checking Dependencies
[1;33m[WARNING][0m package-lock.json not found - dependency versions may be inconsistent
[0;32m[SUCCESS][0m All critical dependencies are installed
[0;35m[HEALTH CHECK][0m Checking Next.js Configuration
[0;34m[INFO][0m Next.js cache size: 285M
[0;32m[SUCCESS][0m Next.js cache appears healthy
[0;35m[HEALTH CHECK][0m Checking TypeScript Configuration
[0;34m[INFO][0m Running TypeScript type check...
[1;33m[WARNING][0m TypeScript compilation has 12 errors
[0;35m[HEALTH CHECK][0m Checking for Problematic Files
[1;33m[WARNING][0m Problematic files detected:       16 nested node_modules directories found
[0;35m[HEALTH CHECK][0m Checking VS Code Configuration
[0;34m[INFO][0m No VS Code configuration found
[0;35m[HEALTH CHECK][0m Checking Port Availability
[1;33m[WARNING][0m Ports in use: 4000
[0;35m[HEALTH CHECK][0m Calculating Overall Health Score
[0;34m[INFO][0m Health Score: 78% (4 healthy, 3 warnings, 0 errors)
[1;33m[WARNING][0m System has some issues but should work
[0;35m[HEALTH CHECK][0m Health Check Complete
[0;34m[INFO][0m Full report available at: /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/logs/health-report.json
[0;34m[INFO][0m Detailed logs available at: /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/logs/health-check.log

{"timestamp": "2025-06-06T17:20:18Z", "project": "CircuLab", "version": "0.1.0", "audit_version": "1.0.0", "summary": {"total_issues": 7, "critical_issues": 2, "important_issues": 2, "optimization_issues": 3}, "categories": {"configuration": {"status": "warning", "issues": [{"severity": "IMPORTANT", "title": "Security vulnerabilities detected", "description": "2 high/critical vulnerabilities found", "file": "package.json", "line": 0, "timestamp": "2025-06-06T17:20:21Z"}, {"severity": "CRITICAL", "title": "Build errors ignored", "description": "TypeScript/ESLint errors are ignored in production builds", "file": "next.config.ts", "line": 0, "timestamp": "2025-06-06T17:20:22Z"}]}, "dependencies": {"status": "error", "issues": [{"severity": "OPTIMIZATION", "title": "Outdated dependencies", "description": "11 packages have newer versions available", "file": "package.json", "line": 0, "timestamp": "2025-06-06T17:20:27Z"}, {"severity": "IMPORTANT", "title": "Missing package-lock.json", "description": "Dependency versions may be inconsistent across environments", "file": "package-lock.json", "line": 0, "timestamp": "2025-06-06T17:20:28Z"}, {"severity": "OPTIMIZATION", "title": "Duplicate dependencies", "description": "      17 nested node_modules directories found", "file": "node_modules", "line": 0, "timestamp": "2025-06-06T17:20:29Z"}, {"severity": "CRITICAL", "title": "Missing critical dependency", "description": "Critical dependency react is not installed", "file": "package.json", "line": 0, "timestamp": "2025-06-06T17:20:29Z"}]}, "architecture": {"status": "warning", "issues": [{"severity": "OPTIMIZATION", "title": "Large files detected", "description": "       5 files exceed 300 lines", "file": "src/", "line": 0, "timestamp": "2025-06-06T17:20:30Z"}]}, "imports": {"status": "unknown", "issues": []}, "typescript": {"status": "unknown", "issues": []}, "security": {"status": "unknown", "issues": []}}, "recommendations": [], "remediation_plan": []}
#!/bin/bash

# CircuLab Health Validator
# Script de validation continue de la santé du système

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
HEALTH_LOG="$PROJECT_ROOT/logs/health-validator.log"
VALIDATION_REPORT="$PROJECT_ROOT/logs/validation-report.json"
TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)

# Create necessary directories
mkdir -p "$PROJECT_ROOT/logs"

log_info() {
    echo -e "${BLUE}[VALIDATOR]${NC} $1" | tee -a "$HEALTH_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$HEALTH_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$HEALTH_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$HEALTH_LOG"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1" | tee -a "$HEALTH_LOG"
}

# Initialize validation report
init_validation_report() {
    cat > "$VALIDATION_REPORT" << EOF
{
  "timestamp": "$TIMESTAMP",
  "validation_version": "2.0.0",
  "status": "running",
  "overall_score": 0,
  "categories": {
    "build": { "score": 0, "status": "pending", "details": [] },
    "dependencies": { "score": 0, "status": "pending", "details": [] },
    "typescript": { "score": 0, "status": "pending", "details": [] },
    "performance": { "score": 0, "status": "pending", "details": [] },
    "security": { "score": 0, "status": "pending", "details": [] },
    "architecture": { "score": 0, "status": "pending", "details": [] }
  },
  "recommendations": [],
  "critical_issues": [],
  "performance_metrics": {}
}
EOF
}

# Update category score
update_category_score() {
    local category="$1"
    local score="$2"
    local status="$3"
    
    if command -v jq &> /dev/null; then
        local temp_file=$(mktemp)
        jq ".categories.$category.score = $score | .categories.$category.status = \"$status\"" "$VALIDATION_REPORT" > "$temp_file"
        mv "$temp_file" "$VALIDATION_REPORT"
    fi
}

# Add detail to category
add_category_detail() {
    local category="$1"
    local detail="$2"
    
    if command -v jq &> /dev/null; then
        local temp_file=$(mktemp)
        jq ".categories.$category.details += [\"$detail\"]" "$VALIDATION_REPORT" > "$temp_file"
        mv "$temp_file" "$VALIDATION_REPORT"
    fi
}

# Validate build system
validate_build() {
    log_section "1. Build System Validation"
    
    local score=0
    local max_score=100
    
    # Test clean build
    log_info "Testing clean build..."
    if npm run build > /tmp/build.log 2>&1; then
        log_success "Build successful"
        score=$((score + 40))
        add_category_detail "build" "Clean build successful"
    else
        log_error "Build failed"
        add_category_detail "build" "Build failed - check TypeScript/ESLint errors"
        local error_count=$(grep -c "Error:" /tmp/build.log 2>/dev/null || echo "0")
        add_category_detail "build" "Build errors: $error_count"
    fi
    
    # Test TypeScript compilation
    log_info "Testing TypeScript compilation..."
    if npx tsc --noEmit > /tmp/tsc.log 2>&1; then
        log_success "TypeScript compilation successful"
        score=$((score + 30))
        add_category_detail "build" "TypeScript compilation clean"
    else
        log_warning "TypeScript compilation issues"
        local ts_errors=$(grep -c "error TS" /tmp/tsc.log 2>/dev/null || echo "0")
        add_category_detail "build" "TypeScript errors: $ts_errors"
        if [ "$ts_errors" -eq 0 ]; then
            score=$((score + 20))
        fi
    fi
    
    # Test ESLint
    log_info "Testing ESLint..."
    if npx eslint src --ext .ts,.tsx > /tmp/eslint.log 2>&1; then
        log_success "ESLint validation passed"
        score=$((score + 30))
        add_category_detail "build" "ESLint validation clean"
    else
        log_warning "ESLint issues found"
        local eslint_errors=$(grep -c "error" /tmp/eslint.log 2>/dev/null || echo "0")
        local eslint_warnings=$(grep -c "warning" /tmp/eslint.log 2>/dev/null || echo "0")
        add_category_detail "build" "ESLint errors: $eslint_errors, warnings: $eslint_warnings"
        if [ "$eslint_errors" -eq 0 ]; then
            score=$((score + 20))
        fi
    fi
    
    local status="healthy"
    if [ "$score" -lt 60 ]; then
        status="error"
    elif [ "$score" -lt 80 ]; then
        status="warning"
    fi
    
    update_category_score "build" "$score" "$status"
    log_info "Build validation score: $score/$max_score"
}

# Validate dependencies
validate_dependencies() {
    log_section "2. Dependencies Validation"
    
    local score=0
    local max_score=100
    
    # Check for security vulnerabilities
    log_info "Checking security vulnerabilities..."
    if npm audit --audit-level=high > /tmp/audit.log 2>&1; then
        log_success "No high-severity vulnerabilities"
        score=$((score + 40))
        add_category_detail "dependencies" "No high-severity vulnerabilities"
    else
        local vuln_count=$(grep -c "high\|critical" /tmp/audit.log 2>/dev/null || echo "0")
        log_warning "$vuln_count high/critical vulnerabilities found"
        add_category_detail "dependencies" "High/critical vulnerabilities: $vuln_count"
        if [ "$vuln_count" -lt 3 ]; then
            score=$((score + 20))
        fi
    fi
    
    # Check for outdated packages
    log_info "Checking outdated packages..."
    if npm outdated > /tmp/outdated.log 2>&1; then
        log_success "All packages up to date"
        score=$((score + 30))
        add_category_detail "dependencies" "All packages up to date"
    else
        local outdated_count=$(wc -l < /tmp/outdated.log)
        if [ "$outdated_count" -gt 1 ]; then  # Header line
            log_info "$((outdated_count - 1)) packages have updates available"
            add_category_detail "dependencies" "Outdated packages: $((outdated_count - 1))"
            if [ "$outdated_count" -lt 10 ]; then
                score=$((score + 20))
            fi
        else
            score=$((score + 30))
        fi
    fi
    
    # Check package-lock.json
    if [ -f "package-lock.json" ]; then
        log_success "package-lock.json present"
        score=$((score + 30))
        add_category_detail "dependencies" "package-lock.json present"
    else
        log_warning "package-lock.json missing"
        add_category_detail "dependencies" "package-lock.json missing"
    fi
    
    local status="healthy"
    if [ "$score" -lt 60 ]; then
        status="error"
    elif [ "$score" -lt 80 ]; then
        status="warning"
    fi
    
    update_category_score "dependencies" "$score" "$status"
    log_info "Dependencies validation score: $score/$max_score"
}

# Validate performance
validate_performance() {
    log_section "3. Performance Validation"
    
    local score=0
    local max_score=100
    
    # Check bundle size
    log_info "Analyzing bundle size..."
    if [ -d ".next" ]; then
        local bundle_size=$(du -sh .next | cut -f1)
        log_info "Bundle size: $bundle_size"
        add_category_detail "performance" "Bundle size: $bundle_size"
        score=$((score + 30))
        
        # Check for large chunks
        local large_chunks=$(find .next -name "*.js" -size +1M | wc -l)
        if [ "$large_chunks" -eq 0 ]; then
            log_success "No large chunks detected"
            score=$((score + 20))
            add_category_detail "performance" "No large chunks (>1MB)"
        else
            log_warning "$large_chunks large chunks detected"
            add_category_detail "performance" "Large chunks detected: $large_chunks"
        fi
    else
        log_warning "No build output found"
        add_category_detail "performance" "No build output to analyze"
    fi
    
    # Check for performance anti-patterns
    log_info "Checking for performance anti-patterns..."
    local console_logs=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -c "console\." | awk -F: '{sum += $2} END {print sum}')
    if [ "$console_logs" -lt 10 ]; then
        log_success "Minimal console.log usage"
        score=$((score + 25))
        add_category_detail "performance" "Console logs: $console_logs"
    else
        log_warning "Excessive console.log usage: $console_logs"
        add_category_detail "performance" "Excessive console logs: $console_logs"
    fi
    
    # Check for unused imports
    local unused_imports=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -c "import.*{.*}" | awk -F: '{sum += $2} END {print sum}')
    if [ "$unused_imports" -lt 200 ]; then
        score=$((score + 25))
        add_category_detail "performance" "Import usage appears optimized"
    else
        add_category_detail "performance" "High import count - review for optimization"
    fi
    
    local status="healthy"
    if [ "$score" -lt 60 ]; then
        status="error"
    elif [ "$score" -lt 80 ]; then
        status="warning"
    fi
    
    update_category_score "performance" "$score" "$status"
    log_info "Performance validation score: $score/$max_score"
}

# Validate architecture
validate_architecture() {
    log_section "4. Architecture Validation"
    
    local score=0
    local max_score=100
    
    # Check folder structure
    local required_dirs=("src/app" "src/components" "src/lib" "src/store" "src/types")
    local missing_dirs=0
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            score=$((score + 15))
        else
            missing_dirs=$((missing_dirs + 1))
        fi
    done
    
    if [ "$missing_dirs" -eq 0 ]; then
        log_success "All required directories present"
        add_category_detail "architecture" "All required directories present"
    else
        log_warning "$missing_dirs required directories missing"
        add_category_detail "architecture" "Missing directories: $missing_dirs"
    fi
    
    # Check for large files
    local large_files=$(find src -name "*.ts" -o -name "*.tsx" | xargs wc -l | awk '$1 > 300 {print $2}' | wc -l)
    if [ "$large_files" -eq 0 ]; then
        log_success "No large files detected"
        score=$((score + 25))
        add_category_detail "architecture" "No files exceed 300 lines"
    else
        log_warning "$large_files files exceed 300 lines"
        add_category_detail "architecture" "Large files: $large_files"
        if [ "$large_files" -lt 5 ]; then
            score=$((score + 15))
        fi
    fi
    
    local status="healthy"
    if [ "$score" -lt 60 ]; then
        status="error"
    elif [ "$score" -lt 80 ]; then
        status="warning"
    fi
    
    update_category_score "architecture" "$score" "$status"
    log_info "Architecture validation score: $score/$max_score"
}

# Calculate overall score
calculate_overall_score() {
    log_section "5. Calculating Overall Score"
    
    if command -v jq &> /dev/null; then
        local build_score=$(jq -r '.categories.build.score' "$VALIDATION_REPORT")
        local deps_score=$(jq -r '.categories.dependencies.score' "$VALIDATION_REPORT")
        local perf_score=$(jq -r '.categories.performance.score' "$VALIDATION_REPORT")
        local arch_score=$(jq -r '.categories.architecture.score' "$VALIDATION_REPORT")
        
        # Weighted average (build and deps are more important)
        local overall_score=$(echo "scale=0; ($build_score * 0.3 + $deps_score * 0.3 + $perf_score * 0.2 + $arch_score * 0.2)" | bc -l)
        
        local temp_file=$(mktemp)
        jq ".overall_score = $overall_score | .status = \"completed\"" "$VALIDATION_REPORT" > "$temp_file"
        mv "$temp_file" "$VALIDATION_REPORT"
        
        log_info "Overall validation score: $overall_score/100"
        
        # Generate recommendations based on score
        if [ "$overall_score" -lt 70 ]; then
            echo "🚨 CRITICAL: System requires immediate attention" | tee -a "$HEALTH_LOG"
        elif [ "$overall_score" -lt 85 ]; then
            echo "⚠️ WARNING: System needs improvement" | tee -a "$HEALTH_LOG"
        else
            echo "✅ EXCELLENT: System is healthy" | tee -a "$HEALTH_LOG"
        fi
    fi
}

# Generate recommendations
generate_recommendations() {
    log_section "6. Generating Recommendations"
    
    local recommendations=()
    
    # Check each category and add specific recommendations
    if command -v jq &> /dev/null; then
        local build_score=$(jq -r '.categories.build.score' "$VALIDATION_REPORT")
        local deps_score=$(jq -r '.categories.dependencies.score' "$VALIDATION_REPORT")
        
        if [ "$build_score" -lt 80 ]; then
            recommendations+=("Fix TypeScript and ESLint errors to improve build reliability")
        fi
        
        if [ "$deps_score" -lt 80 ]; then
            recommendations+=("Update dependencies and fix security vulnerabilities")
        fi
        
        # Add recommendations to report
        for rec in "${recommendations[@]}"; do
            local temp_file=$(mktemp)
            jq --arg rec "$rec" '.recommendations += [$rec]' "$VALIDATION_REPORT" > "$temp_file"
            mv "$temp_file" "$VALIDATION_REPORT"
        done
    fi
}

# Main execution
main() {
    log_info "Starting CircuLab Health Validation"
    echo "Timestamp: $(date)" > "$HEALTH_LOG"
    
    init_validation_report
    
    # Run all validations
    validate_build
    validate_dependencies
    validate_performance
    validate_architecture
    
    # Calculate results
    calculate_overall_score
    generate_recommendations
    
    log_section "Validation Complete"
    log_info "Full report available at: $VALIDATION_REPORT"
    log_info "Detailed logs available at: $HEALTH_LOG"
    
    # Display summary
    echo ""
    echo "=== VALIDATION SUMMARY ==="
    if command -v jq &> /dev/null; then
        echo "Overall Score: $(jq -r '.overall_score' "$VALIDATION_REPORT")/100"
        echo "Build: $(jq -r '.categories.build.score' "$VALIDATION_REPORT")/100"
        echo "Dependencies: $(jq -r '.categories.dependencies.score' "$VALIDATION_REPORT")/100"
        echo "Performance: $(jq -r '.categories.performance.score' "$VALIDATION_REPORT")/100"
        echo "Architecture: $(jq -r '.categories.architecture.score' "$VALIDATION_REPORT")/100"
        echo ""
        echo "Report: $VALIDATION_REPORT"
    fi
}

main "$@"

#!/bin/bash

# CircuLab Development Health Check Script
# Diagnostic préventif pour détecter les problèmes avant qu'ils surviennent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/health-check.log"
HEALTH_REPORT="$PROJECT_ROOT/logs/health-report.json"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_header() {
    echo -e "${PURPLE}[HEALTH CHECK]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize health report
init_health_report() {
    cat > "$HEALTH_REPORT" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "checks": {},
  "overall_status": "unknown",
  "confidence": 0,
  "recommendations": []
}
EOF
}

# Update health report
update_health_report() {
    local check_name="$1"
    local status="$2"
    local message="$3"
    local details="$4"
    
    # Use jq to update the JSON file
    if command -v jq &> /dev/null; then
        jq --arg name "$check_name" \
           --arg status "$status" \
           --arg message "$message" \
           --arg details "$details" \
           '.checks[$name] = {
               "status": $status,
               "message": $message,
               "details": $details,
               "timestamp": now | strftime("%Y-%m-%dT%H:%M:%SZ")
           }' "$HEALTH_REPORT" > "$HEALTH_REPORT.tmp" && mv "$HEALTH_REPORT.tmp" "$HEALTH_REPORT"
    fi
}

# Check Node.js and npm versions
check_node_environment() {
    log_header "Checking Node.js Environment"
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        update_health_report "node_environment" "error" "Node.js not found" ""
        return 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    log_info "Node.js version: $node_version"
    log_info "npm version: $npm_version"
    
    # Check if Node.js version is compatible (>= 18)
    local node_major=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
    if [ "$node_major" -lt 18 ]; then
        log_warning "Node.js version $node_version is below recommended version 18"
        update_health_report "node_environment" "warning" "Node.js version below recommended" "$node_version"
    else
        log_success "Node.js environment is compatible"
        update_health_report "node_environment" "healthy" "Node.js environment compatible" "$node_version"
    fi
}

# Check package.json and dependencies
check_dependencies() {
    log_header "Checking Dependencies"
    
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        log_error "package.json not found"
        update_health_report "dependencies" "error" "package.json not found" ""
        return 1
    fi
    
    if [ ! -d "$PROJECT_ROOT/node_modules" ]; then
        log_warning "node_modules directory not found - dependencies not installed"
        update_health_report "dependencies" "warning" "Dependencies not installed" ""
        return 1
    fi
    
    # Check for package-lock.json
    if [ ! -f "$PROJECT_ROOT/package-lock.json" ]; then
        log_warning "package-lock.json not found - dependency versions may be inconsistent"
    fi
    
    # Check for critical dependencies
    local critical_deps=("next" "typescript" "zustand" "axios")
    local missing_deps=()

    for dep in "${critical_deps[@]}"; do
        if [ ! -d "$PROJECT_ROOT/node_modules/$dep" ]; then
            missing_deps+=("$dep")
        fi
    done

    # Special check for React (can be in Next.js compiled or standalone)
    if [ ! -d "$PROJECT_ROOT/node_modules/react" ] && [ ! -d "$PROJECT_ROOT/node_modules/next/dist/compiled/react" ]; then
        missing_deps+=("react")
    fi
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        log_success "All critical dependencies are installed"
        update_health_report "dependencies" "healthy" "All critical dependencies installed" ""
    else
        log_error "Missing critical dependencies: ${missing_deps[*]}"
        update_health_report "dependencies" "error" "Missing critical dependencies" "${missing_deps[*]}"
    fi
}

# Check Next.js configuration
check_nextjs_config() {
    log_header "Checking Next.js Configuration"
    
    # Check for Next.js cache issues
    if [ -d "$PROJECT_ROOT/.next" ]; then
        local cache_size=$(du -sh "$PROJECT_ROOT/.next" 2>/dev/null | cut -f1)
        log_info "Next.js cache size: $cache_size"
        
        # Check for corrupted cache (very large or very old)
        local cache_age=$(find "$PROJECT_ROOT/.next" -name "*.js" -mtime +7 | wc -l)
        if [ "$cache_age" -gt 100 ]; then
            log_warning "Next.js cache appears to be old (${cache_age} files older than 7 days)"
            update_health_report "nextjs_config" "warning" "Old cache detected" "Files older than 7 days: $cache_age"
        else
            log_success "Next.js cache appears healthy"
            update_health_report "nextjs_config" "healthy" "Next.js cache healthy" "Cache size: $cache_size"
        fi
    else
        log_info "No Next.js cache found (first build)"
        update_health_report "nextjs_config" "healthy" "No cache found - first build" ""
    fi
}

# Check TypeScript configuration
check_typescript() {
    log_header "Checking TypeScript Configuration"
    
    if [ ! -f "$PROJECT_ROOT/tsconfig.json" ]; then
        log_error "tsconfig.json not found"
        update_health_report "typescript" "error" "tsconfig.json not found" ""
        return 1
    fi
    
    # Try to compile TypeScript (dry run)
    if command -v npx &> /dev/null; then
        log_info "Running TypeScript type check..."
        if npx tsc --noEmit --skipLibCheck > /tmp/tsc-check.log 2>&1; then
            log_success "TypeScript compilation successful"
            update_health_report "typescript" "healthy" "TypeScript compilation successful" ""
        else
            local error_count=$(grep -c "error TS" /tmp/tsc-check.log 2>/dev/null || echo "0")
            log_warning "TypeScript compilation has $error_count errors"
            update_health_report "typescript" "warning" "TypeScript compilation errors" "Error count: $error_count"
        fi
    else
        log_warning "Cannot run TypeScript check - npx not available"
        update_health_report "typescript" "warning" "Cannot run TypeScript check" "npx not available"
    fi
}

# Check for common problematic files
check_problematic_files() {
    log_header "Checking for Problematic Files"
    
    local issues=()
    
    # Check for .DS_Store files
    local ds_store_count=$(find "$PROJECT_ROOT" -name ".DS_Store" 2>/dev/null | wc -l)
    if [ "$ds_store_count" -gt 0 ]; then
        issues+=("$ds_store_count .DS_Store files found")
    fi
    
    # Check for node_modules in subdirectories
    local nested_nm=$(find "$PROJECT_ROOT" -name "node_modules" -not -path "$PROJECT_ROOT/node_modules" 2>/dev/null | wc -l)
    if [ "$nested_nm" -gt 0 ]; then
        issues+=("$nested_nm nested node_modules directories found")
    fi
    
    # Check for large log files
    local large_logs=$(find "$PROJECT_ROOT" -name "*.log" -size +10M 2>/dev/null | wc -l)
    if [ "$large_logs" -gt 0 ]; then
        issues+=("$large_logs large log files (>10MB) found")
    fi
    
    if [ ${#issues[@]} -eq 0 ]; then
        log_success "No problematic files detected"
        update_health_report "problematic_files" "healthy" "No problematic files detected" ""
    else
        log_warning "Problematic files detected: ${issues[*]}"
        update_health_report "problematic_files" "warning" "Problematic files detected" "${issues[*]}"
    fi
}

# Check VS Code configuration
check_vscode_config() {
    log_header "Checking VS Code Configuration"
    
    if [ -d "$PROJECT_ROOT/.vscode" ]; then
        log_info "VS Code configuration found"
        
        # Check for settings.json
        if [ -f "$PROJECT_ROOT/.vscode/settings.json" ]; then
            log_info "VS Code settings.json found"
            
            # Check for problematic settings
            if grep -q "typescript.preferences.includePackageJsonAutoImports" "$PROJECT_ROOT/.vscode/settings.json" 2>/dev/null; then
                log_info "TypeScript auto-import settings configured"
            fi
        fi
        
        # Check for extensions.json
        if [ -f "$PROJECT_ROOT/.vscode/extensions.json" ]; then
            log_info "VS Code extensions.json found"
            local recommended_count=$(jq '.recommendations | length' "$PROJECT_ROOT/.vscode/extensions.json" 2>/dev/null || echo "0")
            log_info "Recommended extensions: $recommended_count"
        fi
        
        update_health_report "vscode_config" "healthy" "VS Code configuration found" ""
    else
        log_info "No VS Code configuration found"
        update_health_report "vscode_config" "healthy" "No VS Code configuration" ""
    fi
}

# Check port availability
check_ports() {
    log_header "Checking Port Availability"
    
    local ports=(3000 4000)
    local busy_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -ti:$port >/dev/null 2>&1; then
            busy_ports+=("$port")
        fi
    done
    
    if [ ${#busy_ports[@]} -eq 0 ]; then
        log_success "All required ports are available"
        update_health_report "ports" "healthy" "All required ports available" ""
    else
        log_warning "Ports in use: ${busy_ports[*]}"
        update_health_report "ports" "warning" "Some ports are busy" "${busy_ports[*]}"
    fi
}

# Calculate overall health score
calculate_overall_health() {
    log_header "Calculating Overall Health Score"
    
    if command -v jq &> /dev/null; then
        local healthy_count=$(jq '[.checks[] | select(.status == "healthy")] | length' "$HEALTH_REPORT")
        local warning_count=$(jq '[.checks[] | select(.status == "warning")] | length' "$HEALTH_REPORT")
        local error_count=$(jq '[.checks[] | select(.status == "error")] | length' "$HEALTH_REPORT")
        local total_count=$(jq '.checks | length' "$HEALTH_REPORT")
        
        if [ "$total_count" -gt 0 ]; then
            local confidence=$(( (healthy_count * 100 + warning_count * 50) / total_count ))
            
            local overall_status="healthy"
            if [ "$error_count" -gt 0 ]; then
                overall_status="error"
            elif [ "$warning_count" -gt 0 ]; then
                overall_status="warning"
            fi
            
            # Update the report
            jq --arg status "$overall_status" \
               --argjson confidence "$confidence" \
               '.overall_status = $status | .confidence = $confidence' \
               "$HEALTH_REPORT" > "$HEALTH_REPORT.tmp" && mv "$HEALTH_REPORT.tmp" "$HEALTH_REPORT"
            
            log_info "Health Score: $confidence% ($healthy_count healthy, $warning_count warnings, $error_count errors)"
            
            if [ "$confidence" -ge 80 ]; then
                log_success "System is ready for development!"
            elif [ "$confidence" -ge 60 ]; then
                log_warning "System has some issues but should work"
            else
                log_error "System has significant issues that should be addressed"
            fi
        fi
    fi
}

# Generate recommendations
generate_recommendations() {
    if command -v jq &> /dev/null; then
        local recommendations=()
        
        # Check each failed check and add recommendations
        local node_status=$(jq -r '.checks.node_environment.status // "unknown"' "$HEALTH_REPORT")
        if [ "$node_status" = "error" ] || [ "$node_status" = "warning" ]; then
            recommendations+=("Update Node.js to version 18 or higher")
        fi
        
        local deps_status=$(jq -r '.checks.dependencies.status // "unknown"' "$HEALTH_REPORT")
        if [ "$deps_status" = "error" ]; then
            recommendations+=("Run 'npm install' to install missing dependencies")
        fi
        
        local nextjs_status=$(jq -r '.checks.nextjs_config.status // "unknown"' "$HEALTH_REPORT")
        if [ "$nextjs_status" = "warning" ]; then
            recommendations+=("Clear Next.js cache with 'rm -rf .next'")
        fi
        
        local ts_status=$(jq -r '.checks.typescript.status // "unknown"' "$HEALTH_REPORT")
        if [ "$ts_status" = "warning" ]; then
            recommendations+=("Fix TypeScript errors before proceeding")
        fi
        
        # Update recommendations in the report
        if [ ${#recommendations[@]} -gt 0 ]; then
            local rec_json=$(printf '%s\n' "${recommendations[@]}" | jq -R . | jq -s .)
            jq --argjson recs "$rec_json" '.recommendations = $recs' "$HEALTH_REPORT" > "$HEALTH_REPORT.tmp" && mv "$HEALTH_REPORT.tmp" "$HEALTH_REPORT"
        fi
    fi
}

# Main execution
main() {
    log_header "Starting CircuLab Development Health Check"
    echo "Timestamp: $(date)" > "$LOG_FILE"
    
    init_health_report
    
    # Run all checks
    check_node_environment
    check_dependencies
    check_nextjs_config
    check_typescript
    check_problematic_files
    check_vscode_config
    check_ports
    
    # Calculate results
    calculate_overall_health
    generate_recommendations
    
    log_header "Health Check Complete"
    log_info "Full report available at: $HEALTH_REPORT"
    log_info "Detailed logs available at: $LOG_FILE"
    
    # Display summary
    if command -v jq &> /dev/null; then
        echo ""
        echo "=== HEALTH SUMMARY ==="
        jq -r '"Overall Status: " + .overall_status' "$HEALTH_REPORT"
        jq -r '"Confidence: " + (.confidence | tostring) + "%"' "$HEALTH_REPORT"
        
        local rec_count=$(jq '.recommendations | length' "$HEALTH_REPORT")
        if [ "$rec_count" -gt 0 ]; then
            echo ""
            echo "Recommendations:"
            jq -r '.recommendations[] | "  • " + .' "$HEALTH_REPORT"
        fi
    fi
}

# Run the health check
main "$@"

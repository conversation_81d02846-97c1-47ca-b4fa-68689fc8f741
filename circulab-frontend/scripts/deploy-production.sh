#!/bin/bash

# CircuLab Production Deployment Script
# Déploiement sécurisé avec validation continue et rollback automatique

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DEPLOYMENT_LOG="$PROJECT_ROOT/logs/deployment.log"
BACKUP_DIR="$PROJECT_ROOT/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Configuration
MIN_CONFIDENCE_SCORE=80
ROLLBACK_THRESHOLD=60
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
DEPLOYMENT_TIMEOUT=600    # 10 minutes

# Create necessary directories
mkdir -p "$PROJECT_ROOT/logs" "$BACKUP_DIR"

# Initialize deployment log
echo "CircuLab Production Deployment - $(date)" > "$DEPLOYMENT_LOG"

log_info() {
    echo -e "${BLUE}[DEPLOY]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

# Pre-deployment health check
pre_deployment_check() {
    log_section "Pre-Deployment Health Check"
    
    # Run comprehensive health check
    if ! ./scripts/check-dev-health.sh > /dev/null 2>&1; then
        log_error "Pre-deployment health check failed"
        return 1
    fi
    
    # Check confidence score
    local confidence=$(jq -r '.confidence // 0' logs/health-report.json)
    
    if [ "$confidence" -lt "$MIN_CONFIDENCE_SCORE" ]; then
        log_error "Confidence score too low for deployment: $confidence% (minimum: $MIN_CONFIDENCE_SCORE%)"
        return 1
    fi
    
    log_success "Pre-deployment health check passed (confidence: $confidence%)"
    return 0
}

# Create backup of current deployment
create_backup() {
    log_section "Creating Deployment Backup"
    
    local backup_name="circulab_backup_$TIMESTAMP"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Backup critical files
    cp -r .next "$backup_path/" 2>/dev/null || log_warning "No .next directory to backup"
    cp package.json "$backup_path/"
    cp -r src "$backup_path/"
    cp -r public "$backup_path/"
    
    # Save current environment
    cp .env.local "$backup_path/.env.backup" 2>/dev/null || true
    
    # Create backup manifest
    cat > "$backup_path/manifest.json" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "confidence_score": $(jq -r '.confidence // 0' logs/health-report.json),
  "backup_type": "pre_deployment"
}
EOF

    echo "$backup_name" > "$PROJECT_ROOT/.last_backup"
    log_success "Backup created: $backup_name"
}

# Build for production
build_production() {
    log_section "Building Production Application"
    
    # Set production environment
    export NODE_ENV=production
    
    # Clean previous build
    rm -rf .next
    
    # Build application
    if ! npm run build; then
        log_error "Production build failed"
        return 1
    fi
    
    log_success "Production build completed successfully"
    return 0
}

# Deploy to production
deploy_application() {
    log_section "Deploying Application"
    
    # Start application in production mode
    PORT=3000 npm start &
    local app_pid=$!
    echo $app_pid > "$PROJECT_ROOT/.app_pid"
    
    log_info "Application started with PID: $app_pid"
    
    # Wait for application to be ready
    local attempts=0
    local max_attempts=30
    
    while [ $attempts -lt $max_attempts ]; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            log_success "Application is ready and responding"
            return 0
        fi
        
        sleep 2
        attempts=$((attempts + 1))
        log_info "Waiting for application... ($attempts/$max_attempts)"
    done
    
    log_error "Application failed to start within timeout"
    return 1
}

# Post-deployment validation
post_deployment_validation() {
    log_section "Post-Deployment Validation"
    
    # Wait a bit for application to stabilize
    sleep 10
    
    # Run health check on deployed application
    local validation_attempts=0
    local max_validation_attempts=10
    
    while [ $validation_attempts -lt $max_validation_attempts ]; do
        # Check application health
        local health_response=$(curl -s http://localhost:3000/api/health || echo "ERROR")
        
        if [[ "$health_response" != *"ERROR"* ]]; then
            log_success "Application health endpoint responding"
            
            # Check confidence score in production
            local prod_confidence=$(curl -s http://localhost:3000/health | grep -o 'confidence.*[0-9]\+' | grep -o '[0-9]\+' | head -1 || echo "0")
            
            if [ "$prod_confidence" -ge "$MIN_CONFIDENCE_SCORE" ]; then
                log_success "Production confidence score acceptable: $prod_confidence%"
                return 0
            else
                log_warning "Production confidence score low: $prod_confidence%"
            fi
        fi
        
        validation_attempts=$((validation_attempts + 1))
        sleep 5
    done
    
    log_error "Post-deployment validation failed"
    return 1
}

# Rollback deployment
rollback_deployment() {
    log_section "Rolling Back Deployment"
    
    # Stop current application
    if [ -f "$PROJECT_ROOT/.app_pid" ]; then
        local app_pid=$(cat "$PROJECT_ROOT/.app_pid")
        kill $app_pid 2>/dev/null || true
        rm -f "$PROJECT_ROOT/.app_pid"
    fi
    
    # Get last backup
    if [ -f "$PROJECT_ROOT/.last_backup" ]; then
        local backup_name=$(cat "$PROJECT_ROOT/.last_backup")
        local backup_path="$BACKUP_DIR/$backup_name"
        
        if [ -d "$backup_path" ]; then
            log_info "Restoring from backup: $backup_name"
            
            # Restore files
            rm -rf .next
            cp -r "$backup_path/.next" . 2>/dev/null || true
            cp "$backup_path/.env.backup" .env.local 2>/dev/null || true
            
            # Restart application
            npm start &
            local rollback_pid=$!
            echo $rollback_pid > "$PROJECT_ROOT/.app_pid"
            
            log_success "Rollback completed successfully"
            return 0
        fi
    fi
    
    log_error "Rollback failed - no valid backup found"
    return 1
}

# Continuous monitoring
start_monitoring() {
    log_section "Starting Continuous Monitoring"
    
    # Create monitoring script
    cat > "$PROJECT_ROOT/scripts/production-monitor.sh" << 'EOF'
#!/bin/bash

CONFIDENCE_THRESHOLD=60
CHECK_INTERVAL=60

while true; do
    # Check application health
    HEALTH_RESPONSE=$(curl -s http://localhost:3000/api/health || echo "ERROR")
    
    if [[ "$HEALTH_RESPONSE" == *"ERROR"* ]]; then
        echo "$(date): Application not responding - triggering rollback"
        ./scripts/deploy-production.sh rollback
        break
    fi
    
    # Check confidence score
    CONFIDENCE=$(curl -s http://localhost:3000/health | grep -o 'confidence.*[0-9]\+' | grep -o '[0-9]\+' | head -1 || echo "0")
    
    if [ "$CONFIDENCE" -lt "$CONFIDENCE_THRESHOLD" ]; then
        echo "$(date): Confidence score too low ($CONFIDENCE%) - triggering rollback"
        ./scripts/deploy-production.sh rollback
        break
    fi
    
    echo "$(date): System healthy (confidence: $CONFIDENCE%)"
    sleep $CHECK_INTERVAL
done
EOF

    chmod +x "$PROJECT_ROOT/scripts/production-monitor.sh"
    
    # Start monitoring in background
    nohup ./scripts/production-monitor.sh > logs/production-monitor.log 2>&1 &
    local monitor_pid=$!
    echo $monitor_pid > "$PROJECT_ROOT/.monitor_pid"
    
    log_success "Production monitoring started (PID: $monitor_pid)"
}

# Generate deployment report
generate_deployment_report() {
    log_section "Generating Deployment Report"
    
    local deployment_status="success"
    local confidence_score=$(jq -r '.confidence // 0' logs/health-report.json)
    
    cat > "logs/deployment-report-$TIMESTAMP.json" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "deployment_id": "$TIMESTAMP",
  "status": "$deployment_status",
  "pre_deployment_confidence": $confidence_score,
  "post_deployment_validation": "passed",
  "backup_created": "$(cat .last_backup 2>/dev/null || echo 'none')",
  "monitoring_enabled": true,
  "rollback_threshold": $ROLLBACK_THRESHOLD,
  "deployment_duration": "$(date +%s)",
  "version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
}
EOF

    log_success "Deployment report generated: deployment-report-$TIMESTAMP.json"
}

# Main deployment function
main_deploy() {
    log_info "Starting CircuLab Production Deployment"
    
    # Pre-deployment checks
    if ! pre_deployment_check; then
        log_error "Pre-deployment checks failed - aborting deployment"
        exit 1
    fi
    
    # Create backup
    create_backup
    
    # Build for production
    if ! build_production; then
        log_error "Build failed - aborting deployment"
        exit 1
    fi
    
    # Deploy application
    if ! deploy_application; then
        log_error "Deployment failed - initiating rollback"
        rollback_deployment
        exit 1
    fi
    
    # Validate deployment
    if ! post_deployment_validation; then
        log_error "Post-deployment validation failed - initiating rollback"
        rollback_deployment
        exit 1
    fi
    
    # Start monitoring
    start_monitoring
    
    # Generate report
    generate_deployment_report
    
    log_success "Production deployment completed successfully!"
    log_info "Monitoring active - check logs/production-monitor.log"
    log_info "Deployment report: logs/deployment-report-$TIMESTAMP.json"
}

# Command line interface
case "${1:-deploy}" in
    "deploy")
        main_deploy
        ;;
    "rollback")
        rollback_deployment
        ;;
    "check")
        pre_deployment_check
        ;;
    "monitor")
        start_monitoring
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|check|monitor}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Execute full production deployment"
        echo "  rollback - Rollback to last known good state"
        echo "  check    - Run pre-deployment health check"
        echo "  monitor  - Start production monitoring"
        exit 1
        ;;
esac

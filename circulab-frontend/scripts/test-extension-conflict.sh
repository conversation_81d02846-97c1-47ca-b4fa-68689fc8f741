#!/bin/bash

# Script de Test - Simulation de Conflit d'Extension
# Simule les problèmes causés par l'extension IA AUGMENTE

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/extension-test.log"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

log_info() {
    echo -e "${BLUE}[TEST]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Test 1: Simulate extension conflict by modifying DOM
simulate_extension_conflict() {
    log_info "Simulating extension conflict..."
    
    # Create a temporary file that simulates extension injection
    cat > "$PROJECT_ROOT/public/extension-conflict.js" << 'EOF'
// Simulation d'injection d'extension problématique
(function() {
    // Simulate IA AUGMENTE extension behavior
    if (typeof window !== 'undefined') {
        // Add marker for detection
        document.documentElement.setAttribute('data-augment', 'true');
        
        // Simulate module resolution interference
        const originalRequire = window.require;
        window.require = function(module) {
            console.warn('Extension intercepting module:', module);
            if (originalRequire) {
                return originalRequire.apply(this, arguments);
            }
            throw new Error('Module resolution blocked by extension');
        };
        
        // Simulate webpack interference
        if (window.webpackChunkName) {
            console.error('Extension interfering with webpack chunks');
        }
        
        console.log('IA AUGMENTE extension simulation active');
    }
})();
EOF

    log_success "Extension conflict simulation file created"
}

# Test 2: Inject the conflict script into the application
inject_conflict_script() {
    log_info "Injecting conflict script into application..."
    
    # Backup original layout
    cp "$PROJECT_ROOT/src/app/layout.tsx" "$PROJECT_ROOT/src/app/layout.tsx.backup"
    
    # Add script injection to layout
    cat > "$PROJECT_ROOT/src/app/layout.tsx.temp" << 'EOF'
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Suspense } from "react";
import ErrorBoundary from "@/components/ErrorBoundary";
import { SafeAuthProvider } from "@/lib/safeImports";
import Script from "next/script";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "CircuLab - Industrial Waste Valorization Platform",
  description: "Comprehensive platform for managing and valorizing industrial waste materials through intelligent matching and processing tracking.",
};

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <Script src="/extension-conflict.js" strategy="beforeInteractive" />
        <ErrorBoundary>
          <Suspense fallback={<LoadingFallback />}>
            <SafeAuthProvider>
              {children}
            </SafeAuthProvider>
          </Suspense>
        </ErrorBoundary>
      </body>
    </html>
  );
}
EOF

    mv "$PROJECT_ROOT/src/app/layout.tsx.temp" "$PROJECT_ROOT/src/app/layout.tsx"
    log_success "Conflict script injected into layout"
}

# Test 3: Monitor health dashboard response
monitor_health_response() {
    log_info "Monitoring health dashboard response..."
    
    # Wait for application to restart
    sleep 5
    
    # Check health endpoint
    local health_response=$(curl -s http://localhost:8080/api/health || echo "ERROR")
    
    if [[ "$health_response" == *"ERROR"* ]]; then
        log_error "Health endpoint not responding"
    else
        log_success "Health endpoint responding: $health_response"
    fi
    
    # Check if extension conflict is detected
    local extension_check=$(curl -s http://localhost:8080/health | grep -o "data-augment" || echo "NOT_DETECTED")
    
    if [[ "$extension_check" == "data-augment" ]]; then
        log_success "Extension conflict marker detected in DOM"
    else
        log_warning "Extension conflict marker not detected"
    fi
}

# Test 4: Verify error logging
verify_error_logging() {
    log_info "Verifying error logging system..."
    
    # Check if error logs are being generated
    if [ -f "$PROJECT_ROOT/logs/health-check.log" ]; then
        local error_count=$(grep -c "ERROR" "$PROJECT_ROOT/logs/health-check.log" 2>/dev/null || echo "0")
        log_info "Found $error_count errors in health check logs"
    else
        log_warning "No health check logs found"
    fi
}

# Test 5: Restore original state
restore_original_state() {
    log_info "Restoring original application state..."
    
    # Remove conflict script
    rm -f "$PROJECT_ROOT/public/extension-conflict.js"
    
    # Restore original layout
    if [ -f "$PROJECT_ROOT/src/app/layout.tsx.backup" ]; then
        mv "$PROJECT_ROOT/src/app/layout.tsx.backup" "$PROJECT_ROOT/src/app/layout.tsx"
        log_success "Original layout restored"
    else
        log_warning "No backup layout found"
    fi
}

# Main test execution
main() {
    log_info "Starting Extension Conflict Test Suite"
    echo "Timestamp: $(date)" > "$LOG_FILE"
    
    echo ""
    echo "=== PHASE 1: SETUP ==="
    simulate_extension_conflict
    inject_conflict_script
    
    echo ""
    echo "=== PHASE 2: MONITORING ==="
    monitor_health_response
    verify_error_logging
    
    echo ""
    echo "=== PHASE 3: CLEANUP ==="
    restore_original_state
    
    echo ""
    echo "=== TEST SUMMARY ==="
    log_info "Extension conflict test completed"
    log_info "Check the health dashboard at http://localhost:8080/health"
    log_info "Full test logs available at: $LOG_FILE"
    
    echo ""
    echo "Next steps:"
    echo "1. Open http://localhost:8080/health in your browser"
    echo "2. Observe the Extensions status (should show warning/error)"
    echo "3. Check the recommendations section"
    echo "4. Verify that the application still functions despite the conflict"
}

# Parse command line arguments
case "${1:-run}" in
    "run")
        main
        ;;
    "setup")
        simulate_extension_conflict
        inject_conflict_script
        ;;
    "monitor")
        monitor_health_response
        verify_error_logging
        ;;
    "cleanup")
        restore_original_state
        ;;
    *)
        echo "Usage: $0 {run|setup|monitor|cleanup}"
        echo ""
        echo "Commands:"
        echo "  run     - Execute full test suite"
        echo "  setup   - Setup conflict simulation only"
        echo "  monitor - Monitor health response only"
        echo "  cleanup - Restore original state only"
        exit 1
        ;;
esac

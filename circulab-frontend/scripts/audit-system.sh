#!/bin/bash

# CircuLab System Audit Script
# Audit complet et systémique du projet CircuLab

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
AUDIT_REPORT="$PROJECT_ROOT/logs/audit-report.json"
AUDIT_LOG="$PROJECT_ROOT/logs/audit.log"
TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)

# Create necessary directories
mkdir -p "$PROJECT_ROOT/logs"

log_info() {
    echo -e "${BLUE}[AUDIT]${NC} $1" | tee -a "$AUDIT_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$AUDIT_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$AUDIT_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$AUDIT_LOG"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1" | tee -a "$AUDIT_LOG"
}

# Initialize audit report
init_audit_report() {
    cat > "$AUDIT_REPORT" << EOF
{
  "timestamp": "$TIMESTAMP",
  "project": "CircuLab",
  "version": "0.1.0",
  "audit_version": "1.0.0",
  "summary": {
    "total_issues": 0,
    "critical_issues": 0,
    "important_issues": 0,
    "optimization_issues": 0
  },
  "categories": {
    "configuration": {
      "status": "unknown",
      "issues": []
    },
    "dependencies": {
      "status": "unknown", 
      "issues": []
    },
    "architecture": {
      "status": "unknown",
      "issues": []
    },
    "imports": {
      "status": "unknown",
      "issues": []
    },
    "typescript": {
      "status": "unknown",
      "issues": []
    },
    "security": {
      "status": "unknown",
      "issues": []
    }
  },
  "recommendations": [],
  "remediation_plan": []
}
EOF
}

# Add issue to audit report
add_issue() {
    local category="$1"
    local severity="$2"
    local title="$3"
    local description="$4"
    local file="$5"
    local line="$6"
    
    local issue=$(cat << EOF
{
  "severity": "$severity",
  "title": "$title", 
  "description": "$description",
  "file": "$file",
  "line": $line,
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
)
    
    # Add to report using jq
    if command -v jq &> /dev/null; then
        local temp_file=$(mktemp)
        jq ".categories.$category.issues += [$issue]" "$AUDIT_REPORT" > "$temp_file"
        mv "$temp_file" "$AUDIT_REPORT"
        
        # Update counters
        jq ".summary.total_issues += 1" "$AUDIT_REPORT" > "$temp_file"
        mv "$temp_file" "$AUDIT_REPORT"
        
        case "$severity" in
            "CRITICAL")
                jq ".summary.critical_issues += 1" "$AUDIT_REPORT" > "$temp_file"
                mv "$temp_file" "$AUDIT_REPORT"
                ;;
            "IMPORTANT")
                jq ".summary.important_issues += 1" "$AUDIT_REPORT" > "$temp_file"
                mv "$temp_file" "$AUDIT_REPORT"
                ;;
            "OPTIMIZATION")
                jq ".summary.optimization_issues += 1" "$AUDIT_REPORT" > "$temp_file"
                mv "$temp_file" "$AUDIT_REPORT"
                ;;
        esac
    fi
}

# Audit configuration files
audit_configuration() {
    log_section "1. Configuration Files Audit"
    
    local issues_found=0
    
    # Check package.json
    if [ ! -f "package.json" ]; then
        add_issue "configuration" "CRITICAL" "Missing package.json" "Project root package.json not found" "package.json" 0
        issues_found=$((issues_found + 1))
    else
        log_success "package.json found"
        
        # Check for security vulnerabilities in dependencies
        if command -v npm &> /dev/null; then
            if npm audit --audit-level=high > /tmp/npm-audit.log 2>&1; then
                log_success "No high-severity vulnerabilities found"
            else
                local vuln_count=$(grep -c "high\|critical" /tmp/npm-audit.log 2>/dev/null || echo "0")
                if [ "$vuln_count" -gt 0 ]; then
                    add_issue "configuration" "IMPORTANT" "Security vulnerabilities detected" "$vuln_count high/critical vulnerabilities found" "package.json" 0
                    issues_found=$((issues_found + 1))
                fi
            fi
        fi
    fi
    
    # Check tsconfig.json
    if [ ! -f "tsconfig.json" ]; then
        add_issue "configuration" "CRITICAL" "Missing tsconfig.json" "TypeScript configuration not found" "tsconfig.json" 0
        issues_found=$((issues_found + 1))
    else
        log_success "tsconfig.json found"
        
        # Check for problematic TypeScript settings
        if grep -q '"strict": false' tsconfig.json; then
            add_issue "configuration" "IMPORTANT" "TypeScript strict mode disabled" "Strict mode should be enabled for better type safety" "tsconfig.json" 0
            issues_found=$((issues_found + 1))
        fi
        
        if grep -q '"skipLibCheck": false' tsconfig.json; then
            add_issue "configuration" "OPTIMIZATION" "skipLibCheck disabled" "Consider enabling skipLibCheck for faster builds" "tsconfig.json" 0
            issues_found=$((issues_found + 1))
        fi
    fi
    
    # Check next.config.ts
    if [ ! -f "next.config.ts" ] && [ ! -f "next.config.js" ]; then
        add_issue "configuration" "IMPORTANT" "Missing Next.js config" "Next.js configuration file not found" "next.config.ts" 0
        issues_found=$((issues_found + 1))
    else
        log_success "Next.js config found"
        
        # Check for production optimizations
        if [ -f "next.config.ts" ]; then
            if grep -q "ignoreBuildErrors: true" next.config.ts; then
                add_issue "configuration" "CRITICAL" "Build errors ignored" "TypeScript/ESLint errors are ignored in production builds" "next.config.ts" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Update category status
    if [ $issues_found -eq 0 ]; then
        jq '.categories.configuration.status = "healthy"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    elif [ $issues_found -le 2 ]; then
        jq '.categories.configuration.status = "warning"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    else
        jq '.categories.configuration.status = "error"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    fi
}

# Audit dependencies
audit_dependencies() {
    log_section "2. Dependencies Audit"
    
    local issues_found=0
    
    # Check for outdated dependencies
    if command -v npm &> /dev/null; then
        if npm outdated > /tmp/outdated.log 2>&1; then
            log_success "All dependencies are up to date"
        else
            local outdated_count=$(wc -l < /tmp/outdated.log)
            if [ "$outdated_count" -gt 1 ]; then  # Header line doesn't count
                add_issue "dependencies" "OPTIMIZATION" "Outdated dependencies" "$((outdated_count - 1)) packages have newer versions available" "package.json" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Check for missing package-lock.json
    if [ ! -f "package-lock.json" ]; then
        add_issue "dependencies" "IMPORTANT" "Missing package-lock.json" "Dependency versions may be inconsistent across environments" "package-lock.json" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Check for duplicate dependencies
    if [ -d "node_modules" ]; then
        local duplicate_count=$(find node_modules -name "node_modules" | wc -l)
        if [ "$duplicate_count" -gt 0 ]; then
            add_issue "dependencies" "OPTIMIZATION" "Duplicate dependencies" "$duplicate_count nested node_modules directories found" "node_modules" 0
            issues_found=$((issues_found + 1))
        fi
    fi
    
    # Check critical dependencies
    local critical_deps=("next" "react" "typescript" "zustand" "axios")
    for dep in "${critical_deps[@]}"; do
        if [ ! -d "node_modules/$dep" ]; then
            add_issue "dependencies" "CRITICAL" "Missing critical dependency" "Critical dependency $dep is not installed" "package.json" 0
            issues_found=$((issues_found + 1))
        fi
    done
    
    # Update category status
    if [ $issues_found -eq 0 ]; then
        jq '.categories.dependencies.status = "healthy"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    elif [ $issues_found -le 2 ]; then
        jq '.categories.dependencies.status = "warning"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    else
        jq '.categories.dependencies.status = "error"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    fi
}

# Audit architecture
audit_architecture() {
    log_section "3. Architecture Audit"
    
    local issues_found=0
    
    # Check folder structure
    local required_dirs=("src/app" "src/components" "src/lib" "src/store" "src/types")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            add_issue "architecture" "IMPORTANT" "Missing directory" "Required directory $dir not found" "$dir" 0
            issues_found=$((issues_found + 1))
        fi
    done
    
    # Check for proper separation of concerns
    if [ -d "src/components" ]; then
        local component_files=$(find src/components -name "*.tsx" | wc -l)
        local logic_in_components=$(find src/components -name "*.tsx" -exec grep -l "useState.*useEffect.*axios\|fetch" {} \; | wc -l)
        
        if [ "$logic_in_components" -gt 0 ] && [ "$component_files" -gt 0 ]; then
            local percentage=$((logic_in_components * 100 / component_files))
            if [ "$percentage" -gt 30 ]; then
                add_issue "architecture" "IMPORTANT" "Business logic in components" "$logic_in_components/$component_files components contain business logic" "src/components" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Check for large files
    local large_files=$(find src -name "*.ts" -o -name "*.tsx" | xargs wc -l | awk '$1 > 300 {print $2}' | wc -l)
    if [ "$large_files" -gt 0 ]; then
        add_issue "architecture" "OPTIMIZATION" "Large files detected" "$large_files files exceed 300 lines" "src/" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Check for proper error boundaries
    if [ ! -f "src/components/ErrorBoundary.tsx" ]; then
        add_issue "architecture" "IMPORTANT" "Missing error boundary" "No error boundary component found" "src/components/ErrorBoundary.tsx" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Update category status
    if [ $issues_found -eq 0 ]; then
        jq '.categories.architecture.status = "healthy"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    elif [ $issues_found -le 2 ]; then
        jq '.categories.architecture.status = "warning"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    else
        jq '.categories.architecture.status = "error"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    fi
}

# Audit imports
audit_imports() {
    log_section "4. Imports Audit"
    
    local issues_found=0
    
    # Check for circular imports
    if command -v npx &> /dev/null; then
        if npx madge --circular --extensions ts,tsx src > /tmp/circular.log 2>&1; then
            local circular_count=$(grep -c "Circular dependency" /tmp/circular.log 2>/dev/null || echo "0")
            if [ "$circular_count" -gt 0 ]; then
                add_issue "imports" "CRITICAL" "Circular imports detected" "$circular_count circular dependencies found" "src/" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Check for relative imports going up multiple levels
    local deep_relative=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "import.*from.*\.\./\.\./\.\." | wc -l)
    if [ "$deep_relative" -gt 0 ]; then
        add_issue "imports" "OPTIMIZATION" "Deep relative imports" "$deep_relative files use deep relative imports (../../../)" "src/" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Check for unused imports
    if command -v npx &> /dev/null; then
        if npx ts-unused-exports tsconfig.json > /tmp/unused.log 2>&1; then
            local unused_count=$(wc -l < /tmp/unused.log)
            if [ "$unused_count" -gt 0 ]; then
                add_issue "imports" "OPTIMIZATION" "Unused exports" "$unused_count unused exports found" "src/" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Update category status
    if [ $issues_found -eq 0 ]; then
        jq '.categories.imports.status = "healthy"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    elif [ $issues_found -le 1 ]; then
        jq '.categories.imports.status = "warning"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    else
        jq '.categories.imports.status = "error"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    fi
}

# Audit TypeScript
audit_typescript() {
    log_section "5. TypeScript Audit"
    
    local issues_found=0
    
    # Run TypeScript compiler check
    if command -v npx &> /dev/null; then
        if npx tsc --noEmit --skipLibCheck > /tmp/tsc.log 2>&1; then
            log_success "TypeScript compilation successful"
        else
            local error_count=$(grep -c "error TS" /tmp/tsc.log 2>/dev/null || echo "0")
            if [ "$error_count" -gt 0 ]; then
                add_issue "typescript" "CRITICAL" "TypeScript compilation errors" "$error_count TypeScript errors found" "src/" 0
                issues_found=$((issues_found + 1))
            fi
        fi
    fi
    
    # Check for any types
    local any_count=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -c ": any\|as any" | awk -F: '{sum += $2} END {print sum}')
    if [ "$any_count" -gt 10 ]; then
        add_issue "typescript" "OPTIMIZATION" "Excessive use of any type" "$any_count occurrences of 'any' type found" "src/" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Check for missing type definitions
    local untyped_files=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "Function\|Object\|Array" | wc -l)
    if [ "$untyped_files" -gt 5 ]; then
        add_issue "typescript" "OPTIMIZATION" "Generic types usage" "$untyped_files files use generic types (Function, Object, Array)" "src/" 0
        issues_found=$((issues_found + 1))
    fi
    
    # Update category status
    if [ $issues_found -eq 0 ]; then
        jq '.categories.typescript.status = "healthy"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    elif [ $issues_found -le 1 ]; then
        jq '.categories.typescript.status = "warning"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    else
        jq '.categories.typescript.status = "error"' "$AUDIT_REPORT" > /tmp/audit.tmp && mv /tmp/audit.tmp "$AUDIT_REPORT"
    fi
}

# Generate recommendations
generate_recommendations() {
    log_section "6. Generating Recommendations"
    
    local recommendations=()
    
    # Check critical issues
    local critical_count=$(jq -r '.summary.critical_issues' "$AUDIT_REPORT")
    if [ "$critical_count" -gt 0 ]; then
        recommendations+=("🚨 URGENT: Fix $critical_count critical issues immediately")
        recommendations+=("Run 'npm install' to ensure all dependencies are installed")
        recommendations+=("Fix TypeScript compilation errors before proceeding")
    fi
    
    # Check important issues
    local important_count=$(jq -r '.summary.important_issues' "$AUDIT_REPORT")
    if [ "$important_count" -gt 0 ]; then
        recommendations+=("⚠️ Address $important_count important architectural issues")
        recommendations+=("Review and update outdated dependencies")
        recommendations+=("Implement proper error boundaries")
    fi
    
    # Check optimization issues
    local optimization_count=$(jq -r '.summary.optimization_issues' "$AUDIT_REPORT")
    if [ "$optimization_count" -gt 0 ]; then
        recommendations+=("💡 Consider $optimization_count optimization improvements")
        recommendations+=("Refactor large files and reduce complexity")
        recommendations+=("Update dependencies to latest stable versions")
    fi
    
    # Add recommendations to report
    for rec in "${recommendations[@]}"; do
        local temp_file=$(mktemp)
        jq --arg rec "$rec" '.recommendations += [$rec]' "$AUDIT_REPORT" > "$temp_file"
        mv "$temp_file" "$AUDIT_REPORT"
    done
}

# Generate remediation plan
generate_remediation_plan() {
    log_section "7. Generating Remediation Plan"
    
    local plan=()
    
    # Phase 1: Critical fixes
    plan+=("Phase 1 - Critical Fixes (Immediate)")
    plan+=("1. Fix TypeScript compilation errors")
    plan+=("2. Install missing critical dependencies")
    plan+=("3. Remove build error ignoring from next.config.ts")
    
    # Phase 2: Important improvements
    plan+=("Phase 2 - Important Improvements (This week)")
    plan+=("1. Update outdated dependencies")
    plan+=("2. Implement missing error boundaries")
    plan+=("3. Fix architectural violations")
    
    # Phase 3: Optimizations
    plan+=("Phase 3 - Optimizations (Next sprint)")
    plan+=("1. Refactor large files")
    plan+=("2. Reduce use of 'any' types")
    plan+=("3. Optimize import structure")
    
    # Add plan to report
    for item in "${plan[@]}"; do
        local temp_file=$(mktemp)
        jq --arg item "$item" '.remediation_plan += [$item]' "$AUDIT_REPORT" > "$temp_file"
        mv "$temp_file" "$AUDIT_REPORT"
    done
}

# Main execution
main() {
    log_info "Starting CircuLab System Audit"
    echo "Timestamp: $(date)" > "$AUDIT_LOG"
    
    init_audit_report
    
    # Run all audits
    audit_configuration
    audit_dependencies
    audit_architecture
    audit_imports
    audit_typescript
    
    # Generate outputs
    generate_recommendations
    generate_remediation_plan
    
    log_section "Audit Complete"
    log_info "Full report available at: $AUDIT_REPORT"
    log_info "Detailed logs available at: $AUDIT_LOG"
    
    # Display summary
    echo ""
    echo "=== AUDIT SUMMARY ==="
    if command -v jq &> /dev/null; then
        echo "Total Issues: $(jq -r '.summary.total_issues' "$AUDIT_REPORT")"
        echo "Critical: $(jq -r '.summary.critical_issues' "$AUDIT_REPORT")"
        echo "Important: $(jq -r '.summary.important_issues' "$AUDIT_REPORT")"
        echo "Optimization: $(jq -r '.summary.optimization_issues' "$AUDIT_REPORT")"
        echo ""
        echo "Report: $AUDIT_REPORT"
    fi
}

main "$@"

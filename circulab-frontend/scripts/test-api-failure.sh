#!/bin/bash

# Script de Test - Simulation de Panne API
# Teste la résilience de l'application face aux pannes backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/api-test.log"
BACKEND_PID_FILE="$PROJECT_ROOT/logs/backend.pid"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

log_info() {
    echo -e "${BLUE}[API-TEST]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if backend is running
check_backend_status() {
    local status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:4000/api/health 2>/dev/null || echo "000")
    
    if [ "$status" = "200" ]; then
        log_success "Backend is running (HTTP $status)"
        return 0
    else
        log_error "Backend is not responding (HTTP $status)"
        return 1
    fi
}

# Start backend if not running
start_backend() {
    log_info "Starting backend server..."
    
    cd "$PROJECT_ROOT/../circulab-backend"
    
    # Start backend in background
    npm start > "$PROJECT_ROOT/logs/backend.log" 2>&1 &
    local backend_pid=$!
    
    # Save PID for later cleanup
    echo $backend_pid > "$BACKEND_PID_FILE"
    
    log_info "Backend started with PID: $backend_pid"
    
    # Wait for backend to be ready
    local attempts=0
    local max_attempts=30
    
    while [ $attempts -lt $max_attempts ]; do
        if check_backend_status; then
            log_success "Backend is ready after $attempts attempts"
            return 0
        fi
        
        sleep 2
        attempts=$((attempts + 1))
        log_info "Waiting for backend... ($attempts/$max_attempts)"
    done
    
    log_error "Backend failed to start after $max_attempts attempts"
    return 1
}

# Stop backend
stop_backend() {
    log_info "Stopping backend server..."
    
    if [ -f "$BACKEND_PID_FILE" ]; then
        local backend_pid=$(cat "$BACKEND_PID_FILE")
        
        if kill -0 $backend_pid 2>/dev/null; then
            kill $backend_pid
            log_success "Backend stopped (PID: $backend_pid)"
        else
            log_warning "Backend process not found (PID: $backend_pid)"
        fi
        
        rm -f "$BACKEND_PID_FILE"
    else
        # Try to find and kill backend process
        local backend_pid=$(lsof -ti:4000 2>/dev/null || echo "")
        
        if [ -n "$backend_pid" ]; then
            kill $backend_pid
            log_success "Backend stopped (PID: $backend_pid)"
        else
            log_warning "No backend process found on port 4000"
        fi
    fi
}

# Monitor frontend health during API failure
monitor_frontend_health() {
    log_info "Monitoring frontend health during API failure..."
    
    local start_time=$(date +%s)
    local test_duration=60  # 1 minute test
    local check_interval=10  # Check every 10 seconds
    
    while [ $(($(date +%s) - start_time)) -lt $test_duration ]; do
        # Check frontend accessibility
        local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health 2>/dev/null || echo "000")
        
        if [ "$frontend_status" = "200" ]; then
            log_success "Frontend accessible (HTTP $frontend_status)"
        else
            log_error "Frontend not accessible (HTTP $frontend_status)"
        fi
        
        # Check API health endpoint from frontend perspective
        local api_health=$(curl -s http://localhost:8080/api/health 2>/dev/null || echo "ERROR")
        
        if [[ "$api_health" == *"ERROR"* ]] || [[ "$api_health" == *"error"* ]]; then
            log_warning "API health check failed as expected: $api_health"
        else
            log_info "API health response: $api_health"
        fi
        
        sleep $check_interval
    done
    
    log_info "Frontend monitoring completed"
}

# Test error boundary activation
test_error_boundary() {
    log_info "Testing error boundary activation..."
    
    # Try to access a page that might trigger API calls
    local response=$(curl -s http://localhost:8080/companies 2>/dev/null || echo "ERROR")
    
    if [[ "$response" == *"ERROR"* ]]; then
        log_error "Companies page not accessible"
    elif [[ "$response" == *"Error Boundary"* ]]; then
        log_success "Error boundary activated as expected"
    elif [[ "$response" == *"html"* ]]; then
        log_success "Companies page accessible with fallbacks"
    else
        log_warning "Unexpected response from companies page"
    fi
}

# Test safe imports behavior
test_safe_imports() {
    log_info "Testing safe imports behavior..."
    
    # Check if safe imports are working by looking for fallback components
    local health_page=$(curl -s http://localhost:8080/health 2>/dev/null || echo "ERROR")
    
    if [[ "$health_page" == *"Loading..."* ]]; then
        log_success "Safe import loading fallbacks detected"
    elif [[ "$health_page" == *"Component Failed"* ]]; then
        log_success "Safe import error fallbacks detected"
    elif [[ "$health_page" == *"Health Dashboard"* ]]; then
        log_success "Health dashboard loaded successfully"
    else
        log_warning "Unable to determine safe import status"
    fi
}

# Generate failure report
generate_failure_report() {
    log_info "Generating API failure test report..."
    
    local report_file="$PROJECT_ROOT/logs/api-failure-report.json"
    
    cat > "$report_file" << EOF
{
  "test_name": "API Failure Resilience Test",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "test_duration": "60 seconds",
  "scenarios_tested": [
    "Backend server shutdown",
    "Frontend accessibility during API failure",
    "Error boundary activation",
    "Safe imports fallback behavior",
    "Health monitoring system response"
  ],
  "expected_behaviors": [
    "Frontend remains accessible",
    "Error boundaries catch API failures",
    "Safe imports provide fallbacks",
    "Health dashboard shows API error status",
    "User experience degrades gracefully"
  ],
  "recovery_test": {
    "backend_restart": "automatic",
    "health_recovery": "monitored",
    "confidence_score_recovery": "tracked"
  }
}
EOF

    log_success "Test report generated: $report_file"
}

# Main test execution
main() {
    log_info "Starting API Failure Resilience Test"
    echo "Timestamp: $(date)" > "$LOG_FILE"
    
    echo ""
    echo "=== PHASE 1: BASELINE CHECK ==="
    if check_backend_status; then
        log_success "Backend is running - ready for failure test"
    else
        log_info "Backend not running - starting it first"
        if ! start_backend; then
            log_error "Failed to start backend - aborting test"
            exit 1
        fi
    fi
    
    echo ""
    echo "=== PHASE 2: SIMULATE API FAILURE ==="
    stop_backend
    
    echo ""
    echo "=== PHASE 3: MONITOR RESILIENCE ==="
    monitor_frontend_health
    test_error_boundary
    test_safe_imports
    
    echo ""
    echo "=== PHASE 4: RECOVERY TEST ==="
    log_info "Testing automatic recovery..."
    start_backend
    
    # Wait for recovery
    sleep 10
    
    if check_backend_status; then
        log_success "Backend recovery successful"
    else
        log_error "Backend recovery failed"
    fi
    
    echo ""
    echo "=== PHASE 5: REPORTING ==="
    generate_failure_report
    
    echo ""
    echo "=== TEST SUMMARY ==="
    log_info "API failure resilience test completed"
    log_info "Check the health dashboard at http://localhost:8080/health"
    log_info "Full test logs available at: $LOG_FILE"
    
    echo ""
    echo "Key observations to verify:"
    echo "1. Frontend remained accessible during API failure"
    echo "2. Health dashboard showed API error status"
    echo "3. Error boundaries provided graceful degradation"
    echo "4. Safe imports prevented component crashes"
    echo "5. System recovered automatically when API returned"
}

# Parse command line arguments
case "${1:-run}" in
    "run")
        main
        ;;
    "start-backend")
        start_backend
        ;;
    "stop-backend")
        stop_backend
        ;;
    "check-backend")
        check_backend_status
        ;;
    "monitor")
        monitor_frontend_health
        ;;
    *)
        echo "Usage: $0 {run|start-backend|stop-backend|check-backend|monitor}"
        echo ""
        echo "Commands:"
        echo "  run           - Execute full API failure test"
        echo "  start-backend - Start backend server"
        echo "  stop-backend  - Stop backend server"
        echo "  check-backend - Check backend status"
        echo "  monitor       - Monitor frontend health"
        exit 1
        ;;
esac

#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# CircuLab Pre-commit Health Check
echo "🔍 Running CircuLab pre-commit health check..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with colors
log_info() {
    echo -e "${BLUE}[PRE-COMMIT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    log_error "Not in frontend directory. Skipping health check."
    exit 0
fi

# Run quick health check
log_info "Running quick development health check..."

# Make script executable if needed
chmod +x scripts/check-dev-health.sh 2>/dev/null || true

# Run health check
if ./scripts/check-dev-health.sh > /dev/null 2>&1; then
    # Check confidence score
    if command -v jq &> /dev/null && [ -f "logs/health-report.json" ]; then
        CONFIDENCE=$(jq -r '.confidence // 0' logs/health-report.json)
        
        if [ "$CONFIDENCE" -ge 80 ]; then
            log_success "Health check passed (confidence: $CONFIDENCE%)"
        elif [ "$CONFIDENCE" -ge 60 ]; then
            log_warning "Health check passed with warnings (confidence: $CONFIDENCE%)"
            echo "Consider addressing health issues before committing."
        else
            log_error "Health check failed (confidence: $CONFIDENCE%)"
            echo "Please fix critical health issues before committing."
            echo "Run './scripts/check-dev-health.sh' for detailed information."
            exit 1
        fi
    else
        log_success "Basic health check passed"
    fi
else
    log_warning "Health check script failed, but allowing commit"
fi

# Run linting
log_info "Running ESLint..."
if npm run lint:check > /dev/null 2>&1; then
    log_success "Linting passed"
else
    log_warning "Linting issues found. Run 'npm run lint:fix' to auto-fix."
fi

# Run type checking
log_info "Running TypeScript type check..."
if npx tsc --noEmit --skipLibCheck > /dev/null 2>&1; then
    log_success "Type checking passed"
else
    log_error "TypeScript errors found. Please fix before committing."
    echo "Run 'npx tsc --noEmit' for detailed error information."
    exit 1
fi

# Check for large files
log_info "Checking for large files..."
LARGE_FILES=$(find . -name "*.js" -o -name "*.ts" -o -name "*.tsx" -o -name "*.json" | xargs ls -la | awk '$5 > 1048576 {print $9, $5}')

if [ -n "$LARGE_FILES" ]; then
    log_warning "Large files detected:"
    echo "$LARGE_FILES"
    echo "Consider optimizing or excluding these files."
fi

# Check for debugging code
log_info "Checking for debugging code..."
DEBUG_CODE=$(git diff --cached --name-only | xargs grep -l "console\.log\|debugger\|TODO\|FIXME" 2>/dev/null || true)

if [ -n "$DEBUG_CODE" ]; then
    log_warning "Debugging code or TODOs found in:"
    echo "$DEBUG_CODE"
    echo "Consider removing debugging code before committing."
fi

log_success "Pre-commit checks completed successfully!"
echo ""

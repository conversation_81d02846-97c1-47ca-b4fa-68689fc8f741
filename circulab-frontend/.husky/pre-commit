#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# CircuLab Pre-commit Health Check with Defensive Architecture
echo "🛡️ Running CircuLab pre-commit validation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to log with colors
log_info() {
    echo -e "${BLUE}[PRE-COMMIT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1"
}

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    log_error "Not in frontend directory. Skipping health check."
    exit 0
fi

# Section 1: Defensive Architecture Health Check
log_section "1. Defensive Architecture Health Check"

# Make script executable if needed
chmod +x scripts/check-dev-health.sh 2>/dev/null || true

# Run comprehensive health check
if ./scripts/check-dev-health.sh > /dev/null 2>&1; then
    # Check confidence score
    if command -v jq &> /dev/null && [ -f "logs/health-report.json" ]; then
        CONFIDENCE=$(jq -r '.confidence // 0' logs/health-report.json)
        OVERALL_STATUS=$(jq -r '.overall_status // "unknown"' logs/health-report.json)

        if [ "$CONFIDENCE" -ge 80 ]; then
            log_success "System health excellent (confidence: $CONFIDENCE%)"
        elif [ "$CONFIDENCE" -ge 60 ]; then
            log_warning "System health acceptable with warnings (confidence: $CONFIDENCE%)"
            echo "💡 Consider addressing health issues before committing."

            # Show recommendations
            RECOMMENDATIONS=$(jq -r '.recommendations[]?' logs/health-report.json 2>/dev/null || echo "")
            if [ -n "$RECOMMENDATIONS" ]; then
                echo "📋 Recommendations:"
                echo "$RECOMMENDATIONS" | sed 's/^/  • /'
            fi
        else
            log_error "System health critical (confidence: $CONFIDENCE%)"
            echo "🚨 Please fix critical health issues before committing."
            echo "Run './scripts/check-dev-health.sh' for detailed information."

            # Show critical issues
            RECOMMENDATIONS=$(jq -r '.recommendations[]?' logs/health-report.json 2>/dev/null || echo "")
            if [ -n "$RECOMMENDATIONS" ]; then
                echo "🔧 Required actions:"
                echo "$RECOMMENDATIONS" | sed 's/^/  • /'
            fi
            exit 1
        fi
    else
        log_success "Basic health check passed"
    fi
else
    log_warning "Health check script failed, but allowing commit"
fi

# Section 2: SafeImports Validation
log_section "2. SafeImports Validation"

# Check if SafeImports are being used properly
UNSAFE_IMPORTS=$(git diff --cached --name-only | grep -E '\.(tsx?|jsx?)$' | xargs grep -l "import.*from.*components" 2>/dev/null | xargs grep -L "SafeImports\|safeImports" 2>/dev/null || true)

if [ -n "$UNSAFE_IMPORTS" ]; then
    log_warning "Files using direct imports instead of SafeImports:"
    echo "$UNSAFE_IMPORTS" | sed 's/^/  • /'
    echo "💡 Consider using SafeImports for better resilience"
else
    log_success "SafeImports usage validated"
fi

# Section 3: Error Boundary Coverage
log_section "3. Error Boundary Coverage"

# Check if new components are wrapped in Error Boundaries
NEW_COMPONENTS=$(git diff --cached --name-only | grep -E 'components/.*\.(tsx?|jsx?)$' | head -5)

if [ -n "$NEW_COMPONENTS" ]; then
    log_info "New components detected - ensure Error Boundary coverage"
    echo "$NEW_COMPONENTS" | sed 's/^/  • /'
fi

# Section 4: Code Quality Checks
log_section "4. Code Quality Checks"

# Run linting
log_info "Running ESLint..."
if npm run lint:check > /dev/null 2>&1; then
    log_success "Linting passed"
else
    log_warning "Linting issues found. Run 'npm run lint:fix' to auto-fix."
    # Don't fail on linting issues, just warn
fi

# Run type checking
log_info "Running TypeScript type check..."
if npx tsc --noEmit --skipLibCheck > /dev/null 2>&1; then
    log_success "Type checking passed"
else
    log_error "TypeScript errors found. Please fix before committing."
    echo "Run 'npx tsc --noEmit' for detailed error information."
    exit 1
fi

# Section 5: Security and Performance Checks
log_section "5. Security and Performance Checks"

# Check for large files
log_info "Checking for large files..."
LARGE_FILES=$(find . -name "*.js" -o -name "*.ts" -o -name "*.tsx" -o -name "*.json" | xargs ls -la 2>/dev/null | awk '$5 > 1048576 {print $9, $5}' || true)

if [ -n "$LARGE_FILES" ]; then
    log_warning "Large files detected:"
    echo "$LARGE_FILES" | sed 's/^/  • /'
    echo "💡 Consider optimizing or excluding these files."
fi

# Check for debugging code
log_info "Checking for debugging code..."
DEBUG_CODE=$(git diff --cached --name-only | xargs grep -l "console\.log\|debugger\|TODO\|FIXME" 2>/dev/null || true)

if [ -n "$DEBUG_CODE" ]; then
    log_warning "Debugging code or TODOs found in:"
    echo "$DEBUG_CODE" | sed 's/^/  • /'
    echo "💡 Consider removing debugging code before committing."
fi

# Check for sensitive data
log_info "Checking for sensitive data..."
SENSITIVE_PATTERNS="password\|secret\|token\|api_key\|private_key"
SENSITIVE_FILES=$(git diff --cached --name-only | xargs grep -l "$SENSITIVE_PATTERNS" 2>/dev/null || true)

if [ -n "$SENSITIVE_FILES" ]; then
    log_error "Potential sensitive data found in:"
    echo "$SENSITIVE_FILES" | sed 's/^/  • /'
    echo "🚨 Please review and remove sensitive data before committing."
    exit 1
fi

# Section 6: Defensive Architecture Compliance
log_section "6. Defensive Architecture Compliance"

# Check if health monitoring is enabled
if grep -q "NEXT_PUBLIC_HEALTH_CHECK_INTERVAL" .env* 2>/dev/null; then
    log_success "Health monitoring configuration found"
else
    log_warning "Health monitoring configuration missing"
fi

# Check if error boundaries are in place
if grep -q "ErrorBoundary" src/app/layout.tsx 2>/dev/null; then
    log_success "Error Boundary integration confirmed"
else
    log_warning "Error Boundary integration not found in layout"
fi

# Check if SafeImports are properly configured
if [ -f "src/lib/safeImports.tsx" ]; then
    log_success "SafeImports system available"
else
    log_warning "SafeImports system not found"
fi

# Check if Health Dashboard is accessible
if [ -f "src/app/health/page.tsx" ]; then
    log_success "Health Dashboard available"
else
    log_warning "Health Dashboard not found"
fi

# Final summary
log_section "Pre-commit Validation Summary"

if [ -f "logs/health-report.json" ]; then
    FINAL_CONFIDENCE=$(jq -r '.confidence // 0' logs/health-report.json)
    HEALTHY_COUNT=$(jq -r '.summary.healthy // 0' logs/health-report.json)
    WARNING_COUNT=$(jq -r '.summary.warning // 0' logs/health-report.json)
    ERROR_COUNT=$(jq -r '.summary.error // 0' logs/health-report.json)

    echo "📊 System Health: $FINAL_CONFIDENCE% confidence"
    echo "✅ Healthy: $HEALTHY_COUNT | ⚠️ Warnings: $WARNING_COUNT | ❌ Errors: $ERROR_COUNT"

    # Final validation
    if [ "$FINAL_CONFIDENCE" -ge 80 ]; then
        echo "🎉 Excellent! Your commit is protected by a robust defensive architecture."
    elif [ "$FINAL_CONFIDENCE" -ge 60 ]; then
        echo "⚠️ Good! Minor issues detected but commit allowed."
    else
        echo "🚨 Critical issues detected. Please address before committing."
        exit 1
    fi
fi

log_success "Pre-commit validation completed successfully!"
echo "🛡️ Your code is protected by CircuLab Defensive Architecture"
echo ""

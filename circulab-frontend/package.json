{"name": "circulab-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:standalone": "NODE_ENV=production next build", "build:static": "NODE_ENV=production next build && next export", "start": "next start", "start:production": "NODE_ENV=production next start", "lint": "next lint", "lint:check": "next lint --quiet", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "health-check": "./scripts/check-dev-health.sh", "health-check:quick": "./scripts/check-dev-health.sh && echo 'Quick health check completed'", "test:extension": "./scripts/test-extension-conflict.sh", "test:api": "./scripts/test-api-failure.sh", "test:resilience": "npm run test:extension && npm run test:api", "prepare": "husky install", "pre-commit": "npm run health-check:quick && npm run lint:check && npm run type-check", "clean": "rm -rf .next out"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}
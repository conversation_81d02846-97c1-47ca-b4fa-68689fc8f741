/**
 * CircuLab Backend Safe Imports
 * Adaptation de l'architecture défensive pour Node.js/Express
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class BackendSafeImports extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            timeout: options.timeout || 5000,
            logLevel: options.logLevel || 'warn',
            fallbacksEnabled: options.fallbacksEnabled !== false,
            healthCheckInterval: options.healthCheckInterval || 30000,
            ...options
        };
        
        this.importCache = new Map();
        this.errorLog = [];
        this.healthMetrics = {
            totalImports: 0,
            successfulImports: 0,
            failedImports: 0,
            averageLoadTime: 0,
            lastHealthCheck: null
        };
        
        this.startHealthMonitoring();
    }
    
    /**
     * Safe import with retry logic and fallbacks
     */
    async safeRequire(modulePath, fallbackOptions = {}) {
        const startTime = Date.now();
        const importId = `${modulePath}_${Date.now()}`;
        
        this.healthMetrics.totalImports++;
        
        try {
            // Check cache first
            if (this.importCache.has(modulePath)) {
                const cached = this.importCache.get(modulePath);
                if (cached.success) {
                    this.log('info', `Using cached module: ${modulePath}`);
                    return cached.module;
                }
            }
            
            // Attempt import with retry logic
            const module = await this.attemptImportWithRetry(modulePath);
            
            // Cache successful import
            this.importCache.set(modulePath, {
                success: true,
                module,
                timestamp: Date.now()
            });
            
            this.healthMetrics.successfulImports++;
            this.updateAverageLoadTime(Date.now() - startTime);
            
            this.emit('importSuccess', { modulePath, loadTime: Date.now() - startTime });
            
            return module;
            
        } catch (error) {
            this.healthMetrics.failedImports++;
            this.logError('Import failed', error, { modulePath, importId });
            
            // Try fallback if available
            if (this.options.fallbacksEnabled && fallbackOptions.fallback) {
                this.log('warn', `Using fallback for ${modulePath}`);
                return fallbackOptions.fallback;
            }
            
            // Return mock if specified
            if (fallbackOptions.mock) {
                this.log('warn', `Using mock for ${modulePath}`);
                return fallbackOptions.mock;
            }
            
            this.emit('importError', { modulePath, error, importId });
            throw error;
        }
    }
    
    /**
     * Attempt import with retry logic
     */
    async attemptImportWithRetry(modulePath, attempt = 1) {
        try {
            // Resolve module path
            const resolvedPath = this.resolveModulePath(modulePath);
            
            // Check if file exists for local modules
            if (resolvedPath.startsWith('.') || resolvedPath.startsWith('/')) {
                if (!fs.existsSync(resolvedPath)) {
                    throw new Error(`Module file not found: ${resolvedPath}`);
                }
            }
            
            // Clear require cache for fresh import
            delete require.cache[require.resolve(resolvedPath)];
            
            // Import with timeout
            const module = await this.importWithTimeout(resolvedPath);
            
            this.log('info', `Successfully imported ${modulePath} on attempt ${attempt}`);
            return module;
            
        } catch (error) {
            if (attempt < this.options.retryAttempts) {
                this.log('warn', `Import attempt ${attempt} failed for ${modulePath}, retrying...`);
                
                // Exponential backoff
                const delay = this.options.retryDelay * Math.pow(2, attempt - 1);
                await this.sleep(delay);
                
                return this.attemptImportWithRetry(modulePath, attempt + 1);
            }
            
            throw error;
        }
    }
    
    /**
     * Import with timeout protection
     */
    async importWithTimeout(modulePath) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`Import timeout for ${modulePath}`));
            }, this.options.timeout);
            
            try {
                const module = require(modulePath);
                clearTimeout(timeout);
                resolve(module);
            } catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }
    
    /**
     * Resolve module path
     */
    resolveModulePath(modulePath) {
        if (modulePath.startsWith('.')) {
            return path.resolve(process.cwd(), modulePath);
        }
        return modulePath;
    }
    
    /**
     * Safe middleware loader
     */
    async safeMiddleware(middlewarePath, fallbackMiddleware = null) {
        try {
            const middleware = await this.safeRequire(middlewarePath);
            
            // Validate middleware function
            if (typeof middleware !== 'function') {
                throw new Error(`Invalid middleware: ${middlewarePath} is not a function`);
            }
            
            return middleware;
            
        } catch (error) {
            this.log('error', `Failed to load middleware ${middlewarePath}: ${error.message}`);
            
            if (fallbackMiddleware) {
                this.log('warn', `Using fallback middleware for ${middlewarePath}`);
                return fallbackMiddleware;
            }
            
            // Return no-op middleware as last resort
            return (req, res, next) => {
                this.log('warn', `No-op middleware executed for ${middlewarePath}`);
                next();
            };
        }
    }
    
    /**
     * Safe route handler loader
     */
    async safeRouteHandler(handlerPath, fallbackHandler = null) {
        try {
            const handler = await this.safeRequire(handlerPath);
            
            // Validate handler
            if (typeof handler !== 'function' && typeof handler.default !== 'function') {
                throw new Error(`Invalid route handler: ${handlerPath}`);
            }
            
            return typeof handler === 'function' ? handler : handler.default;
            
        } catch (error) {
            this.log('error', `Failed to load route handler ${handlerPath}: ${error.message}`);
            
            if (fallbackHandler) {
                return fallbackHandler;
            }
            
            // Return error handler as fallback
            return (req, res) => {
                res.status(500).json({
                    error: 'Handler not available',
                    message: `Route handler ${handlerPath} failed to load`,
                    timestamp: new Date().toISOString()
                });
            };
        }
    }
    
    /**
     * Safe configuration loader
     */
    async safeConfig(configPath, defaultConfig = {}) {
        try {
            const config = await this.safeRequire(configPath);
            return { ...defaultConfig, ...config };
            
        } catch (error) {
            this.log('warn', `Failed to load config ${configPath}, using defaults`);
            return defaultConfig;
        }
    }
    
    /**
     * Health check for all cached modules
     */
    async performHealthCheck() {
        this.log('info', 'Performing health check on cached modules');
        
        const healthResults = {
            timestamp: new Date().toISOString(),
            totalCached: this.importCache.size,
            healthyModules: 0,
            unhealthyModules: 0,
            details: []
        };
        
        for (const [modulePath, cached] of this.importCache.entries()) {
            try {
                // Try to access the module
                if (cached.module && typeof cached.module === 'object') {
                    // Module seems accessible
                    healthResults.healthyModules++;
                    healthResults.details.push({
                        module: modulePath,
                        status: 'healthy',
                        lastLoaded: new Date(cached.timestamp).toISOString()
                    });
                } else {
                    throw new Error('Module not accessible');
                }
                
            } catch (error) {
                healthResults.unhealthyModules++;
                healthResults.details.push({
                    module: modulePath,
                    status: 'unhealthy',
                    error: error.message,
                    lastLoaded: new Date(cached.timestamp).toISOString()
                });
                
                // Remove unhealthy module from cache
                this.importCache.delete(modulePath);
            }
        }
        
        this.healthMetrics.lastHealthCheck = healthResults;
        this.emit('healthCheck', healthResults);
        
        return healthResults;
    }
    
    /**
     * Start health monitoring
     */
    startHealthMonitoring() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.options.healthCheckInterval);
    }
    
    /**
     * Get health metrics
     */
    getHealthMetrics() {
        return {
            ...this.healthMetrics,
            successRate: this.healthMetrics.totalImports > 0 
                ? (this.healthMetrics.successfulImports / this.healthMetrics.totalImports) * 100 
                : 100,
            cacheSize: this.importCache.size,
            errorCount: this.errorLog.length
        };
    }
    
    /**
     * Get recent errors
     */
    getRecentErrors(limit = 10) {
        return this.errorLog.slice(-limit);
    }
    
    /**
     * Clear cache
     */
    clearCache() {
        this.importCache.clear();
        this.log('info', 'Import cache cleared');
    }
    
    /**
     * Clear error log
     */
    clearErrorLog() {
        this.errorLog = [];
        this.log('info', 'Error log cleared');
    }
    
    /**
     * Utility methods
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    updateAverageLoadTime(loadTime) {
        const total = this.healthMetrics.averageLoadTime * (this.healthMetrics.successfulImports - 1);
        this.healthMetrics.averageLoadTime = (total + loadTime) / this.healthMetrics.successfulImports;
    }
    
    log(level, message) {
        if (this.shouldLog(level)) {
            console[level](`[SafeImports] ${message}`);
        }
    }
    
    logError(context, error, metadata = {}) {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            metadata
        };
        
        this.errorLog.push(errorEntry);
        
        // Keep only last 100 errors
        if (this.errorLog.length > 100) {
            this.errorLog = this.errorLog.slice(-100);
        }
        
        this.log('error', `${context}: ${error.message}`);
    }
    
    shouldLog(level) {
        const levels = ['error', 'warn', 'info', 'debug'];
        const currentLevelIndex = levels.indexOf(this.options.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        
        return messageLevelIndex <= currentLevelIndex;
    }
}

// Express middleware for safe imports
function createSafeImportsMiddleware(options = {}) {
    const safeImports = new BackendSafeImports(options);
    
    return (req, res, next) => {
        req.safeImports = safeImports;
        next();
    };
}

// Health check endpoint
function createHealthEndpoint(safeImports) {
    return async (req, res) => {
        try {
            const healthCheck = await safeImports.performHealthCheck();
            const metrics = safeImports.getHealthMetrics();
            
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                safeImports: {
                    health: healthCheck,
                    metrics,
                    recentErrors: safeImports.getRecentErrors(5)
                }
            });
            
        } catch (error) {
            res.status(500).json({
                status: 'error',
                message: 'Health check failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    };
}

module.exports = {
    BackendSafeImports,
    createSafeImportsMiddleware,
    createHealthEndpoint
};

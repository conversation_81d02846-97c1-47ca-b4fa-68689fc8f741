/**
 * CircuLab External Monitoring Integration
 * Intégration avec Sentry, LogRocket et autres outils de monitoring
 */

class ExternalMonitoringIntegration {
    constructor(config = {}) {
        this.config = {
            sentry: {
                enabled: false,
                dsn: '',
                environment: 'production',
                release: '',
                ...config.sentry
            },
            logRocket: {
                enabled: false,
                appId: '',
                ...config.logRocket
            },
            datadog: {
                enabled: false,
                clientToken: '',
                applicationId: '',
                site: 'datadoghq.com',
                ...config.datadog
            },
            newRelic: {
                enabled: false,
                licenseKey: '',
                applicationId: '',
                ...config.newRelic
            },
            customWebhooks: config.customWebhooks || []
        };
        
        this.initialized = false;
        this.integrations = new Map();
    }
    
    /**
     * Initialize all configured monitoring integrations
     */
    async initialize() {
        if (this.initialized) {
            console.warn('[ExternalMonitoring] Already initialized');
            return;
        }
        
        try {
            // Initialize Sentry
            if (this.config.sentry.enabled) {
                await this.initializeSentry();
            }
            
            // Initialize LogRocket
            if (this.config.logRocket.enabled) {
                await this.initializeLogRocket();
            }
            
            // Initialize DataDog
            if (this.config.datadog.enabled) {
                await this.initializeDataDog();
            }
            
            // Initialize New Relic
            if (this.config.newRelic.enabled) {
                await this.initializeNewRelic();
            }
            
            this.initialized = true;
            console.log('[ExternalMonitoring] All integrations initialized');
            
        } catch (error) {
            console.error('[ExternalMonitoring] Initialization failed:', error);
            throw error;
        }
    }
    
    /**
     * Initialize Sentry integration
     */
    async initializeSentry() {
        try {
            // Dynamic import for Sentry
            const Sentry = await this.safeImport('@sentry/browser');
            
            if (!Sentry) {
                throw new Error('Sentry package not available');
            }
            
            Sentry.init({
                dsn: this.config.sentry.dsn,
                environment: this.config.sentry.environment,
                release: this.config.sentry.release,
                integrations: [
                    new Sentry.BrowserTracing(),
                ],
                tracesSampleRate: 0.1,
                beforeSend: (event) => {
                    // Filter out defensive architecture internal errors
                    if (event.tags && event.tags.component === 'DefensiveArchitecture') {
                        return null;
                    }
                    return event;
                }
            });
            
            this.integrations.set('sentry', Sentry);
            console.log('[ExternalMonitoring] Sentry initialized');
            
        } catch (error) {
            console.error('[ExternalMonitoring] Sentry initialization failed:', error);
        }
    }
    
    /**
     * Initialize LogRocket integration
     */
    async initializeLogRocket() {
        try {
            const LogRocket = await this.safeImport('logrocket');
            
            if (!LogRocket) {
                throw new Error('LogRocket package not available');
            }
            
            LogRocket.init(this.config.logRocket.appId);
            
            // Setup LogRocket + Sentry integration
            if (this.integrations.has('sentry')) {
                const Sentry = this.integrations.get('sentry');
                LogRocket.getSessionURL((sessionURL) => {
                    Sentry.configureScope((scope) => {
                        scope.setContext('LogRocket', { sessionURL });
                    });
                });
            }
            
            this.integrations.set('logRocket', LogRocket);
            console.log('[ExternalMonitoring] LogRocket initialized');
            
        } catch (error) {
            console.error('[ExternalMonitoring] LogRocket initialization failed:', error);
        }
    }
    
    /**
     * Initialize DataDog RUM integration
     */
    async initializeDataDog() {
        try {
            const { datadogRum } = await this.safeImport('@datadog/browser-rum');
            
            if (!datadogRum) {
                throw new Error('DataDog RUM package not available');
            }
            
            datadogRum.init({
                applicationId: this.config.datadog.applicationId,
                clientToken: this.config.datadog.clientToken,
                site: this.config.datadog.site,
                service: 'circulab-frontend',
                env: this.config.sentry.environment,
                version: this.config.sentry.release,
                sampleRate: 100,
                trackInteractions: true,
                defaultPrivacyLevel: 'mask-user-input'
            });
            
            datadogRum.startSessionReplayRecording();
            
            this.integrations.set('datadog', datadogRum);
            console.log('[ExternalMonitoring] DataDog RUM initialized');
            
        } catch (error) {
            console.error('[ExternalMonitoring] DataDog initialization failed:', error);
        }
    }
    
    /**
     * Initialize New Relic integration
     */
    async initializeNewRelic() {
        try {
            // New Relic is typically loaded via script tag
            if (typeof window !== 'undefined' && window.newrelic) {
                this.integrations.set('newRelic', window.newrelic);
                console.log('[ExternalMonitoring] New Relic initialized');
            } else {
                console.warn('[ExternalMonitoring] New Relic not available');
            }
            
        } catch (error) {
            console.error('[ExternalMonitoring] New Relic initialization failed:', error);
        }
    }
    
    /**
     * Send error to all configured monitoring services
     */
    captureError(error, context = {}) {
        const errorData = {
            message: error.message,
            stack: error.stack,
            name: error.name,
            timestamp: new Date().toISOString(),
            context: {
                ...context,
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
                url: typeof window !== 'undefined' ? window.location.href : 'unknown'
            }
        };
        
        // Send to Sentry
        if (this.integrations.has('sentry')) {
            const Sentry = this.integrations.get('sentry');
            Sentry.withScope((scope) => {
                Object.keys(context).forEach(key => {
                    scope.setTag(key, context[key]);
                });
                Sentry.captureException(error);
            });
        }
        
        // Send to DataDog
        if (this.integrations.has('datadog')) {
            const datadogRum = this.integrations.get('datadog');
            datadogRum.addError(error, context);
        }
        
        // Send to New Relic
        if (this.integrations.has('newRelic')) {
            const newrelic = this.integrations.get('newRelic');
            newrelic.noticeError(error, context);
        }
        
        // Send to custom webhooks
        this.sendToWebhooks('error', errorData);
        
        return errorData;
    }
    
    /**
     * Send performance metrics
     */
    capturePerformance(metrics) {
        const performanceData = {
            ...metrics,
            timestamp: new Date().toISOString()
        };
        
        // Send to DataDog
        if (this.integrations.has('datadog')) {
            const datadogRum = this.integrations.get('datadog');
            Object.keys(metrics).forEach(key => {
                if (typeof metrics[key] === 'number') {
                    datadogRum.addTiming(key, metrics[key]);
                }
            });
        }
        
        // Send to New Relic
        if (this.integrations.has('newRelic')) {
            const newrelic = this.integrations.get('newRelic');
            Object.keys(metrics).forEach(key => {
                if (typeof metrics[key] === 'number') {
                    newrelic.addPageAction('performance_metric', {
                        metric: key,
                        value: metrics[key]
                    });
                }
            });
        }
        
        // Send to custom webhooks
        this.sendToWebhooks('performance', performanceData);
    }
    
    /**
     * Send health check results
     */
    captureHealthCheck(healthData) {
        const healthCheckData = {
            ...healthData,
            timestamp: new Date().toISOString()
        };
        
        // Send to Sentry as breadcrumb
        if (this.integrations.has('sentry')) {
            const Sentry = this.integrations.get('sentry');
            Sentry.addBreadcrumb({
                message: 'Health Check',
                category: 'health',
                level: healthData.overall === 'healthy' ? 'info' : 
                       healthData.overall === 'warning' ? 'warning' : 'error',
                data: {
                    confidence: healthData.confidence,
                    status: healthData.overall
                }
            });
        }
        
        // Send to DataDog as custom event
        if (this.integrations.has('datadog')) {
            const datadogRum = this.integrations.get('datadog');
            datadogRum.addAction('health_check', {
                confidence: healthData.confidence,
                status: healthData.overall,
                checks: Object.keys(healthData.checks).length
            });
        }
        
        // Send to custom webhooks
        this.sendToWebhooks('health', healthCheckData);
    }
    
    /**
     * Send user action/event
     */
    captureUserAction(action, properties = {}) {
        const actionData = {
            action,
            properties,
            timestamp: new Date().toISOString()
        };
        
        // Send to LogRocket
        if (this.integrations.has('logRocket')) {
            const LogRocket = this.integrations.get('logRocket');
            LogRocket.track(action, properties);
        }
        
        // Send to DataDog
        if (this.integrations.has('datadog')) {
            const datadogRum = this.integrations.get('datadog');
            datadogRum.addAction(action, properties);
        }
        
        // Send to New Relic
        if (this.integrations.has('newRelic')) {
            const newrelic = this.integrations.get('newRelic');
            newrelic.addPageAction(action, properties);
        }
        
        // Send to custom webhooks
        this.sendToWebhooks('action', actionData);
    }
    
    /**
     * Set user context
     */
    setUserContext(user) {
        // Set in Sentry
        if (this.integrations.has('sentry')) {
            const Sentry = this.integrations.get('sentry');
            Sentry.setUser(user);
        }
        
        // Set in LogRocket
        if (this.integrations.has('logRocket')) {
            const LogRocket = this.integrations.get('logRocket');
            LogRocket.identify(user.id, user);
        }
        
        // Set in DataDog
        if (this.integrations.has('datadog')) {
            const datadogRum = this.integrations.get('datadog');
            datadogRum.setUser(user);
        }
    }
    
    /**
     * Send data to custom webhooks
     */
    async sendToWebhooks(type, data) {
        for (const webhook of this.config.customWebhooks) {
            if (webhook.types && !webhook.types.includes(type)) {
                continue;
            }
            
            try {
                const payload = {
                    type,
                    data,
                    source: 'circulab-defensive-architecture',
                    timestamp: new Date().toISOString()
                };
                
                await fetch(webhook.url, {
                    method: webhook.method || 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...webhook.headers
                    },
                    body: JSON.stringify(payload)
                });
                
            } catch (error) {
                console.error(`[ExternalMonitoring] Webhook failed for ${webhook.url}:`, error);
            }
        }
    }
    
    /**
     * Safe import with fallback
     */
    async safeImport(moduleName) {
        try {
            if (typeof window !== 'undefined') {
                // Browser environment - dynamic import
                const module = await import(moduleName);
                return module.default || module;
            } else {
                // Node.js environment
                return require(moduleName);
            }
        } catch (error) {
            console.warn(`[ExternalMonitoring] Failed to import ${moduleName}:`, error.message);
            return null;
        }
    }
    
    /**
     * Get integration status
     */
    getStatus() {
        return {
            initialized: this.initialized,
            integrations: Array.from(this.integrations.keys()),
            config: {
                sentry: { enabled: this.config.sentry.enabled },
                logRocket: { enabled: this.config.logRocket.enabled },
                datadog: { enabled: this.config.datadog.enabled },
                newRelic: { enabled: this.config.newRelic.enabled },
                webhooks: this.config.customWebhooks.length
            }
        };
    }
    
    /**
     * Create middleware for Express.js
     */
    createExpressMiddleware() {
        return (req, res, next) => {
            // Add monitoring to request object
            req.monitoring = {
                captureError: (error, context = {}) => {
                    return this.captureError(error, {
                        ...context,
                        url: req.url,
                        method: req.method,
                        userAgent: req.get('User-Agent'),
                        ip: req.ip
                    });
                },
                captureAction: (action, properties = {}) => {
                    return this.captureUserAction(action, {
                        ...properties,
                        url: req.url,
                        method: req.method
                    });
                }
            };
            
            next();
        };
    }
    
    /**
     * Create React error boundary integration
     */
    createReactErrorBoundary() {
        const self = this;
        
        return class MonitoringErrorBoundary extends React.Component {
            constructor(props) {
                super(props);
                this.state = { hasError: false, error: null };
            }
            
            static getDerivedStateFromError(error) {
                return { hasError: true, error };
            }
            
            componentDidCatch(error, errorInfo) {
                self.captureError(error, {
                    component: 'ReactErrorBoundary',
                    errorInfo,
                    props: this.props
                });
            }
            
            render() {
                if (this.state.hasError) {
                    return this.props.fallback || React.createElement('div', {
                        style: { padding: '20px', textAlign: 'center' }
                    }, 'Something went wrong. The error has been reported.');
                }
                
                return this.props.children;
            }
        };
    }
}

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExternalMonitoringIntegration;
} else if (typeof window !== 'undefined') {
    window.ExternalMonitoringIntegration = ExternalMonitoringIntegration;
}

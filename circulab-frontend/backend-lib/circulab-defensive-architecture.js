/**
 * CircuLab Defensive Architecture - Reusable Components
 * Package réutilisable pour étendre l'architecture défensive à d'autres projets
 */

// Core defensive architecture components
const DefensiveArchitecture = {
    // Version and metadata
    version: '1.0.0',
    name: 'CircuLab Defensive Architecture',
    
    // Core modules
    SafeImports: null,
    HealthMonitor: null,
    ErrorBoundary: null,
    MetricsCollector: null,
    AlertSystem: null,
    
    // Configuration
    config: {
        retryAttempts: 3,
        retryDelay: 1000,
        healthCheckInterval: 30000,
        confidenceThreshold: 80,
        alertThreshold: 70,
        logLevel: 'warn'
    },
    
    // Initialize the defensive architecture
    async initialize(options = {}) {
        this.config = { ...this.config, ...options };
        
        // Initialize core components
        this.SafeImports = await this.initializeSafeImports();
        this.HealthMonitor = await this.initializeHealthMonitor();
        this.ErrorBoundary = await this.initializeErrorBoundary();
        this.MetricsCollector = await this.initializeMetricsCollector();
        this.AlertSystem = await this.initializeAlertSystem();
        
        console.log(`[DefensiveArchitecture] Initialized v${this.version}`);
        return this;
    },
    
    // Safe Imports Module
    async initializeSafeImports() {
        return {
            // Create safe import wrapper
            createSafeImport: (importFunction, componentName, options = {}) => {
                return async (...args) => {
                    const startTime = Date.now();
                    let attempt = 0;
                    
                    while (attempt < this.config.retryAttempts) {
                        try {
                            const result = await importFunction(...args);
                            
                            // Log successful import
                            this.MetricsCollector?.recordImport(componentName, true, Date.now() - startTime);
                            
                            return result;
                            
                        } catch (error) {
                            attempt++;
                            
                            if (attempt >= this.config.retryAttempts) {
                                // Log failed import
                                this.MetricsCollector?.recordImport(componentName, false, Date.now() - startTime);
                                this.ErrorBoundary?.captureError(error, { component: componentName, context: 'SafeImport' });
                                
                                // Return fallback if available
                                if (options.fallback) {
                                    console.warn(`[SafeImports] Using fallback for ${componentName}`);
                                    return options.fallback;
                                }
                                
                                throw error;
                            }
                            
                            // Wait before retry
                            await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt));
                        }
                    }
                };
            },
            
            // Batch import with fallbacks
            batchImport: async (imports) => {
                const results = {};
                const errors = [];
                
                for (const [name, importConfig] of Object.entries(imports)) {
                    try {
                        results[name] = await this.SafeImports.createSafeImport(
                            importConfig.import,
                            name,
                            importConfig.options || {}
                        )();
                    } catch (error) {
                        errors.push({ name, error });
                        if (importConfig.required) {
                            throw new Error(`Required import ${name} failed: ${error.message}`);
                        }
                    }
                }
                
                return { results, errors };
            }
        };
    },
    
    // Health Monitor Module
    async initializeHealthMonitor() {
        const healthChecks = new Map();
        const healthHistory = [];
        
        return {
            // Register health check
            registerCheck: (name, checkFunction, options = {}) => {
                healthChecks.set(name, {
                    check: checkFunction,
                    interval: options.interval || this.config.healthCheckInterval,
                    enabled: options.enabled !== false,
                    lastRun: null,
                    lastResult: null
                });
            },
            
            // Run all health checks
            runHealthChecks: async () => {
                const results = {
                    timestamp: new Date().toISOString(),
                    overall: 'healthy',
                    confidence: 100,
                    checks: {},
                    summary: { healthy: 0, warning: 0, error: 0 }
                };
                
                let totalScore = 0;
                let maxScore = 0;
                
                for (const [name, config] of healthChecks.entries()) {
                    if (!config.enabled) continue;
                    
                    try {
                        const checkResult = await config.check();
                        const score = checkResult.score || (checkResult.status === 'healthy' ? 20 : checkResult.status === 'warning' ? 10 : 0);
                        
                        results.checks[name] = {
                            status: checkResult.status || 'healthy',
                            message: checkResult.message || 'OK',
                            score: score,
                            details: checkResult.details || {}
                        };
                        
                        totalScore += score;
                        maxScore += 20;
                        results.summary[checkResult.status || 'healthy']++;
                        
                        config.lastRun = new Date();
                        config.lastResult = checkResult;
                        
                    } catch (error) {
                        results.checks[name] = {
                            status: 'error',
                            message: error.message,
                            score: 0,
                            details: { error: error.stack }
                        };
                        
                        maxScore += 20;
                        results.summary.error++;
                        
                        this.ErrorBoundary?.captureError(error, { component: name, context: 'HealthCheck' });
                    }
                }
                
                // Calculate overall confidence
                results.confidence = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
                
                // Determine overall status
                if (results.confidence >= this.config.confidenceThreshold) {
                    results.overall = 'healthy';
                } else if (results.confidence >= 50) {
                    results.overall = 'warning';
                } else {
                    results.overall = 'error';
                }
                
                // Store in history
                healthHistory.push(results);
                if (healthHistory.length > 100) {
                    healthHistory.shift();
                }
                
                // Trigger alerts if needed
                if (results.confidence < this.config.alertThreshold) {
                    this.AlertSystem?.triggerAlert('health', results);
                }
                
                return results;
            },
            
            // Get health history
            getHistory: (limit = 10) => healthHistory.slice(-limit),
            
            // Get registered checks
            getRegisteredChecks: () => Array.from(healthChecks.keys())
        };
    },
    
    // Error Boundary Module
    async initializeErrorBoundary() {
        const errorLog = [];
        
        return {
            // Capture error
            captureError: (error, context = {}) => {
                const errorEntry = {
                    timestamp: new Date().toISOString(),
                    error: {
                        message: error.message,
                        stack: error.stack,
                        name: error.name
                    },
                    context,
                    id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                };
                
                errorLog.push(errorEntry);
                
                // Keep only last 1000 errors
                if (errorLog.length > 1000) {
                    errorLog.shift();
                }
                
                // Log to console based on log level
                if (this.config.logLevel === 'error' || this.config.logLevel === 'warn' || this.config.logLevel === 'info') {
                    console.error(`[ErrorBoundary] ${error.message}`, { context, stack: error.stack });
                }
                
                // Trigger alert for critical errors
                if (context.critical) {
                    this.AlertSystem?.triggerAlert('error', errorEntry);
                }
                
                return errorEntry.id;
            },
            
            // Get errors
            getErrors: (filter = {}) => {
                let filtered = errorLog;
                
                if (filter.since) {
                    const since = new Date(filter.since);
                    filtered = filtered.filter(e => new Date(e.timestamp) >= since);
                }
                
                if (filter.context) {
                    filtered = filtered.filter(e => 
                        Object.keys(filter.context).every(key => 
                            e.context[key] === filter.context[key]
                        )
                    );
                }
                
                return filtered.slice(-(filter.limit || 100));
            },
            
            // Clear errors
            clearErrors: () => {
                errorLog.length = 0;
            },
            
            // Create error handler middleware (for Express)
            createErrorHandler: () => {
                return (error, req, res, next) => {
                    const errorId = this.ErrorBoundary.captureError(error, {
                        url: req.url,
                        method: req.method,
                        userAgent: req.get('User-Agent'),
                        ip: req.ip
                    });
                    
                    res.status(500).json({
                        error: 'Internal Server Error',
                        errorId,
                        timestamp: new Date().toISOString()
                    });
                };
            }
        };
    },
    
    // Metrics Collector Module
    async initializeMetricsCollector() {
        const metrics = {
            imports: { total: 0, successful: 0, failed: 0, totalTime: 0 },
            healthChecks: { total: 0, healthy: 0, warning: 0, error: 0 },
            errors: { total: 0, critical: 0 },
            performance: { averageResponseTime: 0, requestCount: 0 }
        };
        
        return {
            // Record import metrics
            recordImport: (component, success, duration) => {
                metrics.imports.total++;
                metrics.imports.totalTime += duration;
                
                if (success) {
                    metrics.imports.successful++;
                } else {
                    metrics.imports.failed++;
                }
            },
            
            // Record health check metrics
            recordHealthCheck: (result) => {
                metrics.healthChecks.total++;
                metrics.healthChecks[result.overall]++;
            },
            
            // Record error metrics
            recordError: (critical = false) => {
                metrics.errors.total++;
                if (critical) {
                    metrics.errors.critical++;
                }
            },
            
            // Record performance metrics
            recordPerformance: (responseTime) => {
                const total = metrics.performance.averageResponseTime * metrics.performance.requestCount;
                metrics.performance.requestCount++;
                metrics.performance.averageResponseTime = (total + responseTime) / metrics.performance.requestCount;
            },
            
            // Get all metrics
            getMetrics: () => ({
                ...metrics,
                timestamp: new Date().toISOString(),
                importSuccessRate: metrics.imports.total > 0 ? (metrics.imports.successful / metrics.imports.total) * 100 : 100,
                averageImportTime: metrics.imports.total > 0 ? metrics.imports.totalTime / metrics.imports.total : 0
            }),
            
            // Reset metrics
            resetMetrics: () => {
                Object.keys(metrics).forEach(key => {
                    if (typeof metrics[key] === 'object') {
                        Object.keys(metrics[key]).forEach(subKey => {
                            metrics[key][subKey] = 0;
                        });
                    } else {
                        metrics[key] = 0;
                    }
                });
            }
        };
    },
    
    // Alert System Module
    async initializeAlertSystem() {
        const alertHandlers = new Map();
        const alertHistory = [];
        
        return {
            // Register alert handler
            registerHandler: (type, handler) => {
                if (!alertHandlers.has(type)) {
                    alertHandlers.set(type, []);
                }
                alertHandlers.get(type).push(handler);
            },
            
            // Trigger alert
            triggerAlert: async (type, data) => {
                const alert = {
                    id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    type,
                    timestamp: new Date().toISOString(),
                    data
                };
                
                alertHistory.push(alert);
                
                // Keep only last 100 alerts
                if (alertHistory.length > 100) {
                    alertHistory.shift();
                }
                
                // Execute handlers
                const handlers = alertHandlers.get(type) || [];
                for (const handler of handlers) {
                    try {
                        await handler(alert);
                    } catch (error) {
                        console.error(`[AlertSystem] Handler failed for ${type}:`, error);
                    }
                }
                
                return alert.id;
            },
            
            // Get alert history
            getAlerts: (filter = {}) => {
                let filtered = alertHistory;
                
                if (filter.type) {
                    filtered = filtered.filter(a => a.type === filter.type);
                }
                
                if (filter.since) {
                    const since = new Date(filter.since);
                    filtered = filtered.filter(a => new Date(a.timestamp) >= since);
                }
                
                return filtered.slice(-(filter.limit || 50));
            }
        };
    },
    
    // Utility functions
    utils: {
        // Create health check endpoint
        createHealthEndpoint: function() {
            return async (req, res) => {
                try {
                    const healthResult = await DefensiveArchitecture.HealthMonitor.runHealthChecks();
                    const metrics = DefensiveArchitecture.MetricsCollector.getMetrics();
                    const recentErrors = DefensiveArchitecture.ErrorBoundary.getErrors({ limit: 5 });
                    
                    res.json({
                        status: 'ok',
                        defensiveArchitecture: {
                            version: DefensiveArchitecture.version,
                            health: healthResult,
                            metrics,
                            recentErrors,
                            timestamp: new Date().toISOString()
                        }
                    });
                    
                } catch (error) {
                    DefensiveArchitecture.ErrorBoundary?.captureError(error, { context: 'HealthEndpoint' });
                    
                    res.status(500).json({
                        status: 'error',
                        message: 'Health check failed',
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            };
        },
        
        // Create metrics endpoint
        createMetricsEndpoint: function() {
            return (req, res) => {
                const metrics = DefensiveArchitecture.MetricsCollector.getMetrics();
                res.json(metrics);
            };
        },
        
        // Create performance monitoring middleware
        createPerformanceMiddleware: function() {
            return (req, res, next) => {
                const startTime = Date.now();
                
                res.on('finish', () => {
                    const responseTime = Date.now() - startTime;
                    DefensiveArchitecture.MetricsCollector.recordPerformance(responseTime);
                });
                
                next();
            };
        }
    }
};

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js
    module.exports = DefensiveArchitecture;
} else if (typeof window !== 'undefined') {
    // Browser
    window.CircuLabDefensiveArchitecture = DefensiveArchitecture;
} else {
    // Other environments
    this.CircuLabDefensiveArchitecture = DefensiveArchitecture;
}

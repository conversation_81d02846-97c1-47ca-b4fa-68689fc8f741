# CircuLab Defensive Architecture - Cron Jobs Configuration
# Generated on Fri Jun  6 19:11:11 CEST 2025

# Health monitoring every 30 minutes
*/30 * * * * cd /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend && ./scripts/cron-health-monitor.sh >> logs/cron-output.log 2>&1

# Weekly report generation every Monday at 9:00 AM
0 9 * * 1 cd /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend && ./scripts/weekly-report-generator.sh >> logs/weekly-report.log 2>&1

# Daily cleanup at 2:00 AM
0 2 * * * cd /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend && find logs/ -name "*.log" -mtime +7 -delete && find metrics/ -name "*.jsonl" -exec tail -1000 {} \; > {}.tmp && mv {}.tmp {} 2>/dev/null || true

# Alert system health check every 5 minutes (only during business hours)
*/5 9-17 * * 1-5 cd /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend && ./scripts/setup-alerts.sh check >> logs/alert-check.log 2>&1

# Backup important files daily at 3:00 AM
0 3 * * * cd /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend && tar -czf backups/daily-backup-$(date +\%Y\%m\%d).tar.gz logs/ metrics/ config/ 2>/dev/null || true

# Remove old backups (keep 30 days)
0 4 * * * find /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/backups/ -name "daily-backup-*.tar.gz" -mtime +30 -delete 2>/dev/null || true

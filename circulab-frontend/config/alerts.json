{"slack": {"enabled": false, "webhook_url": "", "channel": "#circulab-alerts", "username": "CircuLab Health Bot", "icon_emoji": ":shield:"}, "discord": {"enabled": false, "webhook_url": "", "username": "CircuLab Health Bot"}, "email": {"enabled": true, "to": "<EMAIL>", "smtp_server": "smtp.gmail.com", "smtp_port": 587}, "thresholds": {"critical": 50, "warning": 70}, "intervals": {"health_check": 30, "alert_cooldown": 300}}
# CircuLab Waste Management Dashboard - Comprehensive Demo

## 🎯 Overview
CircuLab is a complete industrial waste valorization platform built with modern web technologies. This comprehensive demo showcases a production-ready application with advanced features for waste management, company administration, and processing analytics.

## 🏗️ Architecture & Technology Stack

### Frontend Framework
- **Next.js 15.3.2** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **Tailwind CSS v4** for responsive, utility-first styling
- **Radix UI** primitives with custom shadcn/ui components

### State Management & API
- **Zustand** for lightweight, scalable state management
- **Axios** with custom interceptors for HTTP requests
- **React Hook Form** with Zod validation for forms
- **JWT Authentication** with automatic token refresh

### Data Visualization & Export
- **Recharts** for interactive charts and analytics
- **Custom SVG charts** for specialized visualizations
- **XLSX library** for Excel export functionality
- **CSV export** with custom formatting

## 📱 Complete Feature Set

### 1. Dashboard Main Page (`/`)
**Key Performance Metrics Cards:**
- Total waste processed: 476,117.93 tonnes with 12.5% growth trend
- Processing efficiency tracking with interactive area charts
- Real-time data integration from API endpoints
- Responsive metric cards with hover animations

**Interactive Charts & Visualizations:**
- Waste processing trends with custom SVG area charts
- Processing by category with donut charts (Organic 45%, Plastic 30%, Metal 25%)
- Waste generated map with multiple view modes (targets, yearly, map, table, chart)
- Custom chart components with gradient fills and animations

**Layout Structure:**
- Responsive grid layout adapting from mobile to desktop
- Tabbed interface: KPIs, Waste dashboard, Processing, Efficiency, Materials, Analytics
- Real-time data updates with loading states and error handling

### 2. Authentication System
**Login/Register Pages:**
- Form validation using React Hook Form + Zod schemas
- Professional branding with gradient backgrounds
- Demo credentials provided for easy testing
- Responsive design with mobile-first approach

**JWT Token Management:**
- Automatic token refresh mechanism
- Secure token storage in localStorage
- Request interceptors for automatic token attachment
- Graceful handling of expired tokens with re-authentication

**Protected Routes:**
- Route-level authentication guards
- Automatic redirects for unauthenticated users
- User profile management and company association
- Role-based access control ready for implementation

### 3. Data Table Components
**Companies Management (`/companies`):**
- Search functionality with real-time filtering
- Pagination with configurable page sizes
- CRUD operations (Create, Read, Update, Delete)
- Company details: SIRET, industry type, contact information
- **NEW: CSV/Excel Export** with customizable columns

**Processing History (`/processing-history`):**
- Advanced filtering: waste type, treatment method, date ranges
- Detailed processing records with input/output quantities
- Efficiency calculations and environmental impact tracking
- Processor company associations and treatment method details
- **NEW: Export functionality** with specialized formatting

**Sorting & Filtering:**
- Multi-column sorting capabilities
- Date range filters with calendar pickers
- Dropdown filters for categorical data
- Search across multiple fields simultaneously

### 4. API Integration & State Management
**API Client Architecture:**
- Centralized Axios instance with base configuration
- Request/response interceptors for authentication and error handling
- Automatic retry logic for failed requests
- Type-safe API responses with TypeScript interfaces

**Zustand Store Patterns:**
- Modular stores for different data domains (auth, materials, companies)
- Optimistic updates for better user experience
- Error state management with user-friendly messages
- Pagination state management with URL synchronization

**Error Handling & Retry Logic:**
- Network error detection and user notification
- Automatic token refresh on 401 errors
- Graceful degradation for offline scenarios
- Toast notifications for user feedback

### 5. Responsive Design
**Desktop Layout (lg: 1024px+):**
- Fixed sidebar navigation with 64 width units
- Main content area with max-width container
- Multi-column grid layouts for optimal space usage
- Hover effects and smooth transitions

**Mobile Layout (< 1024px):**
- Collapsible hamburger menu with overlay
- Touch-friendly button sizes and spacing
- Stacked layouts for better mobile readability
- Swipe gestures for navigation (ready for implementation)

**Tailwind CSS Responsive Breakpoints:**
- `sm:` 640px - Small tablets and large phones
- `md:` 768px - Tablets
- `lg:` 1024px - Laptops and small desktops
- `xl:` 1280px - Large desktops

### 6. Production Deployment (Docker)
**Multi-stage Dockerfile:**
- Optimized build process with dependency caching
- Security-hardened with non-root user
- Health checks for container monitoring
- Standalone output for minimal runtime footprint

**Docker Compose Configuration:**
- Frontend service with environment variables
- Nginx reverse proxy with SSL termination
- Network isolation and volume management
- Health checks and restart policies

**Deployment Scripts:**
- Automated deployment with `deploy.sh`
- Environment validation and setup
- Container lifecycle management
- Health monitoring and logging

### 7. NEW FEATURE: CSV/Excel Export
**Export Utilities (`/lib/exportUtils.ts`):**
- Support for both CSV and Excel formats
- Customizable column configurations
- Data formatting for different types (dates, numbers, booleans)
- Multi-sheet Excel export capability

**Export Components:**
- Dropdown menu with format selection
- Progress indicators during export
- Toast notifications for success/error feedback
- Specialized export buttons for different data types

**Pre-configured Export Columns:**
- Companies: Name, SIRET, Industry, Contact details
- Processing History: Waste type, Treatment method, Quantities, Dates
- Users: Username, Email, Role, Company, Status
- Waste Materials: Type, Quantity, Status, Location

## 🔧 Technical Implementation Details

### Component Architecture
- Reusable UI components with consistent design system
- Compound component patterns for complex interactions
- Custom hooks for shared logic and state management
- TypeScript interfaces for prop validation

### Performance Optimizations
- Code splitting with Next.js dynamic imports
- Image optimization with Next.js Image component
- Bundle analysis and size optimization (101 kB shared JS)
- Efficient re-rendering with React.memo and useMemo

### Security Features
- JWT token validation and refresh
- Input sanitization and validation
- CSRF protection ready for implementation
- Secure HTTP headers in Nginx configuration

### Accessibility
- ARIA labels and semantic HTML
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## 🚀 Deployment Instructions

### Local Development
```bash
npm install
npm run dev
```

### Docker Production Deployment
```bash
# Build and deploy
./deploy.sh

# Individual commands
./deploy.sh build    # Build image only
./deploy.sh start    # Start container
./deploy.sh logs     # View logs
./deploy.sh health   # Health check
```

### Environment Configuration
Copy `.env.production.example` to `.env.production` and configure:
- API endpoints
- Authentication secrets
- Database connections
- External service credentials

## 📊 Demo Data & Testing
- Mock API responses for all endpoints
- Realistic sample data for companies and processing history
- Demo authentication credentials provided
- Comprehensive error scenarios for testing

## 🎨 Design System
- Consistent color palette with primary/secondary colors
- Typography scale with proper hierarchy
- Spacing system using Tailwind's scale
- Component variants for different contexts
- Dark mode ready (theme switching capability)

This comprehensive demo showcases a production-ready waste management platform with modern development practices, robust architecture, and user-friendly design.

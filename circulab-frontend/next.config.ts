import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Use standalone output for production deployment with Express
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,

  // Enable static export for easier serving from Express in production
  trailingSlash: true,

  // Configure asset prefix for production
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : undefined,

  // Experimental features
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },

  // ESLint configuration for build - SECURED
  eslint: {
    // Enforce ESLint checks during builds for better code quality
    ignoreDuringBuilds: false,
    dirs: ['src'], // Only check src directory
  },

  // TypeScript configuration for build - SECURED
  typescript: {
    // Enforce TypeScript checks during builds for type safety
    ignoreBuildErrors: false,
    tsconfigPath: './tsconfig.json',
  },

  // Configure rewrites for API routes in development
  async rewrites() {
    if (process.env.NODE_ENV === 'development') {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:4000/api/:path*',
        },
        {
          source: '/health',
          destination: 'http://localhost:4000/health',
        },
        {
          source: '/api-docs/:path*',
          destination: 'http://localhost:4000/api-docs/:path*',
        },
      ];
    }
    return [];
  },

  // Webpack configuration optimized for performance
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Performance optimizations
    if (!dev && !isServer) {
      // Split chunks for better caching
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
            // CircuLab specific chunks
            dashboard: {
              test: /[\\/]src[\\/]components[\\/]dashboard[\\/]/,
              name: 'dashboard',
              chunks: 'all',
              priority: 8,
            },
            ui: {
              test: /[\\/]src[\\/]components[\\/]ui[\\/]/,
              name: 'ui',
              chunks: 'all',
              priority: 7,
            },
          },
        },
      };

      // Tree shaking optimizations
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    // Bundle analyzer in development
    if (dev && process.env.ANALYZE === 'true') {
      try {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'server',
            openAnalyzer: true,
          })
        );
      } catch (e) {
        console.warn('Bundle analyzer not available');
      }
    }

    return config;
  },
};

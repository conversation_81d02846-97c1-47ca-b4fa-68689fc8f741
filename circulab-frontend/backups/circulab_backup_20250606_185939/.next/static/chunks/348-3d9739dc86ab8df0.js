"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[348],{1154:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1788:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},3029:(e,n,r)=>{r.d(n,{C:()=>l});var t=r(2115),o=r(3257),a=r(8868),l=e=>{let{present:n,children:r}=e,l=function(e){var n,r;let[o,l]=t.useState(),i=t.useRef(null),d=t.useRef(e),s=t.useRef("none"),[c,p]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=u(i.current);s.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=i.current,r=d.current;if(r!==e){let t=s.current,o=u(n);e?p("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?p("UNMOUNT"):r&&t!==o?p("ANIMATION_OUT"):p("UNMOUNT"),d.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let n,r=null!=(e=o.ownerDocument.defaultView)?e:window,t=e=>{let t=u(i.current).includes(e.animationName);if(e.target===o&&t&&(p("ANIMATION_END"),!d.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=u(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(n),i="function"==typeof r?r({present:l.isPresent}):t.Children.only(r),d=(0,o.s)(l.ref,function(e){var n,r;let t=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||l.isPresent?t.cloneElement(i,{ref:d}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},3052:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3227:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},3717:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4261:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},4616:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5500:(e,n,r)=>{r.d(n,{H_:()=>e7,UC:()=>e4,YJ:()=>e5,q7:()=>e6,VF:()=>ne,JU:()=>e9,ZL:()=>e2,z6:()=>e3,hN:()=>e8,bL:()=>e0,wv:()=>nn,Pb:()=>nr,G5:()=>no,ZP:()=>nt,l9:()=>e1});var t=r(2115),o=r(5677),a=r(3257),l=r(1549),u=r(7769),i=r(4347),d=r(6459),s=r(5191),c=r(4904),p=r(953),f=r(7507),v=r(3561),m=r(4105),h=r(510),g=r(3029),w=r(9848),y=r(3232),x=r(293),M=r(1492),b=r(4752),k=r(5155),C=["Enter"," "],R=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...R],D={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},N={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_="Menu",[A,I,T]=(0,d.N)(_),[E,P]=(0,l.A)(_,[T,m.Bk,w.RG]),O=(0,m.Bk)(),S=(0,w.RG)(),[L,F]=E(_),[G,K]=E(_),U=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=O(n),[d,c]=t.useState(null),p=t.useRef(!1),f=(0,x.c)(l),v=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,k.jsx)(m.bL,{...i,children:(0,k.jsx)(L,{scope:n,open:r,onOpenChange:f,content:d,onContentChange:c,children:(0,k.jsx)(G,{scope:n,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};U.displayName=_;var B=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,k.jsx)(m.Mz,{...o,...t,ref:n})});B.displayName="MenuAnchor";var q="MenuPortal",[H,V]=E(q,{forceMount:void 0}),z=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=F(q,n);return(0,k.jsx)(H,{scope:n,forceMount:r,children:(0,k.jsx)(g.C,{present:r||a.open,children:(0,k.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};z.displayName=q;var Z="MenuContent",[W,X]=E(Z),Y=t.forwardRef((e,n)=>{let r=V(Z,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=F(Z,e.__scopeMenu),l=K(Z,e.__scopeMenu);return(0,k.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:t||a.open,children:(0,k.jsx)(A.Slot,{scope:e.__scopeMenu,children:l.modal?(0,k.jsx)(J,{...o,ref:n}):(0,k.jsx)(Q,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=F(Z,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(n,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,M.Eq)(e)},[]),(0,k.jsx)(ee,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let r=F(Z,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:M,disableOutsideScroll:C,...D}=e,N=F(Z,r),_=K(Z,r),A=O(r),T=S(r),E=I(r),[P,L]=t.useState(null),G=t.useRef(null),U=(0,a.s)(n,G,N.onContentChange),B=t.useRef(0),q=t.useRef(""),H=t.useRef(0),V=t.useRef(null),z=t.useRef("right"),X=t.useRef(0),Y=C?b.A:t.Fragment,J=e=>{var n,r;let t=q.current+e,o=E().filter(e=>!e.disabled),a=document.activeElement,l=null==(n=o.find(e=>e.ref.current===a))?void 0:n.textValue,u=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=r?e.indexOf(r):-1,l=(t=Math.max(a,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(r=o.find(e=>e.textValue===u))?void 0:r.ref.current;!function e(n){q.current=n,window.clearTimeout(B.current),""!==n&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var n,r;return z.current===(null==(n=V.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let l=n[e],u=n[a],i=l.x,d=l.y,s=u.x,c=u.y;d>t!=c>t&&r<(s-i)*(t-d)/(c-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=V.current)?void 0:r.area)},[]);return(0,k.jsx)(W,{scope:r,searchRef:q,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var n;Q(e)||(null==(n=G.current)||n.focus(),L(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:H,onPointerGraceIntentChange:t.useCallback(e=>{V.current=e},[]),children:(0,k.jsx)(Y,{...C?{as:$,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null==(n=G.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:M,children:(0,k.jsx)(w.bL,{asChild:!0,...T,dir:_.dir,orientation:"vertical",loop:l,currentTabStopId:P,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.m)(v,e=>{_.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eN(N.open),"data-radix-menu-content":"",dir:_.dir,...A,...D,ref:U,style:{outline:"none",...D.style},onKeyDown:(0,o.m)(D.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&J(e.key));let o=G.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=E().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),q.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{let n=e.target,r=X.current!==e.clientX;e.currentTarget.contains(n)&&r&&(z.current=e.clientX>X.current?"right":"left",X.current=e.clientX)}))})})})})})})});Y.displayName=Z;var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,k.jsx)(i.sG.div,{role:"group",...t,ref:n})});en.displayName="MenuGroup";var er=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,k.jsx)(i.sG.div,{...t,ref:n})});er.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:l,...u}=e,d=t.useRef(null),s=K(et,e.__scopeMenu),c=X(et,e.__scopeMenu),p=(0,a.s)(n,d),f=t.useRef(!1);return(0,k.jsx)(el,{...u,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!r&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;f.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;r||n&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:l=!1,textValue:u,...d}=e,s=X(et,r),c=S(r),p=t.useRef(null),f=(0,a.s)(n,p),[v,m]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var n;g((null!=(n=e.textContent)?n:"").trim())}},[d.children]),(0,k.jsx)(A.ItemSlot,{scope:r,disabled:l,textValue:null!=u?u:h,children:(0,k.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,k.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(r)?"mixed":r,...a,ref:n,"data-state":eA(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,es]=E(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,k.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,k.jsx)(en,{...o,ref:n})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=es(ep,e.__scopeMenu),l=r===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:n,"data-state":eA(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eh]=E(ev,{checked:!1}),eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=eh(ev,r);return(0,k.jsx)(g.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,k.jsx)(i.sG.span,{...o,ref:n,"data-state":eA(a.checked)})})});eg.displayName=ev;var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,k.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});ew.displayName="MenuSeparator";var ey=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,k.jsx)(m.i3,{...o,...t,ref:n})});ey.displayName="MenuArrow";var ex="MenuSub",[eM,eb]=E(ex),ek=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,l=F(ex,n),u=O(n),[i,d]=t.useState(null),[s,c]=t.useState(null),p=(0,x.c)(a);return t.useEffect(()=>(!1===l.open&&p(!1),()=>p(!1)),[l.open,p]),(0,k.jsx)(m.bL,{...u,children:(0,k.jsx)(L,{scope:n,open:o,onOpenChange:p,content:s,onContentChange:c,children:(0,k.jsx)(eM,{scope:n,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:i,onTriggerChange:d,children:r})})})};ek.displayName=ex;var eC="MenuSubTrigger",eR=t.forwardRef((e,n)=>{let r=F(eC,e.__scopeMenu),l=K(eC,e.__scopeMenu),u=eb(eC,e.__scopeMenu),i=X(eC,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,k.jsx)(B,{asChild:!0,...p,children:(0,k.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eN(r.open),...e,ref:(0,a.t)(n,u.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eI(n=>{i.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>{var n,t;f();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,a="right"===n,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&D[l.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});eR.displayName=eC;var ej="MenuSubContent",eD=t.forwardRef((e,n)=>{let r=V(Z,e.__scopeMenu),{forceMount:l=r.forceMount,...u}=e,i=F(Z,e.__scopeMenu),d=K(Z,e.__scopeMenu),s=eb(ej,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(n,c);return(0,k.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:l||i.open,children:(0,k.jsx)(A.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;d.isUsingKeyboardRef.current&&(null==(n=c.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=N[d.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function eN(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eA(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eI(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=ej;var eT="DropdownMenu",[eE,eP]=(0,l.A)(eT,[P]),eO=P(),[eS,eL]=eE(eT),eF=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eO(n),c=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eT});return(0,k.jsx)(eS,{scope:n,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,k.jsx)(U,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:r})})};eF.displayName=eT;var eG="DropdownMenuTrigger",eK=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...l}=e,u=eL(eG,r),d=eO(r);return(0,k.jsx)(B,{asChild:!0,...d,children:(0,k.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(n,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eG;var eU=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eO(n);return(0,k.jsx)(z,{...t,...r})};eU.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,l=eL(eB,r),u=eO(r),i=t.useRef(!1);return(0,k.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null==(n=l.triggerRef.current)||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eq.displayName=eB;var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(en,{...o,...t,ref:n})});eH.displayName="DropdownMenuGroup";var eV=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(er,{...o,...t,ref:n})});eV.displayName="DropdownMenuLabel";var ez=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(ea,{...o,...t,ref:n})});ez.displayName="DropdownMenuItem";var eZ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(eu,{...o,...t,ref:n})});eZ.displayName="DropdownMenuCheckboxItem";var eW=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(ec,{...o,...t,ref:n})});eW.displayName="DropdownMenuRadioGroup";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(ef,{...o,...t,ref:n})});eX.displayName="DropdownMenuRadioItem";var eY=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(eg,{...o,...t,ref:n})});eY.displayName="DropdownMenuItemIndicator";var eJ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(ew,{...o,...t,ref:n})});eJ.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(ey,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(eR,{...o,...t,ref:n})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,k.jsx)(eD,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eF,e1=eK,e2=eU,e4=eq,e5=eH,e9=eV,e6=ez,e7=eZ,e3=eW,e8=eX,ne=eY,nn=eJ,nr=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,l=eO(n),[i,d]=(0,u.i)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,k.jsx)(ek,{...l,open:i,onOpenChange:d,children:r})},nt=eQ,no=e$},7434:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9428:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(9946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9848:(e,n,r)=>{r.d(n,{RG:()=>M,bL:()=>A,q7:()=>I});var t=r(2115),o=r(5677),a=r(6459),l=r(3257),u=r(1549),i=r(3561),d=r(4347),s=r(293),c=r(7769),p=r(5191),f=r(5155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,a.N)(h),[x,M]=(0,u.A)(h,[y]),[b,k]=x(h),C=t.forwardRef((e,n)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(R,{...e,ref:n})})}));C.displayName=h;var R=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:M,preventScrollOnEntryFocus:k=!1,...C}=e,R=t.useRef(null),j=(0,l.s)(n,R),D=(0,p.jH)(i),[N,A]=(0,c.i)({prop:g,defaultProp:null!=y?y:null,onChange:x,caller:h}),[I,T]=t.useState(!1),E=(0,s.c)(M),P=w(r),O=t.useRef(!1),[S,L]=t.useState(0);return t.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,E),()=>e.removeEventListener(v,E)},[E]),(0,f.jsx)(b,{scope:r,orientation:a,dir:D,loop:u,currentTabStopId:N,onItemFocus:t.useCallback(e=>A(e),[A]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>L(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:I||0===S?-1:0,"data-orientation":a,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!I){let n=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=P().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),k)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),j="RovingFocusGroupItem",D=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:u,children:s,...c}=e,p=(0,i.B)(),v=u||p,m=k(j,r),h=m.currentTabStopId===v,y=w(r),{onFocusableItemAdd:x,onFocusableItemRemove:M,currentTabStopId:b}=m;return t.useEffect(()=>{if(a)return x(),()=>M()},[a,x,M]),(0,f.jsx)(g.ItemSlot,{scope:r,id:v,focusable:a,active:l,children:(0,f.jsx)(d.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...c,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return N[o]}(e,m.orientation,m.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=m.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>_(r))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=b}):s})})});D.displayName=j;var N={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var A=C,I=D}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[488],{2488:(e,t,r)=>{r.r(t),r.d(t,{default:()=>s});var a=r(5155),o=r(2115),n=r(3294);function s(e){let{children:t}=e,{initialize:r,isLoading:s}=(0,n.n)();return(0,o.useEffect)(()=>{r()},[r]),(0,a.jsx)(a.Fragment,{children:t})}},2956:(e,t,r)=>{r.d(t,{u:()=>n});var a=r(3464);class o{setupInterceptors(){this.instance.interceptors.request.use(e=>{{let t=localStorage.getItem("accessToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!r._retry){r._retry=!0;{let e=localStorage.getItem("refreshToken");if(e)try{let t=await this.instance.post("/auth/refresh",{refreshToken:e});if(t.data.success){let{accessToken:e,refreshToken:a}=t.data.data.tokens;return localStorage.setItem("accessToken",e),localStorage.setItem("refreshToken",a),r.headers.Authorization="Bearer ".concat(e),this.instance(r)}}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}}}return Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,r){try{return(await this.instance.post(e,t,r)).data}catch(e){throw this.handleError(e)}}async put(e,t,r){try{return(await this.instance.put(e,t,r)).data}catch(e){throw this.handleError(e)}}async patch(e,t,r){try{return(await this.instance.patch(e,t,r)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){var t,r;let a=Error((null==(t=e.response.data)?void 0:t.message)||(null==(r=e.response.data)?void 0:r.error)||"An error occurred");return a.response=e.response,a}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}constructor(){this.instance=a.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let n=new o},3294:(e,t,r)=>{r.d(t,{n:()=>s});var a=r(5453),o=r(6786),n=r(2956);let s=(0,a.v)()((0,o.Zr)((e,t)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async t=>{try{e({isLoading:!0,error:null});let r=await n.u.post("/auth/login",t);if(r.success&&r.data){let{user:t,tokens:a}=r.data;localStorage.setItem("accessToken",a.accessToken),localStorage.setItem("refreshToken",a.refreshToken),e({user:t,tokens:a,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Login failed")}catch(t){var r,a;throw e({error:(null==(a=t.response)||null==(r=a.data)?void 0:r.message)||t.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},register:async t=>{try{e({isLoading:!0,error:null});let r=await n.u.post("/auth/register",t);if(r.success&&r.data){let{user:t,tokens:a}=r.data;localStorage.setItem("accessToken",a.accessToken),localStorage.setItem("refreshToken",a.refreshToken),e({user:t,tokens:a,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Registration failed")}catch(t){var r,a;throw e({error:(null==(a=t.response)||null==(r=a.data)?void 0:r.message)||t.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},logout:()=>{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{let t=localStorage.getItem("refreshToken");if(!t)throw Error("No refresh token available");let r=await n.u.post("/auth/refresh",{refreshToken:t});if(r.success&&r.data){let{tokens:t}=r.data;localStorage.setItem("accessToken",t.accessToken),localStorage.setItem("refreshToken",t.refreshToken),e({tokens:t})}else throw Error("Token refresh failed")}catch(e){throw t().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let t=await n.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",t),t.success&&t.data)console.log("✅ Profile fetched successfully:",t.data.email),e({user:t.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(a){var t,r;throw console.error("❌ Profile fetch error:",a),e({error:(null==(r=a.response)||null==(t=r.data)?void 0:t.message)||a.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),a}},clearError:()=>e({error:null}),setLoading:t=>e({isLoading:t}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store...");let r=localStorage.getItem("accessToken"),a=localStorage.getItem("refreshToken");if(r&&a){console.log("\uD83D\uDD11 Found stored tokens, validating..."),e({tokens:{accessToken:r,refreshToken:a},isLoading:!0});try{await t().fetchProfile(),console.log("✅ Profile fetched successfully")}catch(e){console.log("❌ Profile fetch failed, trying to refresh token...");try{await t().refreshToken(),await t().fetchProfile(),console.log("✅ Token refreshed and profile fetched")}catch(e){console.log("❌ Token refresh failed, logging out"),t().logout()}}}else console.log("\uD83D\uDEAB No tokens found, setting loading to false"),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null})}catch(t){console.error("❌ Auth initialization error:",t),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))},5453:(e,t,r)=>{r.d(t,{v:()=>i});var a=r(2115);let o=e=>{let t,r=new Set,a=(e,a)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=a?a:"object"!=typeof o||null===o)?o:Object.assign({},t,o),r.forEach(r=>r(t,e))}},o=()=>t,n={setState:a,getState:o,getInitialState:()=>s,subscribe:e=>(r.add(e),()=>r.delete(e))},s=t=e(a,o,n);return n},n=e=>e?o(e):o,s=e=>e,l=e=>{let t=n(e),r=e=>(function(e,t=s){let r=a.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return a.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},i=e=>e?l(e):l},6786:(e,t,r)=>{r.d(t,{Zr:()=>o});let a=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>a(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>a(t)(e)}}},o=(e,t)=>(r,o,n)=>{let s,l={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let a=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=r.getItem(e))?t:null;return o instanceof Promise?o.then(a):a(o)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},i=!1,c=new Set,u=new Set,h=l.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},o,n);let d=()=>{let e=l.partialize({...o()});return h.setItem(l.name,{state:e,version:l.version})},g=n.setState;n.setState=(e,t)=>{g(e,t),d()};let f=e((...e)=>{r(...e),d()},o,n);n.getInitialState=()=>f;let m=()=>{var e,t;if(!h)return;i=!1,c.forEach(e=>{var t;return e(null!=(t=o())?t:f)});let n=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=o())?e:f))||void 0;return a(h.getItem.bind(h))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[a,n]=e;if(r(s=l.merge(n,null!=(t=o())?t:f),!0),a)return d()}).then(()=>{null==n||n(s,void 0),s=o(),i=!0,u.forEach(e=>e(s))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{l={...l,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>m(),hasHydrated:()=>i,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},l.skipHydration||m(),s||f}}}]);
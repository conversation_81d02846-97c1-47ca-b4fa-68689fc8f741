"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[796],{293:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(2115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},1549:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(2115),o=r(5155);function l(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,l){let u=n.createContext(l),i=r.length;r=[...r,l];let a=t=>{let{scope:r,children:l,...a}=t,c=r?.[e]?.[i]||u,s=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:l})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[i]||u,c=n.useContext(a);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},3561:(e,t,r)=>{r.d(t,{B:()=>a});var n,o=r(2115),l=r(8868),u=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function a(e){let[t,r]=o.useState(u());return(0,l.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},4347:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>i});var n=r(2115),o=r(7650),l=r(3232),u=r(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5191:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(2115);r(5155);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},5677:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},6459:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function l(e,t,r){var o=n(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}r.d(t,{N:()=>d});var u,i=r(2115),a=r(1549),c=r(3257),s=r(3232),f=r(5155);function d(e){let t=e+"CollectionProvider",[r,n]=(0,a.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,n=i.useRef(null),l=i.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};u.displayName=t;let d=e+"CollectionSlot",m=(0,s.TL)(d),p=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(d,r),u=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(m,{ref:u,children:n})});p.displayName=d;let v=e+"CollectionItemSlot",h="data-radix-collection-item",w=(0,s.TL)(v),y=i.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,u=i.useRef(null),a=(0,c.s)(t,u),s=l(v,r);return i.useEffect(()=>(s.itemMap.set(u,{ref:u,...o}),()=>void s.itemMap.delete(u))),(0,f.jsx)(w,{...{[h]:""},ref:a,children:n})});return y.displayName=v,[{Provider:u,Slot:p,ItemSlot:y},function(t){let r=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var m=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}u=new WeakMap},7769:(e,t,r)=>{r.d(t,{i:()=>i});var n,o=r(2115),l=r(8868),u=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,i,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),i=o.useRef(t);return u(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==r&&(i.current?.(r),l.current=r)},[r,l]),[r,n,i]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else i(t)},[c,e,i,a])]}Symbol("RADIX:SYNC_STATE")},8868:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(2115),o=globalThis?.document?n.useLayoutEffect:()=>{}},9946:(e,t,r)=>{r.d(t,{A:()=>f});var n=r(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),u=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:s="",children:f,iconNode:d,...m}=e;return(0,n.createElement)("svg",{ref:t,...c,width:o,height:o,stroke:r,strokeWidth:u?24*Number(l)/Number(o):l,className:i("lucide",s),...!f&&!a(m)&&{"aria-hidden":"true"},...m},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])}),f=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:a,...c}=r;return(0,n.createElement)(s,{ref:l,iconNode:t,className:i("lucide-".concat(o(u(e))),"lucide-".concat(e),a),...c})});return r.displayName=u(e),r}}}]);
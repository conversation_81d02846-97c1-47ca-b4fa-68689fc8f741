(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[286],{402:()=>{},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(5155),s=a(2115),o=a(9434);let n=s.forwardRef((e,t)=>{let{className:a,type:s,...n}=e;return(0,r.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...n})});n.displayName="Input"},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(5155);a(2115);var s=a(310),o=a(9434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{className:(0,o.cn)(n({variant:a}),t),...s})}},7481:(e,t,a)=>{"use strict";a.d(t,{d:()=>d});var r=a(2115);let s={toasts:[]},o=[];function n(e){switch(e.type){case"ADD_TOAST":s={...s,toasts:[...s.toasts,e.payload]};break;case"REMOVE_TOAST":s={...s,toasts:s.toasts.filter(t=>t.id!==e.payload)};break;case"CLEAR_TOASTS":s={...s,toasts:[]}}o.forEach(e=>e(s))}function l(e){let{title:t,description:a,variant:r="default",duration:s=5e3}=e,o=Math.random().toString(36).substr(2,9);return n({type:"ADD_TOAST",payload:{id:o,title:t,description:a,variant:r,duration:s}}),s>0&&setTimeout(()=>{n({type:"REMOVE_TOAST",payload:o})},s),o}function d(){let[e,t]=(0,r.useState)(s),a=(0,r.useCallback)(e=>(o.push(e),()=>{o=o.filter(t=>t!==e)}),[]);return(0,r.useCallback)(()=>{o=[]},[]),r.useEffect(()=>a(t),[a]),{toast:l,toasts:e.toasts,dismiss:e=>{n({type:"REMOVE_TOAST",payload:e})},clear:()=>{n({type:"CLEAR_TOASTS"})}}}},7571:()=>{},9508:(e,t,a)=>{"use strict";a.d(t,{OR:()=>E,IT:()=>T});var r=a(5155),s=a(2115),o=a(1154),n=a(1788),l=a(7434),d=a(4261),i=a(285),c=a(5500),u=a(3052),m=a(5196),f=a(9428),p=a(9434);let x=c.bL,b=c.l9;c.YJ,c.ZL,c.Pb,c.z6,s.forwardRef((e,t)=>{let{className:a,inset:s,children:o,...n}=e;return(0,r.jsxs)(c.ZP,{ref:t,className:(0,p.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",a),...n,children:[o,(0,r.jsx)(u.A,{className:"ml-auto h-4 w-4"})]})}).displayName=c.ZP.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.G5,{ref:t,className:(0,p.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})}).displayName=c.G5.displayName;let y=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...o}=e;return(0,r.jsx)(c.ZL,{children:(0,r.jsx)(c.UC,{ref:t,sideOffset:s,className:(0,p.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})})});y.displayName=c.UC.displayName;let h=s.forwardRef((e,t)=>{let{className:a,inset:s,...o}=e;return(0,r.jsx)(c.q7,{ref:t,className:(0,p.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",a),...o})});h.displayName=c.q7.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,checked:o,...n}=e;return(0,r.jsxs)(c.H_,{ref:t,className:(0,p.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:o,...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(c.VF,{children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})}),s]})}).displayName=c.H_.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,r.jsxs)(c.hN,{ref:t,className:(0,p.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(c.VF,{children:(0,r.jsx)(f.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=c.hN.displayName;let g=s.forwardRef((e,t)=>{let{className:a,inset:s,...o}=e;return(0,r.jsx)(c.JU,{ref:t,className:(0,p.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",a),...o})});g.displayName=c.JU.displayName;let v=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.wv,{ref:t,className:(0,p.cn)("-mx-1 my-1 h-px bg-muted",a),...s})});v.displayName=c.wv.displayName;var N=a(7481),w=a(3113);function j(e,t){return t.split(".").reduce((e,t)=>null==e?void 0:e[t],e)}let k={date:e=>e?new Date(e).toLocaleDateString():"",number:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return null==e?"":Number(e).toFixed(t)},boolean:e=>e?"Yes":"No"};function S(e){let{data:t,columns:a,filename:c="export",disabled:u=!1,variant:m="outline",size:f="sm",className:p}=e,[k,S]=(0,s.useState)(!1),{toast:E}=(0,N.d)(),T=async e=>{if(0===t.length)return void E({title:"No data to export",description:"There is no data available to export.",variant:"destructive"});S(!0);try{let r={filename:c,includeTimestamp:!0};"csv"===e?(!function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{filename:r="export",includeTimestamp:s=!0}=a,o=[t.map(e=>e.label),...e.map(e=>t.map(t=>{var a;let r=j(e,t.key);return t.formatter?t.formatter(r):(a=r,null==a?"":a instanceof Date?a.toISOString().split("T")[0]:"object"==typeof a?JSON.stringify(a):String(a))}))].map(e=>e.map(e=>'"'.concat(String(e).replace(/"/g,'""'),'"')).join(",")).join("\n"),n=s?"_".concat(new Date().toISOString().split("T")[0]):"";!function(e,t,a){let r=new Blob([e],{type:a}),s=window.URL.createObjectURL(r),o=document.createElement("a");o.href=s,o.download=t,o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(s)}(o,"".concat(r).concat(n,".csv"),"text/csv;charset=utf-8;")}(t,a,r),E({title:"Export successful",description:"Data exported to CSV format (".concat(t.length," records)")})):(!function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{filename:r="export",sheetName:s="Sheet1",includeTimestamp:o=!0}=a,n=w.Wp.book_new(),l=e.map(e=>{let a={};return t.forEach(t=>{let r=j(e,t.key);a[t.label]=t.formatter?t.formatter(r):r}),a}),d=w.Wp.json_to_sheet(l),i=t.map(e=>({wch:Math.max(e.label.length,15)}));d["!cols"]=i,w.Wp.book_append_sheet(n,d,s);let c=o?"_".concat(new Date().toISOString().split("T")[0]):"",u="".concat(r).concat(c,".xlsx");w._h(n,u)}(t,a,r),E({title:"Export successful",description:"Data exported to Excel format (".concat(t.length," records)")}))}catch(e){console.error("Export error:",e),E({title:"Export failed",description:"An error occurred while exporting the data.",variant:"destructive"})}finally{S(!1)}};return(0,r.jsxs)(x,{children:[(0,r.jsx)(b,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:m,size:f,disabled:u||k,className:p,children:[k?(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Export"]})}),(0,r.jsxs)(y,{align:"end",className:"w-48",children:[(0,r.jsx)(g,{children:"Export Format"}),(0,r.jsx)(v,{}),(0,r.jsxs)(h,{onClick:()=>T("csv"),disabled:k,className:"cursor-pointer",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{children:"CSV File"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Comma-separated values"})]})]}),(0,r.jsxs)(h,{onClick:()=>T("excel"),disabled:k,className:"cursor-pointer",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{children:"Excel File"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Microsoft Excel format"})]})]}),(0,r.jsx)(v,{}),(0,r.jsxs)(h,{disabled:!0,className:"text-xs text-muted-foreground",children:[t.length," records available"]})]})]})}function E(e){let{companies:t,...a}=e;return(0,r.jsx)(S,{data:t,columns:[{key:"name",label:"Company Name"},{key:"siret",label:"SIRET"},{key:"industryType",label:"Industry Type"},{key:"address",label:"Address"},{key:"contactPerson",label:"Contact Person"},{key:"contactEmail",label:"Contact Email"},{key:"createdAt",label:"Created Date",formatter:e=>e?new Date(e).toLocaleDateString():""}],filename:"companies",...a})}function T(e){let{history:t,...a}=e;return(0,r.jsx)(S,{data:t,columns:[{key:"wasteMaterial.type",label:"Waste Type"},{key:"treatmentMethod.name",label:"Treatment Method"},{key:"inputQuantity",label:"Input Quantity",formatter:e=>e?Number(e).toFixed(2):""},{key:"outputQuantity",label:"Output Quantity",formatter:e=>e?Number(e).toFixed(2):""},{key:"processorCompany.name",label:"Processor"},{key:"dateProcessed",label:"Date Processed",formatter:e=>e?new Date(e).toLocaleDateString():""},{key:"createdAt",label:"Created Date",formatter:e=>e?new Date(e).toLocaleDateString():""}],filename:"processing-history",...a})}k.date,k.date,k.number,k.number,k.date,k.date,k.boolean,k.date}}]);
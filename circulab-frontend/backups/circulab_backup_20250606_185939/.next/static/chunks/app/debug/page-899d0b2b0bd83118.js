(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[302],{2233:(e,t,r)=>{Promise.resolve().then(r.bind(r,7152))},2956:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});var s=r(3464);class a{setupInterceptors(){this.instance.interceptors.request.use(e=>{{let t=localStorage.getItem("accessToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!r._retry){r._retry=!0;{let e=localStorage.getItem("refreshToken");if(e)try{let t=await this.instance.post("/auth/refresh",{refreshToken:e});if(t.data.success){let{accessToken:e,refreshToken:s}=t.data.data.tokens;return localStorage.setItem("accessToken",e),localStorage.setItem("refreshToken",s),r.headers.Authorization="Bearer ".concat(e),this.instance(r)}}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}}}return Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,r){try{return(await this.instance.post(e,t,r)).data}catch(e){throw this.handleError(e)}}async put(e,t,r){try{return(await this.instance.put(e,t,r)).data}catch(e){throw this.handleError(e)}}async patch(e,t,r){try{return(await this.instance.patch(e,t,r)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){var t,r;let s=Error((null==(t=e.response.data)?void 0:t.message)||(null==(r=e.response.data)?void 0:r.error)||"An error occurred");return s.response=e.response,s}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}constructor(){this.instance=s.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let n=new a},3294:(e,t,r)=>{"use strict";r.d(t,{n:()=>o});var s=r(5453),a=r(6786),n=r(2956);let o=(0,s.v)()((0,a.Zr)((e,t)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async t=>{try{e({isLoading:!0,error:null});let r=await n.u.post("/auth/login",t);if(r.success&&r.data){let{user:t,tokens:s}=r.data;localStorage.setItem("accessToken",s.accessToken),localStorage.setItem("refreshToken",s.refreshToken),e({user:t,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Login failed")}catch(t){var r,s;throw e({error:(null==(s=t.response)||null==(r=s.data)?void 0:r.message)||t.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},register:async t=>{try{e({isLoading:!0,error:null});let r=await n.u.post("/auth/register",t);if(r.success&&r.data){let{user:t,tokens:s}=r.data;localStorage.setItem("accessToken",s.accessToken),localStorage.setItem("refreshToken",s.refreshToken),e({user:t,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Registration failed")}catch(t){var r,s;throw e({error:(null==(s=t.response)||null==(r=s.data)?void 0:r.message)||t.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},logout:()=>{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{let t=localStorage.getItem("refreshToken");if(!t)throw Error("No refresh token available");let r=await n.u.post("/auth/refresh",{refreshToken:t});if(r.success&&r.data){let{tokens:t}=r.data;localStorage.setItem("accessToken",t.accessToken),localStorage.setItem("refreshToken",t.refreshToken),e({tokens:t})}else throw Error("Token refresh failed")}catch(e){throw t().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let t=await n.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",t),t.success&&t.data)console.log("✅ Profile fetched successfully:",t.data.email),e({user:t.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(s){var t,r;throw console.error("❌ Profile fetch error:",s),e({error:(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||s.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),s}},clearError:()=>e({error:null}),setLoading:t=>e({isLoading:t}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store...");let r=localStorage.getItem("accessToken"),s=localStorage.getItem("refreshToken");if(r&&s){console.log("\uD83D\uDD11 Found stored tokens, validating..."),e({tokens:{accessToken:r,refreshToken:s},isLoading:!0});try{await t().fetchProfile(),console.log("✅ Profile fetched successfully")}catch(e){console.log("❌ Profile fetch failed, trying to refresh token...");try{await t().refreshToken(),await t().fetchProfile(),console.log("✅ Token refreshed and profile fetched")}catch(e){console.log("❌ Token refresh failed, logging out"),t().logout()}}}else console.log("\uD83D\uDEAB No tokens found, setting loading to false"),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null})}catch(t){console.error("❌ Auth initialization error:",t),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>i});var s=r(2115);let a=e=>{let t,r=new Set,s=(e,s)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=s?s:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,n={setState:s,getState:a,getInitialState:()=>o,subscribe:e=>(r.add(e),()=>r.delete(e))},o=t=e(s,a,n);return n},n=e=>e?a(e):a,o=e=>e,l=e=>{let t=n(e),r=e=>(function(e,t=o){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},i=e=>e?l(e):l},6786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>a});let s=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>s(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},a=(e,t)=>(r,a,n)=>{let o,l={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let s=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(s):s(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},i=!1,c=new Set,d=new Set,u=l.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},a,n);let h=()=>{let e=l.partialize({...a()});return u.setItem(l.name,{state:e,version:l.version})},g=n.setState;n.setState=(e,t)=>{g(e,t),h()};let m=e((...e)=>{r(...e),h()},a,n);n.getInitialState=()=>m;let f=()=>{var e,t;if(!u)return;i=!1,c.forEach(e=>{var t;return e(null!=(t=a())?t:m)});let n=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=a())?e:m))||void 0;return s(u.getItem.bind(u))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,n]=e;if(r(o=l.merge(n,null!=(t=a())?t:m),!0),s)return h()}).then(()=>{null==n||n(o,void 0),o=a(),i=!0,d.forEach(e=>e(o))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{l={...l,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>f(),hasHydrated:()=>i,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},l.skipHydration||f(),o||m}},7152:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(5155),a=r(2115),n=r(3294);function o(){let e=(0,n.n)(),[t,r]=(0,a.useState)(null),[o,l]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("http://localhost:4000/health"),t=await e.json();r({success:!0,data:t,status:e.status})}catch(e){r({success:!1,error:e instanceof Error?e.message:"Unknown error"})}})(),l({accessToken:o.getItem("accessToken"),refreshToken:o.getItem("refreshToken"),authStorage:o.getItem("auth-storage")})},[]),(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83D\uDD0D CircuLab Debug Page"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDD10 Auth Store State"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Is Loading:"})," ",e.isLoading?"✅ True":"❌ False"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Is Authenticated:"})," ",e.isAuthenticated?"✅ True":"❌ False"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"User:"})," ",e.user?"✅ Present":"❌ Null"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tokens:"})," ",e.tokens?"✅ Present":"❌ Null"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",e.error||"❌ None"]})]}),e.user&&(0,s.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,s.jsx)("strong",{children:"User Details:"}),(0,s.jsx)("pre",{className:"text-sm mt-2",children:JSON.stringify(e.user,null,2)})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83C\uDF10 API Connectivity Test"}),t?(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"p-4 rounded ".concat(t.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:[(0,s.jsx)("strong",{children:"Status:"})," ",t.success?"✅ Success":"❌ Failed",t.success&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("strong",{children:"Response:"})," ",t.data.status," (Status: ",t.status,")"]}),!t.success&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("strong",{children:"Error:"})," ",t.error]})]})}):(0,s.jsx)("div",{children:"Testing API..."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDCBE LocalStorage Data"}),o?(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Access Token:"})," ",o.accessToken?"✅ Present":"❌ None",o.accessToken&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[o.accessToken.substring(0,50),"..."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Refresh Token:"})," ",o.refreshToken?"✅ Present":"❌ None",o.refreshToken&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[o.refreshToken.substring(0,50),"..."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Auth Storage:"})," ",o.authStorage?"✅ Present":"❌ None",o.authStorage&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[o.authStorage.substring(0,100),"..."]})]})]})}):(0,s.jsx)("div",{children:"Loading localStorage data..."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDEE0️ Debug Actions"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:()=>e.initialize(),className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-4",children:"\uD83D\uDD04 Re-initialize Auth"}),(0,s.jsx)("button",{onClick:()=>{o.clear(),window.location.reload()},className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 mr-4",children:"\uD83D\uDDD1️ Clear Storage & Reload"}),(0,s.jsx)("button",{onClick:()=>window.location.href="/login",className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-4",children:"\uD83D\uDD10 Go to Login"}),(0,s.jsx)("button",{onClick:()=>window.location.href="/dashboard",className:"bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600",children:"\uD83D\uDCCA Go to Dashboard"})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(2233)),_N_E=e.O()}]);
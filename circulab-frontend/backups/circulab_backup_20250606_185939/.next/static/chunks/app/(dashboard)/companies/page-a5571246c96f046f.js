(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{928:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),c=t(2115),i=t(4616),r=t(7924),l=t(3227),n=t(2657),d=t(3717),m=t(2525),o=t(285),x=t(2523),h=t(6695),u=t(6126),j=t(5534),p=t(7481),f=t(9508);function N(){let[e,s]=(0,c.useState)([]),[t,N]=(0,c.useState)(!0),[g,v]=(0,c.useState)(""),[y,b]=(0,c.useState)(1),[A,w]=(0,c.useState)(1),[C,E]=(0,c.useState)(0),{toast:S}=(0,p.d)(),k=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{N(!0);let a=await j.yq.getAll({page:e,limit:10,search:t||void 0,sortBy:"createdAt",sortOrder:"desc"});a.success&&a.data&&(s(a.data.companies),E(a.data.total),w(a.data.totalPages),b(a.data.page))}catch(e){console.error("Error fetching companies:",e),S({title:"Error",description:"Failed to fetch companies",variant:"destructive"})}finally{N(!1)}};(0,c.useEffect)(()=>{k(1,g)},[g]);let P=e=>{v(e),b(1)},$=e=>{k(e,g)},_=async(e,s)=>{if(confirm("Are you sure you want to delete ".concat(s,"?")))try{await j.yq.delete(e),S({title:"Success",description:"Company deleted successfully"}),k(y,g)}catch(e){console.error("Error deleting company:",e),S({title:"Error",description:"Failed to delete company",variant:"destructive"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Companies"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage companies in the CircuLab platform"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(f.OR,{companies:e}),(0,a.jsxs)(o.$,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add Company"]})]})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsx)(h.ZB,{children:"Search Companies"}),(0,a.jsx)(h.BT,{children:"Find companies by name, contact person, or email"})]}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)("div",{className:"flex gap-4",children:(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(r.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(x.p,{placeholder:"Search companies...",value:g,onChange:e=>P(e.target.value),className:"pl-10"})]})})})]}),(0,a.jsx)("div",{className:"grid gap-4",children:t?(0,a.jsx)("div",{className:"text-center py-8",children:"Loading companies..."}):0===e.length?(0,a.jsx)(h.Zp,{children:(0,a.jsxs)(h.Wu,{className:"text-center py-8",children:[(0,a.jsx)(l.A,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No companies found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:g?"No companies match your search criteria.":"Get started by adding your first company."}),(0,a.jsxs)(o.$,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add Company"]})]})}):e.map(e=>(0,a.jsx)(h.Zp,{children:(0,a.jsx)(h.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),e.industryType&&(0,a.jsx)(u.E,{variant:"secondary",children:e.industryType})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.siret&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"SIRET"}),(0,a.jsx)("p",{className:"text-sm",children:e.siret})]}),e.address&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Address"}),(0,a.jsx)("p",{className:"text-sm",children:e.address})]}),e.contactPerson&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Contact Person"}),(0,a.jsx)("p",{className:"text-sm",children:e.contactPerson})]}),e.contactEmail&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Contact Email"}),(0,a.jsx)("p",{className:"text-sm",children:e.contactEmail})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-4 mt-4 text-sm text-muted-foreground",children:(0,a.jsxs)("span",{children:["Created ",new Date(e.createdAt).toLocaleDateString()]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,a.jsx)(o.$,{variant:"outline",size:"sm",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>_(e.id,e.name),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]})]})})},e.id))}),A>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>$(y-1),disabled:1===y,children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center px-4",children:["Page ",y," of ",A]}),(0,a.jsx)(o.$,{variant:"outline",onClick:()=>$(y+1),disabled:y===A,children:"Next"})]})]})}},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8255:(e,s,t)=>{Promise.resolve().then(t.bind(t,928))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,306,464,796,131,348,260,286,441,684,358],()=>s(8255)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[817],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155);t(2115);var a=t(3232),n=t(310),i=t(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:n,asChild:o=!1,...l}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:r})),...l})}},4287:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(5155),a=t(6695),n=t(285);function i(){return(0,s.jsx)("div",{className:"animate-fade-in",children:(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Analytics & Rapports"}),(0,s.jsx)(a.BT,{children:"Analysez les donn\xe9es de vos d\xe9chets et g\xe9n\xe9rez des rapports d\xe9taill\xe9s"})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"mx-auto w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Module Analytics"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Visualisez les tendances, g\xe9n\xe9rez des rapports et analysez l'efficacit\xe9 de votre gestion des d\xe9chets."}),(0,s.jsx)(n.$,{children:"Voir les Rapports"})]})})]})})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>d});var s=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});l.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},8898:(e,r,t)=>{Promise.resolve().then(t.bind(t,4287))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(7576),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[306,441,684,358],()=>r(8898)),_N_E=e.O()}]);
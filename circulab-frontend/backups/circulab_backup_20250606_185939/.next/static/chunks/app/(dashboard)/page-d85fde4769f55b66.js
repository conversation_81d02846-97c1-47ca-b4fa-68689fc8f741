(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[966],{792:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>Y});var t=a(5155),l=a(2115),r=a(6695),i=a(285),c=a(7313),n=a(1788),d=a(9946);let o=(0,d.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var x=a(9074);let h=(0,d.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),m=(0,d.A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);var u=a(2713),p=a(5453),f=a(5534);let g=(0,p.v)((e,s)=>({materials:[],currentMaterial:null,statistics:null,isLoading:!1,error:null,pagination:null,fetchMaterials:async s=>{try{e({isLoading:!0,error:null});let a=new URLSearchParams;s&&Object.entries(s).forEach(e=>{let[s,t]=e;null!=t&&a.append(s,t.toString())});let t=await f.nv.get("/waste-materials?".concat(a.toString()));if(t.success&&t.data)e({materials:t.data,pagination:t.pagination||null,isLoading:!1,error:null});else throw Error(t.message||"Failed to fetch waste materials")}catch(s){var a,t;throw e({error:(null==(t=s.response)||null==(a=t.data)?void 0:a.message)||s.message||"Failed to fetch waste materials",isLoading:!1}),s}},fetchMaterialById:async s=>{try{e({isLoading:!0,error:null});let a=await f.nv.get("/waste-materials/".concat(s));if(a.success&&a.data)e({currentMaterial:a.data,isLoading:!1,error:null});else throw Error(a.message||"Failed to fetch waste material")}catch(s){var a,t;throw e({error:(null==(t=s.response)||null==(a=t.data)?void 0:a.message)||s.message||"Failed to fetch waste material",isLoading:!1}),s}},createMaterial:async s=>{try{e({isLoading:!0,error:null});let a=await f.nv.post("/waste-materials",s);if(a.success&&a.data)return e(e=>({materials:[a.data,...e.materials],isLoading:!1,error:null})),a.data;throw Error(a.message||"Failed to create waste material")}catch(s){var a,t;throw e({error:(null==(t=s.response)||null==(a=t.data)?void 0:a.message)||s.message||"Failed to create waste material",isLoading:!1}),s}},updateMaterial:async(s,a)=>{try{e({isLoading:!0,error:null});let t=await f.nv.put("/waste-materials/".concat(s),a);if(t.success&&t.data)return e(e=>{var a;return{materials:e.materials.map(e=>e.id===s?t.data:e),currentMaterial:(null==(a=e.currentMaterial)?void 0:a.id)===s?t.data:e.currentMaterial,isLoading:!1,error:null}}),t.data;throw Error(t.message||"Failed to update waste material")}catch(s){var t,l;throw e({error:(null==(l=s.response)||null==(t=l.data)?void 0:t.message)||s.message||"Failed to update waste material",isLoading:!1}),s}},deleteMaterial:async s=>{try{e({isLoading:!0,error:null});let a=await f.nv.delete("/waste-materials/".concat(s));if(a.success)e(e=>{var a;return{materials:e.materials.filter(e=>e.id!==s),currentMaterial:(null==(a=e.currentMaterial)?void 0:a.id)===s?null:e.currentMaterial,isLoading:!1,error:null}});else throw Error(a.message||"Failed to delete waste material")}catch(s){var a,t;throw e({error:(null==(t=s.response)||null==(a=t.data)?void 0:a.message)||s.message||"Failed to delete waste material",isLoading:!1}),s}},fetchStatistics:async()=>{try{e({isLoading:!0,error:null});let s=await f.nv.get("/waste-materials/statistics");if(s.success&&s.data)e({statistics:s.data,isLoading:!1,error:null});else throw Error(s.message||"Failed to fetch statistics")}catch(t){var s,a;throw e({error:(null==(a=t.response)||null==(s=a.data)?void 0:s.message)||t.message||"Failed to fetch statistics",isLoading:!1}),t}},clearError:()=>e({error:null}),setLoading:s=>e({isLoading:s}),clearCurrentMaterial:()=>e({currentMaterial:null})}));var j=a(3257),v=a(1549),y=a(5677),b=a(7769),N=a(2739),w=a(3767),k=a(3029),M=a(4347),E="Checkbox",[C,L]=(0,v.A)(E),[P,A]=C(E);function R(e){let{__scopeCheckbox:s,checked:a,children:r,defaultChecked:i,disabled:c,form:n,name:d,onCheckedChange:o,required:x,value:h="on",internal_do_not_use_render:m}=e,[u,p]=(0,b.i)({prop:a,defaultProp:null!=i&&i,onChange:o,caller:E}),[f,g]=l.useState(null),[j,v]=l.useState(null),y=l.useRef(!1),N=!f||!!n||!!f.closest("form"),w={checked:u,disabled:c,setChecked:p,control:f,setControl:g,name:d,form:n,value:h,hasConsumerStoppedPropagationRef:y,required:x,defaultChecked:!O(i)&&i,isFormControl:N,bubbleInput:j,setBubbleInput:v};return(0,t.jsx)(P,{scope:s,...w,children:"function"==typeof m?m(w):r})}var B="CheckboxTrigger",T=l.forwardRef((e,s)=>{let{__scopeCheckbox:a,onKeyDown:r,onClick:i,...c}=e,{control:n,value:d,disabled:o,checked:x,required:h,setControl:m,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:f,bubbleInput:g}=A(B,a),v=(0,j.s)(s,m),b=l.useRef(x);return l.useEffect(()=>{let e=null==n?void 0:n.form;if(e){let s=()=>u(b.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[n,u]),(0,t.jsx)(M.sG.button,{type:"button",role:"checkbox","aria-checked":O(x)?"mixed":x,"aria-required":h,"data-state":D(x),"data-disabled":o?"":void 0,disabled:o,value:d,...c,ref:v,onKeyDown:(0,y.m)(r,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,y.m)(i,e=>{u(e=>!!O(e)||!e),g&&f&&(p.current=e.isPropagationStopped(),p.current||e.stopPropagation())})})});T.displayName=B;var Z=l.forwardRef((e,s)=>{let{__scopeCheckbox:a,name:l,checked:r,defaultChecked:i,required:c,disabled:n,value:d,onCheckedChange:o,form:x,...h}=e;return(0,t.jsx)(R,{__scopeCheckbox:a,checked:r,defaultChecked:i,disabled:n,required:c,onCheckedChange:o,name:l,form:x,value:d,internal_do_not_use_render:e=>{let{isFormControl:l}=e;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T,{...h,ref:s,__scopeCheckbox:a}),l&&(0,t.jsx)(z,{__scopeCheckbox:a})]})}})});Z.displayName=E;var W="CheckboxIndicator",S=l.forwardRef((e,s)=>{let{__scopeCheckbox:a,forceMount:l,...r}=e,i=A(W,a);return(0,t.jsx)(k.C,{present:l||O(i.checked)||!0===i.checked,children:(0,t.jsx)(M.sG.span,{"data-state":D(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s,style:{pointerEvents:"none",...e.style}})})});S.displayName=W;var F="CheckboxBubbleInput",z=l.forwardRef((e,s)=>{let{__scopeCheckbox:a,...r}=e,{control:i,hasConsumerStoppedPropagationRef:c,checked:n,defaultChecked:d,required:o,disabled:x,name:h,value:m,form:u,bubbleInput:p,setBubbleInput:f}=A(F,a),g=(0,j.s)(s,f),v=(0,N.Z)(n),y=(0,w.X)(i);l.useEffect(()=>{if(!p)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!c.current;if(v!==n&&e){let a=new Event("click",{bubbles:s});p.indeterminate=O(n),e.call(p,!O(n)&&n),p.dispatchEvent(a)}},[p,v,n,c]);let b=l.useRef(!O(n)&&n);return(0,t.jsx)(M.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=d?d:b.current,required:o,disabled:x,name:h,value:m,form:u,...r,tabIndex:-1,ref:g,style:{...r.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function O(e){return"indeterminate"===e}function D(e){return O(e)?"indeterminate":e?"checked":"unchecked"}z.displayName=F;var H=a(5196),I=a(9434);let X=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(Z,{ref:s,className:(0,I.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...l,children:(0,t.jsx)(S,{className:(0,I.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(H.A,{className:"h-4 w-4"})})})});function $(){let[e,s]=(0,l.useState)([{title:"Organization",options:[{id:"org1",label:"CircuLab HQ",checked:!1},{id:"org2",label:"Regional Office",checked:!1},{id:"org3",label:"Processing Center",checked:!1}]},{title:"Hub",options:[{id:"hub1",label:"North Hub",checked:!1},{id:"hub2",label:"South Hub",checked:!1},{id:"hub3",label:"Central Hub",checked:!1}]},{title:"Facility",options:[{id:"fac1",label:"Processing Plant A",checked:!1},{id:"fac2",label:"Processing Plant B",checked:!1},{id:"fac3",label:"Sorting Facility",checked:!1}]},{title:"Site",options:[{id:"site1",label:"Collection Point 1",checked:!1},{id:"site2",label:"Collection Point 2",checked:!1},{id:"site3",label:"Storage Site",checked:!1}]},{title:"Activity",options:[{id:"act1",label:"Collection",checked:!1},{id:"act2",label:"Processing",checked:!1},{id:"act3",label:"Recycling",checked:!1},{id:"act4",label:"Disposal",checked:!1}]},{title:"Scope",options:[{id:"scope1",label:"Organic Waste",checked:!1},{id:"scope2",label:"Plastic Waste",checked:!1},{id:"scope3",label:"Metal Waste",checked:!1}]},{title:"Location",options:[{id:"loc1",label:"Urban Areas",checked:!1},{id:"loc2",label:"Industrial Zones",checked:!1},{id:"loc3",label:"Rural Areas",checked:!1}]},{title:"Tags",options:[{id:"tag1",label:"High Priority",checked:!1},{id:"tag2",label:"Recyclable",checked:!1},{id:"tag3",label:"Hazardous",checked:!1}]},{title:"Time period",options:[{id:"time1",label:"Last 7 days",checked:!1},{id:"time2",label:"Last 30 days",checked:!0},{id:"time3",label:"Last 90 days",checked:!1},{id:"time4",label:"Last year",checked:!1}]}]),a=(a,t)=>{let l=[...e];l[a].options[t].checked=!l[a].options[t].checked,s(l)};return(0,t.jsx)("div",{className:"w-80 bg-gray-800 text-white h-full overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-6",children:"Filter options"}),(0,t.jsx)("div",{className:"space-y-6",children:e.map((e,s)=>(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:e.title}),(0,t.jsx)("div",{className:"space-y-2",children:e.options.map((e,l)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(X,{id:e.id,checked:e.checked,onCheckedChange:()=>a(s,l),className:"border-gray-500 data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500"}),(0,t.jsx)("label",{htmlFor:e.id,className:"text-sm text-gray-300 cursor-pointer hover:text-white transition-colors",children:e.label})]},e.id))})]},e.title))}),(0,t.jsx)(i.$,{onClick:()=>{console.log("Applying filters:",e)},className:"w-full mt-8 bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded",children:"APPLY FILTER"})]})})}function _(e){let{title:s,value:a,unit:l,trend:i,chartData:c}=e,{areaPath:n,linePath:d}=(e=>{if(0===e.length)return{areaPath:"",linePath:""};let s=Math.max(...e.map(e=>e.value)),a=Math.min(...e.map(e=>e.value)),t=s-a,l=e.map((s,l)=>{let r=10+l/(e.length-1)*280,i=90-(s.value-a)/t*80;return"".concat(r,",").concat(i)});return{areaPath:"M ".concat(10,",").concat(90," L ").concat(l.join(" L ")," L ").concat(290,",").concat(90," Z"),linePath:"M ".concat(l.join(" L "))}})(c);return(0,t.jsxs)(r.Zp,{className:"border-0 shadow-sm",children:[(0,t.jsx)(r.aR,{className:"pb-2",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:s})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:a}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:l}),i&&(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(i>0?"text-green-600":"text-red-600"),children:[i>0?"+":"",i,"%"]})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("svg",{width:"100%",height:"100",viewBox:"0 0 300 100",className:"overflow-visible",children:[(0,t.jsx)("defs",{children:(0,t.jsxs)("linearGradient",{id:"gradient-".concat(s),x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,t.jsx)("stop",{offset:"0%",stopColor:"#E91E63",stopOpacity:"0.3"}),(0,t.jsx)("stop",{offset:"100%",stopColor:"#E91E63",stopOpacity:"0.05"})]})}),(0,t.jsx)("path",{d:n,fill:"url(#gradient-".concat(s,")"),className:"transition-all duration-300"}),(0,t.jsx)("path",{d:d,fill:"none",stroke:"#E91E63",strokeWidth:"2",className:"transition-all duration-300"}),c.map((e,s)=>{let a=Math.max(...c.map(e=>e.value)),l=Math.min(...c.map(e=>e.value)),r=10+s/(c.length-1)*280,i=90-(e.value-l)/(a-l)*70;return(0,t.jsx)("circle",{cx:r,cy:i,r:"3",fill:"#E91E63",className:"hover:r-4 transition-all duration-200 cursor-pointer"},s)})]})})]})]})}function U(e){let{data:s}=e,a=s||[{month:"Jan",processed:85,collected:100},{month:"Feb",processed:92,collected:110},{month:"Mar",processed:78,collected:95},{month:"Apr",processed:88,collected:105},{month:"May",processed:95,collected:115},{month:"Jun",processed:82,collected:98}],l=Math.max(...a.flatMap(e=>[e.processed,e.collected]));return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"relative h-48",children:(0,t.jsxs)("svg",{width:"100%",height:"100%",viewBox:"0 0 400 200",className:"overflow-visible",children:[[0,25,50,75,100].map(e=>(0,t.jsxs)("g",{children:[(0,t.jsx)("line",{x1:"40",y1:160-e/l*120,x2:"360",y2:160-e/l*120,stroke:"#E5E7EB",strokeWidth:"1"}),(0,t.jsx)("text",{x:"35",y:165-e/l*120,textAnchor:"end",className:"text-xs fill-gray-500",children:e})]},e)),a.map((e,s)=>{let a=60+50*s,r=e.processed/l*120,i=e.collected/l*120;return(0,t.jsxs)("g",{children:[(0,t.jsx)("rect",{x:a,y:160-i,width:20,height:i,fill:"#E5E7EB",rx:"2"}),(0,t.jsx)("rect",{x:a,y:160-r,width:20,height:r,fill:"#E91E63",rx:"2",className:"hover:opacity-80 transition-opacity cursor-pointer"}),(0,t.jsx)("text",{x:a+10,y:"180",textAnchor:"middle",className:"text-xs fill-gray-600",children:e.month}),(0,t.jsx)("text",{x:a+10,y:155-r,textAnchor:"middle",className:"text-xs fill-gray-700 font-medium",children:e.processed})]},e.month)})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-pink-500 rounded"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Processed"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-gray-300 rounded"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Collected"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:[a.reduce((e,s)=>e+s.processed,0),"T"]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Total Processed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:[Math.round(a.reduce((e,s)=>e+s.processed,0)/a.reduce((e,s)=>e+s.collected,0)*100),"%"]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Efficiency Rate"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-gray-900",children:a.length}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Months Tracked"})]})]})]})}function V(e){let{title:s,percentage:a,categories:l}=e,i=2*Math.PI*45;return(0,t.jsxs)(r.Zp,{className:"border-0 shadow-sm",children:[(0,t.jsx)(r.aR,{className:"pb-4",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium text-gray-600",children:s})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("svg",{width:"120",height:"120",className:"transform -rotate-90",children:[(0,t.jsx)("circle",{cx:"60",cy:"60",r:45,stroke:"#E5E7EB",strokeWidth:"8",fill:"none"}),(0,t.jsx)("circle",{cx:"60",cy:"60",r:45,stroke:"#E91E63",strokeWidth:"8",fill:"none",strokeDasharray:i,strokeDashoffset:i-a/100*i,strokeLinecap:"round",className:"transition-all duration-1000 ease-out"})]}),(0,t.jsx)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:(0,t.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[a,"%"]})})]}),(0,t.jsx)("div",{className:"space-y-3 ml-6",children:l.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},s))})]})})]})}function q(e){let{viewMode:s}=e,[a,r]=(0,l.useState)(null),i=[{id:"1",name:"Collection Point A",x:20,y:30,value:45,type:"collection"},{id:"2",name:"Processing Plant B",x:60,y:25,value:78,type:"processing"},{id:"3",name:"Collection Point C",x:35,y:60,value:32,type:"collection"},{id:"4",name:"Disposal Site D",x:80,y:70,value:25,type:"disposal"},{id:"5",name:"Processing Plant E",x:15,y:75,value:56,type:"processing"},{id:"6",name:"Collection Point F",x:70,y:40,value:38,type:"collection"}],c=e=>{switch(e){case"collection":return"#22C55E";case"processing":return"#3B82F6";case"disposal":return"#EF4444";default:return"#6B7280"}},n=e=>Math.max(8,Math.min(20,e/4));return(0,t.jsxs)("div",{className:"w-full",children:["map"===s&&(0,t.jsxs)("div",{className:"relative w-full h-96 bg-gray-100 rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50",children:(0,t.jsxs)("svg",{className:"w-full h-full opacity-20",children:[(0,t.jsx)("defs",{children:(0,t.jsx)("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:(0,t.jsx)("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"#94A3B8",strokeWidth:"1"})})}),(0,t.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)"})]})}),i.map(e=>(0,t.jsxs)("div",{className:"absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110",style:{left:"".concat(e.x,"%"),top:"".concat(e.y,"%")},onClick:()=>r(e.id),children:[(0,t.jsx)("div",{className:"rounded-full shadow-lg border-2 border-white flex items-center justify-center",style:{backgroundColor:c(e.type),width:"".concat(n(e.value),"px"),height:"".concat(n(e.value),"px")},children:(0,t.jsx)("span",{className:"text-white text-xs font-bold",children:e.value})}),a===e.id&&(0,t.jsxs)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg shadow-lg p-2 min-w-max z-10",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.value,"T waste"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.type})]})]},e.id)),(0,t.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Legend"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("span",{className:"text-xs",children:"Collection"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,t.jsx)("span",{className:"text-xs",children:"Processing"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,t.jsx)("span",{className:"text-xs",children:"Disposal"})]})]})]})]}),"table"===s&&(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-sm",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Location"}),(0,t.jsx)("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Type"}),(0,t.jsx)("th",{className:"text-right py-2 px-3 font-medium text-gray-600",children:"Waste (T)"})]})}),(0,t.jsx)("tbody",{children:i.map(e=>(0,t.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-2 px-3",children:e.name}),(0,t.jsx)("td",{className:"py-2 px-3",children:(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize ".concat("collection"===e.type?"bg-green-100 text-green-800":"processing"===e.type?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"),children:e.type})}),(0,t.jsx)("td",{className:"py-2 px-3 text-right font-medium",children:e.value})]},e.id))})]})}),"chart"===s&&(0,t.jsx)("div",{className:"space-y-4",children:i.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.name}),(0,t.jsxs)("span",{className:"text-sm font-bold text-gray-900",children:[e.value,"T"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(e.value/Math.max(...i.map(e=>e.value))*100,"%"),backgroundColor:c(e.type)}})})]},e.id))}),"targets"===s&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:"85%"}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Collection Target"}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,t.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"85%"}})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:"92%"}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Processing Target"}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"92%"}})})]})]}),"yearly"===s&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:"2,847T"}),(0,t.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"Total waste processed this year"}),(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Q1"}),(0,t.jsx)("div",{className:"text-gray-500",children:"642T"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Q2"}),(0,t.jsx)("div",{className:"text-gray-500",children:"718T"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Q3"}),(0,t.jsx)("div",{className:"text-gray-500",children:"756T"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Q4"}),(0,t.jsx)("div",{className:"text-gray-500",children:"731T"})]})]})]})]})}X.displayName=Z.displayName;var G=a(6874),Q=a.n(G);let J=e=>{let{title:s,data:a,centerValue:l,centerLabel:i}=e,c=a.reduce((e,s)=>e+s.value,0),n=0,d=(e,s)=>{let a=Math.PI/180*(3.6*s-90),t=Math.PI/180*((s+e)*3.6-90),l=50+35*Math.cos(a),r=50+35*Math.sin(a),i=50+35*Math.cos(t),c=50+35*Math.sin(t);return"M 50 50 L ".concat(l," ").concat(r," A 35 35 0 ").concat(+(e>50)," 1 ").concat(i," ").concat(c," Z")};return(0,t.jsxs)(r.Zp,{className:"border-0 shadow-sm",children:[(0,t.jsx)(r.aR,{className:"pb-4",children:(0,t.jsx)(r.ZB,{className:"text-lg font-semibold",children:s})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("svg",{width:"200",height:"200",viewBox:"0 0 100 100",className:"transform -rotate-90",children:[a.map((e,s)=>{let a=e.value/c*100,l=d(a,n);return n+=a,(0,t.jsx)("path",{d:l,fill:e.color,className:"hover:opacity-80 transition-opacity cursor-pointer"},s)}),(0,t.jsx)("circle",{cx:"50",cy:"50",r:"20",fill:"white"})]}),l&&(0,t.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[(0,t.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:l}),i&&(0,t.jsx)("span",{className:"text-xs text-gray-500",children:i})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.label}),(0,t.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.value,"%"]})]},s))})]})})]})},K=e=>{let{title:s,data:a,subtitle:l}=e,i=Math.max(...a.map(e=>e.value));return(0,t.jsxs)(r.Zp,{className:"border-0 shadow-sm",children:[(0,t.jsxs)(r.aR,{className:"pb-4",children:[(0,t.jsx)(r.ZB,{className:"text-lg font-semibold",children:s}),l&&(0,t.jsx)(r.BT,{children:l})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,t.jsxs)("span",{className:"text-sm font-bold text-gray-900",children:[e.value,"T"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,t.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(e.value/i*100,"%"),backgroundColor:e.color}})})]},s))})})]})};function Y(){let{statistics:e,fetchStatistics:s}=g(),[a,d]=(0,l.useState)("overview"),[p,f]=(0,l.useState)("targets");(0,l.useEffect)(()=>{s()},[s]);let j=null==e?void 0:e.overview;return(0,t.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,t.jsx)($,{}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,t.jsxs)("div",{className:"px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboards"}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Export"]})})]}),(0,t.jsx)(c.tU,{value:a,onValueChange:d,className:"mt-4",children:(0,t.jsxs)(c.j7,{className:"grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6",children:[(0,t.jsx)(c.Xi,{value:"overview",children:"KPIs"}),(0,t.jsx)(c.Xi,{value:"waste",children:"Waste dashboard"}),(0,t.jsx)(c.Xi,{value:"processing",children:"Processing dashboard"}),(0,t.jsx)(c.Xi,{value:"efficiency",children:"Efficiency"}),(0,t.jsx)(c.Xi,{value:"materials",children:"Materials"}),(0,t.jsx)(c.Xi,{value:"analytics",children:"Analytics"})]})})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-auto p-6",children:(0,t.jsxs)(c.tU,{value:a,onValueChange:d,children:[(0,t.jsxs)(c.av,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsx)(_,{title:"Waste processed",value:"476,117.93",unit:"tonnes",trend:12.5,chartData:[{month:"Jan",value:4e5},{month:"Feb",value:42e4},{month:"Mar",value:45e4},{month:"Apr",value:476117}]}),(0,t.jsx)(V,{title:"Processing by category",percentage:75,categories:[{name:"Organic",color:"#E91E63",percentage:45},{name:"Plastic",color:"#9E9E9E",percentage:30},{name:"Metal",color:"#757575",percentage:25}]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,t.jsx)("div",{children:(0,t.jsx)(r.ZB,{children:"Waste generated"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(i.$,{variant:"targets"===p?"default":"outline",size:"sm",onClick:()=>f("targets"),children:[(0,t.jsx)(o,{className:"h-4 w-4 mr-1"}),"view targets"]}),(0,t.jsxs)(i.$,{variant:"yearly"===p?"default":"outline",size:"sm",onClick:()=>f("yearly"),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"yearly"]}),(0,t.jsxs)(i.$,{variant:"map"===p?"default":"outline",size:"sm",onClick:()=>f("map"),children:[(0,t.jsx)(h,{className:"h-4 w-4 mr-1"}),"map"]}),(0,t.jsxs)(i.$,{variant:"table"===p?"default":"outline",size:"sm",onClick:()=>f("table"),children:[(0,t.jsx)(m,{className:"h-4 w-4 mr-1"}),"table"]}),(0,t.jsxs)(i.$,{variant:"chart"===p?"default":"outline",size:"sm",onClick:()=>f("chart"),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"chart"]})]})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(q,{viewMode:p})})]})]}),(0,t.jsxs)(c.av,{value:"waste",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Waste"})}),(0,t.jsxs)(r.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==j?void 0:j.totalMaterials)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium",children:"Processing Rate"})}),(0,t.jsxs)(r.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+5% from last month"})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium",children:"Active Facilities"})}),(0,t.jsxs)(r.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==j?void 0:j.totalMaterials)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+3 new this month"})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(r.ZB,{className:"text-sm font-medium",children:"Efficiency Score"})}),(0,t.jsxs)(r.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2% from last week"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Processing Efficiency"}),(0,t.jsx)(r.BT,{children:"Monthly processing trends"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(U,{})})]}),(0,t.jsx)(J,{title:"Types de d\xe9chets",data:[{label:"Plastiques",value:35,color:"#22c55e"},{label:"M\xe9taux",value:25,color:"#3b82f6"},{label:"Organiques",value:20,color:"#f59e0b"},{label:"Carton",value:20,color:"#ef4444"}],centerValue:"100%",centerLabel:"Total"})]})]}),(0,t.jsx)(c.av,{value:"processing",className:"space-y-4",children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Processing Overview"}),(0,t.jsx)(r.BT,{children:"Track processing activities and efficiency"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,t.jsx)(K,{title:"Masses Collect\xe9es",data:[{label:"Plastiques",value:45,color:"#22c55e"},{label:"Carton",value:38,color:"#f59e0b"},{label:"Organiques",value:32,color:"#3b82f6"},{label:"M\xe9taux",value:28,color:"#ef4444"},{label:"Verre",value:25,color:"#8b5cf6"},{label:"Autres",value:20,color:"#6b7280"}],subtitle:"R\xe9partition par type de d\xe9chet"}),(0,t.jsx)(J,{title:"Traitements",data:[{label:"Recyclage",value:45,color:"#22c55e"},{label:"Valorisation",value:30,color:"#3b82f6"},{label:"Enfouissement",value:25,color:"#f59e0b"}],centerValue:"100%",centerLabel:"Total"})]})})]})}),(0,t.jsx)(c.av,{value:"efficiency",className:"space-y-4",children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Efficiency Metrics"}),(0,t.jsx)(r.BT,{children:"Monitor operational efficiency and optimization opportunities"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(J,{title:"Prestataires",data:[{label:"ETS Recyclage",value:40,color:"#22c55e"},{label:"Green Solutions",value:35,color:"#3b82f6"},{label:"EcoTech",value:25,color:"#f59e0b"}],centerValue:"3",centerLabel:"Actifs"})})]})}),(0,t.jsx)(c.av,{value:"materials",className:"space-y-4",children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Material Management"}),(0,t.jsx)(r.BT,{children:"Manage waste materials and inventory"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsx)(Q(),{href:"/materials",children:(0,t.jsx)(i.$,{className:"w-full h-16 flex-col space-y-2 hover:bg-primary/90 transition-colors",children:(0,t.jsx)("span",{className:"text-sm font-medium",children:"G\xe9rer les D\xe9chets"})})}),(0,t.jsx)(Q(),{href:"/analytics",children:(0,t.jsx)(i.$,{variant:"outline",className:"w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors",children:(0,t.jsx)("span",{className:"text-sm font-medium",children:"Voir Analytics"})})}),(0,t.jsx)(Q(),{href:"/treatment-methods",children:(0,t.jsx)(i.$,{variant:"outline",className:"w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors",children:(0,t.jsx)("span",{className:"text-sm font-medium",children:"Traitements"})})})]})})]})}),(0,t.jsx)(c.av,{value:"analytics",className:"space-y-4",children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Analytics & Reports"}),(0,t.jsx)(r.BT,{children:"Detailed analytics and reporting tools"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"Analytics dashboard will be implemented here."})})]})})]})})]})]})}},1788:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2739:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});var t=a(2115);function l(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},3767:(e,s,a)=>{"use strict";a.d(s,{X:()=>r});var t=a(2115),l=a(8868);function r(e){let[s,a]=t.useState(void 0);return(0,l.N)(()=>{if(e){a({width:e.offsetWidth,height:e.offsetHeight});let s=new ResizeObserver(s=>{let t,l;if(!Array.isArray(s)||!s.length)return;let r=s[0];if("borderBoxSize"in r){let e=r.borderBoxSize,s=Array.isArray(e)?e[0]:e;t=s.inlineSize,l=s.blockSize}else t=e.offsetWidth,l=e.offsetHeight;a({width:t,height:l})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}a(void 0)},[e]),s}},5177:(e,s,a)=>{Promise.resolve().then(a.bind(a,792))},5196:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>n,tU:()=>c});var t=a(5155),l=a(2115),r=a(3204),i=a(9434);let c=r.bL,n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});n.displayName=r.B8.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});d.displayName=r.l9.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});o.displayName=r.UC.displayName},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[306,464,796,962,612,260,441,684,358],()=>s(5177)),_N_E=e.O()}]);
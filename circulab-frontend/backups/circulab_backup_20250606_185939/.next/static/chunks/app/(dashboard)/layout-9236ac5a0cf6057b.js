(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(5155);t(2115);var a=t(3232),n=t(310),o=t(9434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:n,asChild:l=!1,...c}=e,d=l?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:n,className:r})),...c})}},2480:(e,r,t)=>{Promise.resolve().then(t.bind(t,6844))},2956:(e,r,t)=>{"use strict";t.d(r,{u:()=>n});var s=t(3464);class a{setupInterceptors(){this.instance.interceptors.request.use(e=>{{let r=localStorage.getItem("accessToken");r&&(e.headers.Authorization="Bearer ".concat(r))}return e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{var r;let t=e.config;if((null==(r=e.response)?void 0:r.status)===401&&!t._retry){t._retry=!0;{let e=localStorage.getItem("refreshToken");if(e)try{let r=await this.instance.post("/auth/refresh",{refreshToken:e});if(r.data.success){let{accessToken:e,refreshToken:s}=r.data.data.tokens;return localStorage.setItem("accessToken",e),localStorage.setItem("refreshToken",s),t.headers.Authorization="Bearer ".concat(e),this.instance(t)}}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}}}return Promise.reject(e)})}async get(e,r){try{return(await this.instance.get(e,r)).data}catch(e){throw this.handleError(e)}}async post(e,r,t){try{return(await this.instance.post(e,r,t)).data}catch(e){throw this.handleError(e)}}async put(e,r,t){try{return(await this.instance.put(e,r,t)).data}catch(e){throw this.handleError(e)}}async patch(e,r,t){try{return(await this.instance.patch(e,r,t)).data}catch(e){throw this.handleError(e)}}async delete(e,r){try{return(await this.instance.delete(e,r)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){var r,t;let s=Error((null==(r=e.response.data)?void 0:r.message)||(null==(t=e.response.data)?void 0:t.error)||"An error occurred");return s.response=e.response,s}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}constructor(){this.instance=s.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let n=new a},3294:(e,r,t)=>{"use strict";t.d(r,{n:()=>o});var s=t(5453),a=t(6786),n=t(2956);let o=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async r=>{try{e({isLoading:!0,error:null});let t=await n.u.post("/auth/login",r);if(t.success&&t.data){let{user:r,tokens:s}=t.data;localStorage.setItem("accessToken",s.accessToken),localStorage.setItem("refreshToken",s.refreshToken),e({user:r,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Login failed")}catch(r){var t,s;throw e({error:(null==(s=r.response)||null==(t=s.data)?void 0:t.message)||r.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},register:async r=>{try{e({isLoading:!0,error:null});let t=await n.u.post("/auth/register",r);if(t.success&&t.data){let{user:r,tokens:s}=t.data;localStorage.setItem("accessToken",s.accessToken),localStorage.setItem("refreshToken",s.refreshToken),e({user:r,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Registration failed")}catch(r){var t,s;throw e({error:(null==(s=r.response)||null==(t=s.data)?void 0:t.message)||r.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},logout:()=>{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{let r=localStorage.getItem("refreshToken");if(!r)throw Error("No refresh token available");let t=await n.u.post("/auth/refresh",{refreshToken:r});if(t.success&&t.data){let{tokens:r}=t.data;localStorage.setItem("accessToken",r.accessToken),localStorage.setItem("refreshToken",r.refreshToken),e({tokens:r})}else throw Error("Token refresh failed")}catch(e){throw r().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let r=await n.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",r),r.success&&r.data)console.log("✅ Profile fetched successfully:",r.data.email),e({user:r.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(s){var r,t;throw console.error("❌ Profile fetch error:",s),e({error:(null==(t=s.response)||null==(r=t.data)?void 0:r.message)||s.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),s}},clearError:()=>e({error:null}),setLoading:r=>e({isLoading:r}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store...");let t=localStorage.getItem("accessToken"),s=localStorage.getItem("refreshToken");if(t&&s){console.log("\uD83D\uDD11 Found stored tokens, validating..."),e({tokens:{accessToken:t,refreshToken:s},isLoading:!0});try{await r().fetchProfile(),console.log("✅ Profile fetched successfully")}catch(e){console.log("❌ Profile fetch failed, trying to refresh token...");try{await r().refreshToken(),await r().fetchProfile(),console.log("✅ Token refreshed and profile fetched")}catch(e){console.log("❌ Token refresh failed, logging out"),r().logout()}}}else console.log("\uD83D\uDEAB No tokens found, setting loading to false"),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null})}catch(r){console.error("❌ Auth initialization error:",r),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6786:(e,r,t)=>{"use strict";t.d(r,{Zr:()=>a});let s=e=>r=>{try{let t=e(r);if(t instanceof Promise)return t;return{then:e=>s(e)(t),catch(e){return this}}}catch(e){return{then(e){return this},catch:r=>s(r)(e)}}},a=(e,r)=>(t,a,n)=>{let o,i={storage:function(e,r){let t;try{t=e()}catch(e){return}return{getItem:e=>{var r;let s=e=>null===e?null:JSON.parse(e,void 0),a=null!=(r=t.getItem(e))?r:null;return a instanceof Promise?a.then(s):s(a)},setItem:(e,r)=>t.setItem(e,JSON.stringify(r,void 0)),removeItem:e=>t.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,r)=>({...r,...e}),...r},l=!1,c=new Set,d=new Set,h=i.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),t(...e)},a,n);let u=()=>{let e=i.partialize({...a()});return h.setItem(i.name,{state:e,version:i.version})},m=n.setState;n.setState=(e,r)=>{m(e,r),u()};let g=e((...e)=>{t(...e),u()},a,n);n.getInitialState=()=>g;let f=()=>{var e,r;if(!h)return;l=!1,c.forEach(e=>{var r;return e(null!=(r=a())?r:g)});let n=(null==(r=i.onRehydrateStorage)?void 0:r.call(i,null!=(e=a())?e:g))||void 0;return s(h.getItem.bind(h))(i.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===i.version)return[!1,e.state];else{if(i.migrate){let r=i.migrate(e.state,e.version);return r instanceof Promise?r.then(e=>[!0,e]):[!0,r]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var r;let[s,n]=e;if(t(o=i.merge(n,null!=(r=a())?r:g),!0),s)return u()}).then(()=>{null==n||n(o,void 0),o=a(),l=!0,d.forEach(e=>e(o))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{i={...i,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>l,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},i.skipHydration||f(),o||g}},6844:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(5155),a=t(2115),n=t(5695),o=t(3294),i=t(6874),l=t.n(i),c=t(9434);let d=e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})},h=e=>{let{className:r="w-6 h-6"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})},u=e=>{let{className:r="w-6 h-6"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})},m=[{name:"Cockpit",href:"/dashboard",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})})}},{name:"Collectes",href:"/materials",icon:d},{name:"Traitements",href:"/treatment-methods",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsxs)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}},{name:"Historique",href:"/processing-history",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}},{name:"Entreprises",href:"/companies",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}},{name:"Utilisateurs",href:"/users",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}},{name:"Notifications",href:"/notifications",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5zM4.868 19.462A17.173 17.173 0 0012 20c2.485 0 4.809-.503 6.868-1.462M6 10a6 6 0 1112 0v3.586l.707.707A1 1 0 0118 16H6a1 1 0 01-.707-1.707L6 13.586V10z"})})}},{name:"Rapports",href:"/analytics",icon:e=>{let{className:r="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}}];function g(){var e,r,t,i,g,f;let x=(0,n.usePathname)(),{user:v}=(0,o.n)(),[p,k]=(0,a.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsxs)("button",{type:"button",className:"fixed top-4 left-4 z-50 inline-flex items-center justify-center rounded-lg p-2 text-gray-600 bg-white shadow-lg border hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary transition-colors",onClick:()=>k(!p),children:[(0,s.jsx)("span",{className:"sr-only",children:p?"Close menu":"Open menu"}),p?(0,s.jsx)(u,{}):(0,s.jsx)(h,{})]})}),(0,s.jsx)("div",{className:(0,c.cn)("fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-xl border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0",p?"translate-x-0":"-translate-x-full lg:translate-x-0"),children:(0,s.jsxs)("div",{className:"flex h-full flex-col",children:[(0,s.jsx)("div",{className:"flex h-16 items-center justify-center border-b border-gray-200 bg-gradient-to-r from-primary/5 to-primary/10",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-primary rounded-lg flex items-center justify-center",children:(0,s.jsx)(d,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-primary",children:"CircuLab"})]})}),(0,s.jsx)("nav",{className:"flex-1 space-y-2 px-3 py-6",children:m.map(e=>{let r=x===e.href||x.startsWith(e.href+"/");return(0,s.jsxs)(l(),{href:e.href,className:(0,c.cn)("group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 relative",r?"bg-primary text-white shadow-lg shadow-primary/25":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"),onClick:()=>k(!1),children:[(0,s.jsx)(e.icon,{className:(0,c.cn)("mr-3 h-5 w-5 flex-shrink-0 transition-colors",r?"text-white":"text-gray-400 group-hover:text-gray-600")}),(0,s.jsx)("span",{className:"truncate",children:e.name}),r&&(0,s.jsx)("div",{className:"absolute right-3 w-2 h-2 bg-white rounded-full opacity-75"})]},e.name)})}),(0,s.jsx)("div",{className:"border-t border-gray-200 p-4 bg-gray-50/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-sm",children:(0,s.jsx)("span",{className:"text-sm font-semibold text-white",children:(null==v||null==(r=v.username)||null==(e=r[0])?void 0:e.toUpperCase())||(null==v||null==(i=v.email)||null==(t=i[0])?void 0:t.toUpperCase())||"U"})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:(null==v?void 0:v.username)||(null==v||null==(g=v.email)?void 0:g.split("@")[0])}),(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate",children:(null==v||null==(f=v.company)?void 0:f.name)||"No company"})]})]})})]})}),p&&(0,s.jsx)("div",{className:"fixed inset-0 z-30 bg-gray-600 bg-opacity-75 lg:hidden",onClick:()=>k(!1)})]})}var f=t(285);let x=()=>(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),v=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})});function p(){var e,r,t,a,i,l;let c=(0,n.useRouter)(),{user:d,logout:h}=(0,o.n)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-16 justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:"hidden lg:block",children:[(0,s.jsxs)("h1",{className:"text-lg font-semibold text-gray-900",children:["Welcome back, ",(null==d?void 0:d.username)||(null==d||null==(e=d.email)?void 0:e.split("@")[0])]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]}),(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"CircuLab"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,s.jsx)(v,{}),(0,s.jsx)("span",{className:"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white animate-pulse"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"hidden sm:block text-right",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 truncate max-w-32",children:(null==d?void 0:d.username)||(null==d?void 0:d.email)}),(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate max-w-32",children:(null==d||null==(r=d.company)?void 0:r.name)||"No company"})]}),(0,s.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-sm",children:(0,s.jsx)("span",{className:"text-xs font-semibold text-white",children:(null==d||null==(a=d.username)||null==(t=a[0])?void 0:t.toUpperCase())||(null==d||null==(l=d.email)||null==(i=l[0])?void 0:i.toUpperCase())||"U"})}),(0,s.jsxs)(f.$,{variant:"outline",size:"sm",onClick:()=>{h(),c.push("/login")},className:"flex items-center space-x-2 hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors",children:[(0,s.jsx)(x,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})]})})})}function k(e){let{children:r}=e,t=(0,n.useRouter)(),{isAuthenticated:i,user:l,isLoading:c}=(0,o.n)();return((0,a.useEffect)(()=>{c||i||t.push("/login")},[i,c,t]),c)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})}):i&&l?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50/50",children:[(0,s.jsx)(g,{}),(0,s.jsxs)("div",{className:"lg:pl-64",children:[(0,s.jsx)(p,{}),(0,s.jsx)("main",{className:"py-6 lg:py-8",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"animate-fade-in",children:r})})})]})]}):null}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(7576),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[306,464,962,441,684,358],()=>r(2480)),_N_E=e.O()}]);
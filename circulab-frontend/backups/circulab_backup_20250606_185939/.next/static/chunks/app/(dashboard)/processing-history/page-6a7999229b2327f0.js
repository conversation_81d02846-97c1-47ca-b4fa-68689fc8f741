(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[473],{2947:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(5155),r=t(2115),i=t(4616);let c=(0,t(9946).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var l=t(7108),d=t(3227),n=t(9074),o=t(2657),m=t(3717),x=t(2525),u=t(285),h=t(2523),p=t(6695),j=t(6126),g=t(5534),y=t(7481),v=t(9508);function f(){let[e,s]=(0,r.useState)([]),[t,f]=(0,r.useState)(!0),[N,w]=(0,r.useState)(""),[b,k]=(0,r.useState)(""),[A,M]=(0,r.useState)(""),[P,E]=(0,r.useState)(""),[S,C]=(0,r.useState)(1),[Q,T]=(0,r.useState)(1),[$,B]=(0,r.useState)(0),{toast:F}=(0,y.d)(),O=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{f(!0);let t=await g.gK.getAll({page:e,limit:10,wasteType:N||void 0,treatmentMethod:b||void 0,dateFrom:A||void 0,dateTo:P||void 0,sortBy:"dateProcessed",sortOrder:"desc"});t.success&&t.data&&(s(t.data.processingHistory),B(t.data.total),T(t.data.totalPages),C(t.data.page))}catch(e){console.error("Error fetching processing history:",e),F({title:"Error",description:"Failed to fetch processing history",variant:"destructive"})}finally{f(!1)}};(0,r.useEffect)(()=>{O(1)},[N,b,A,P]);let _=e=>{O(e)},D=async e=>{if(confirm("Are you sure you want to delete this processing record?"))try{await g.gK.delete(e),F({title:"Success",description:"Processing record deleted successfully"}),O(S)}catch(e){console.error("Error deleting processing record:",e),F({title:"Error",description:"Failed to delete processing record",variant:"destructive"})}},I=(e,s)=>null==e?"N/A":"".concat(e," ").concat(s),R=(e,s)=>e&&s&&0!==e?Math.round(s/e*100):null;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Processing History"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track waste material processing activities and outcomes"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.IT,{history:e}),(0,a.jsxs)(u.$,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Record Processing"]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Filter Processing Records"}),(0,a.jsx)(p.BT,{children:"Filter by waste type, treatment method, and date range"})]}),(0,a.jsx)(p.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(h.p,{placeholder:"Waste type...",value:N,onChange:e=>w(e.target.value)}),(0,a.jsx)(h.p,{placeholder:"Treatment method...",value:b,onChange:e=>k(e.target.value)}),(0,a.jsx)(h.p,{type:"date",placeholder:"From date",value:A,onChange:e=>M(e.target.value)}),(0,a.jsx)(h.p,{type:"date",placeholder:"To date",value:P,onChange:e=>E(e.target.value)})]})})]}),(0,a.jsx)("div",{className:"grid gap-4",children:t?(0,a.jsx)("div",{className:"text-center py-8",children:"Loading processing history..."}):0===e.length?(0,a.jsx)(p.Zp,{children:(0,a.jsxs)(p.Wu,{className:"text-center py-8",children:[(0,a.jsx)(c,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No processing records found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:N||b||A||P?"No records match your filter criteria.":"Get started by recording your first processing activity."}),(0,a.jsxs)(u.$,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Record Processing"]})]})}):e.map(e=>(0,a.jsx)(p.Zp,{children:(0,a.jsx)(p.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)(c,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("h3",{className:"text-lg font-semibold",children:[e.wasteMaterial.type," Processing"]}),(0,a.jsx)(j.E,{variant:"outline",children:e.treatmentMethod.name})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Waste Material"}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(l.A,{className:"h-3 w-3"}),I(e.wasteMaterial.quantity,e.wasteMaterial.unit)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Processor"}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"h-3 w-3"}),e.processorCompany.name]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Date Processed"}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"h-3 w-3"}),new Date(e.dateProcessed).toLocaleDateString()]})]}),e.inputQuantity&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Input Quantity"}),(0,a.jsx)("p",{className:"text-sm",children:I(e.inputQuantity,e.wasteMaterial.unit)})]}),e.outputQuantity&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Output Quantity"}),(0,a.jsx)("p",{className:"text-sm",children:I(e.outputQuantity,e.wasteMaterial.unit)})]}),e.inputQuantity&&e.outputQuantity&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Efficiency"}),(0,a.jsxs)("p",{className:"text-sm",children:[R(e.inputQuantity,e.outputQuantity),"%"]})]})]}),e.outputMaterialType&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Output Material Type"}),(0,a.jsx)("p",{className:"text-sm",children:e.outputMaterialType})]}),e.notes&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Notes"}),(0,a.jsx)("p",{className:"text-sm",children:e.notes})]}),e.environmentalImpactAchieved&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Environmental Impact"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:Object.entries(e.environmentalImpactAchieved).map(e=>{let[s,t]=e;return(0,a.jsxs)(j.E,{variant:"secondary",className:"text-xs",children:[s,": ",t]},s)})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Recorded ",new Date(e.createdAt).toLocaleDateString()]}),e.processedByUser&&(0,a.jsxs)("span",{children:["by ",e.processedByUser.username||e.processedByUser.email]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,a.jsx)(u.$,{variant:"outline",size:"sm",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>D(e.id),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]})})},e.id))}),Q>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)(u.$,{variant:"outline",onClick:()=>_(S-1),disabled:1===S,children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center px-4",children:["Page ",S," of ",Q]}),(0,a.jsx)(u.$,{variant:"outline",onClick:()=>_(S+1),disabled:S===Q,children:"Next"})]})]})}},4992:(e,s,t)=>{Promise.resolve().then(t.bind(t,2947))},7108:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[874,306,464,796,131,348,260,286,441,684,358],()=>s(4992)),_N_E=e.O()}]);
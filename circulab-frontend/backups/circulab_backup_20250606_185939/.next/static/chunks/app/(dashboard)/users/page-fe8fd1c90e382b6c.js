(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(5155),r=t(2115),l=t(9434);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},3227:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5078:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),r=t(2115),l=t(4616),i=t(7924),d=t(9946);let n=(0,d.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var c=t(5525);let o=(0,d.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var u=t(3227),m=t(2657),h=t(3717),x=t(2525),f=t(285),p=t(2523),v=t(6695),g=t(6126),j=t(9409),y=t(5534),b=t(7481);function N(){let[e,s]=(0,r.useState)([]),[t,d]=(0,r.useState)(!0),[N,w]=(0,r.useState)(""),[A,k]=(0,r.useState)(""),[S,C]=(0,r.useState)(""),[E,M]=(0,r.useState)(1),[T,_]=(0,r.useState)(1),[L,R]=(0,r.useState)(0),{toast:O}=(0,b.d)(),V=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3?arguments[3]:void 0;try{d(!0);let l=await y.dG.getAll({page:e,limit:10,search:t||void 0,role:a||void 0,isEmailVerified:r,sortBy:"createdAt",sortOrder:"desc"});l.success&&l.data&&(s(l.data.users),R(l.data.total),_(l.data.totalPages),M(l.data.page))}catch(e){console.error("Error fetching users:",e),O({title:"Error",description:"Failed to fetch users",variant:"destructive"})}finally{d(!1)}};(0,r.useEffect)(()=>{V(1,N,A,"verified"===S||"unverified"!==S&&void 0)},[N,A,S]);let q=e=>{w(e),M(1)},U=e=>{V(e,N,A,"verified"===S||"unverified"!==S&&void 0)},z=async(e,s)=>{if(confirm("Are you sure you want to delete user ".concat(s,"?")))try{await y.dG.delete(e),O({title:"Success",description:"User deleted successfully"});let s="verified"===S||"unverified"!==S&&void 0;V(E,N,A,s)}catch(e){console.error("Error deleting user:",e),O({title:"Error",description:"Failed to delete user",variant:"destructive"})}},P=e=>e.isEmailVerified?(0,a.jsx)(g.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Verified"}):(0,a.jsx)(g.E,{variant:"secondary",children:"Unverified"});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Users"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage users in the CircuLab platform"})]}),(0,a.jsxs)(f.$,{children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Add User"]})]}),(0,a.jsxs)(v.Zp,{children:[(0,a.jsxs)(v.aR,{children:[(0,a.jsx)(v.ZB,{children:"Search Users"}),(0,a.jsx)(v.BT,{children:"Find users by email, username, or filter by role and verification status"})]}),(0,a.jsx)(v.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(p.p,{placeholder:"Search users...",value:N,onChange:e=>q(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(j.l6,{value:A,onValueChange:k,children:[(0,a.jsx)(j.bq,{className:"w-48",children:(0,a.jsx)(j.yv,{placeholder:"Filter by role"})}),(0,a.jsxs)(j.gC,{children:[(0,a.jsx)(j.eb,{value:"",children:"All roles"}),(0,a.jsx)(j.eb,{value:"super_admin",children:"Super Admin"}),(0,a.jsx)(j.eb,{value:"company_admin",children:"Company Admin"}),(0,a.jsx)(j.eb,{value:"waste_producer",children:"Waste Producer"}),(0,a.jsx)(j.eb,{value:"waste_processor",children:"Waste Processor"}),(0,a.jsx)(j.eb,{value:"viewer",children:"Viewer"})]})]}),(0,a.jsxs)(j.l6,{value:S,onValueChange:C,children:[(0,a.jsx)(j.bq,{className:"w-48",children:(0,a.jsx)(j.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(j.gC,{children:[(0,a.jsx)(j.eb,{value:"",children:"All statuses"}),(0,a.jsx)(j.eb,{value:"verified",children:"Verified"}),(0,a.jsx)(j.eb,{value:"unverified",children:"Unverified"})]})]})]})})]}),(0,a.jsx)("div",{className:"grid gap-4",children:t?(0,a.jsx)("div",{className:"text-center py-8",children:"Loading users..."}):0===e.length?(0,a.jsx)(v.Zp,{children:(0,a.jsxs)(v.Wu,{className:"text-center py-8",children:[(0,a.jsx)(n,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No users found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:N||A||S?"No users match your search criteria.":"Get started by adding your first user."}),(0,a.jsxs)(f.$,{children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Add User"]})]})}):e.map(e=>(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(n,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.username||e.email}),P(e),e.roles.map(e=>(0,a.jsxs)(g.E,{variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-3 w-3 mr-1"}),e.role.name]},e.role.id))]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(o,{className:"h-3 w-3"}),e.email]})]}),e.company&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Company"}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),e.company.name]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Created"}),(0,a.jsx)("p",{className:"text-sm",children:new Date(e.createdAt).toLocaleDateString()})]}),e.lastLoginAt&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Last Login"}),(0,a.jsx)("p",{className:"text-sm",children:new Date(e.lastLoginAt).toLocaleDateString()})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,a.jsx)(f.$,{variant:"outline",size:"sm",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>z(e.id,e.email),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]})})},e.id))}),T>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)(f.$,{variant:"outline",onClick:()=>U(E-1),disabled:1===E,children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center px-4",children:["Page ",E," of ",T]}),(0,a.jsx)(f.$,{variant:"outline",onClick:()=>U(E+1),disabled:E===T,children:"Next"})]})]})}},5226:(e,s,t)=>{Promise.resolve().then(t.bind(t,5078))},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(5155);t(2115);var r=t(310),l=t(9434);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},7481:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(2115);let r={toasts:[]},l=[];function i(e){switch(e.type){case"ADD_TOAST":r={...r,toasts:[...r.toasts,e.payload]};break;case"REMOVE_TOAST":r={...r,toasts:r.toasts.filter(s=>s.id!==e.payload)};break;case"CLEAR_TOASTS":r={...r,toasts:[]}}l.forEach(e=>e(r))}function d(e){let{title:s,description:t,variant:a="default",duration:r=5e3}=e,l=Math.random().toString(36).substr(2,9);return i({type:"ADD_TOAST",payload:{id:l,title:s,description:t,variant:a,duration:r}}),r>0&&setTimeout(()=>{i({type:"REMOVE_TOAST",payload:l})},r),l}function n(){let[e,s]=(0,a.useState)(r),t=(0,a.useCallback)(e=>(l.push(e),()=>{l=l.filter(s=>s!==e)}),[]);return(0,a.useCallback)(()=>{l=[]},[]),a.useEffect(()=>t(s),[t]),{toast:d,toasts:e.toasts,dismiss:e=>{i({type:"REMOVE_TOAST",payload:e})},clear:()=>{i({type:"CLEAR_TOASTS"})}}}},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>p,gC:()=>f,l6:()=>o,yv:()=>u});var a=t(5155),r=t(2115),l=t(5703),i=t(6474),d=t(7863),n=t(5196),c=t(9434);let o=l.bL;l.YJ;let u=l.WT,m=r.forwardRef((e,s)=>{let{className:t,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});h.displayName=l.PP.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,position:i="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...d,children:[(0,a.jsx)(h,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(x,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[306,464,796,131,272,260,441,684,358],()=>s(5226)),_N_E=e.O()}]);
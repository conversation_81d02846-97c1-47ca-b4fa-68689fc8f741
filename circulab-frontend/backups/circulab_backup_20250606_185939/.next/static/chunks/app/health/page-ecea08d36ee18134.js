(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[653],{255:(e,t,s)=>{"use strict";function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),s(5155),s(7650),s(5744),s(589)},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(5155);s(2115);var a=s(3232),n=s(310),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:n,asChild:c=!1,...o}=e,d=c?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...o})}},775:(e,t,s)=>{"use strict";s.d(t,{SafeAuthProvider:()=>u,W$:()=>d});var r=s(5155),a=s(6645),n=s.n(a);function i(e){let{height:t="h-20",width:s="w-full",className:a=""}=e;return(0,r.jsx)("div",{className:"animate-pulse bg-gray-200 rounded ".concat(t," ").concat(s," ").concat(a),children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Loading..."})})})}function l(e){let{error:t,componentName:s,retry:a}=e;return(0,r.jsxs)("div",{className:"border border-red-200 bg-red-50 rounded-lg p-4 m-2",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("svg",{className:"h-5 w-5 text-red-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,r.jsxs)("h3",{className:"text-sm font-medium text-red-800",children:["Component Failed: ",s]})]}),(0,r.jsx)("p",{className:"text-sm text-red-700 mb-3",children:t.message||"Unknown error occurred"}),a&&(0,r.jsx)("button",{onClick:a,className:"text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded",children:"Retry Loading"}),!1]})}s(2115);class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}log(e,t){let s={timestamp:new Date,error:e,context:t};this.errors.push(s),this.errors.length>100&&(this.errors=this.errors.slice(-100))}getErrors(){return[...this.errors]}clearErrors(){this.errors=[]}constructor(){this.errors=[]}}function o(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{loading:a=()=>(0,r.jsx)(i,{}),ssr:o=!1,onError:d,fallback:u,retryCount:m=3,timeout:h=1e4}=s,x=0,f=c.getInstance();return n()(async()=>{try{let s=e(),r=new Promise((e,s)=>{setTimeout(()=>s(Error("Import timeout for ".concat(t))),h)}),a=await Promise.race([s,r]);return x=0,a}catch(s){if(f.log(s,"Safe import failed for ".concat(t)),d&&d(s),x<m)return x++,console.warn("Retrying import for ".concat(t," (attempt ").concat(x,"/").concat(m,")")),await new Promise(e=>setTimeout(e,1e3*Math.pow(2,x))),e();if(u)return{default:u};return{default:e=>(0,r.jsx)(l,{error:s,componentName:t,retry:()=>window.location.reload()})}}},{loading:a,ssr:o})}function d(){return c.getInstance()}let u=o(()=>Promise.all([s.e(464),s.e(488)]).then(s.bind(s,2488)),"AuthProvider",{loading:()=>(0,r.jsx)(i,{height:"h-screen"}),ssr:!1});o(()=>Promise.all([s.e(306),s.e(464),s.e(260),s.e(51)]).then(s.bind(s,4051)),"DashboardContent",{loading:()=>(0,r.jsx)(i,{height:"h-96"})})},2146:(e,t,s)=>{"use strict";function r(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),s(5262)},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return n},createSnapshot:function(){return l}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function n(){return a?new a:new r}function i(e){return a?a.bind(e):r.bind(e)}function l(){return a?a.snapshot():function(e,...t){return e(...t)}}},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=s(7828)},5978:(e,t,s)=>{Promise.resolve().then(s.bind(s,9351))},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(5155);s(2115);var a=s(310),n=s(9434);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},6645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(8229)._(s(7357));function a(e,t){var s;let a={};"function"==typeof e&&(a.loader=e);let n={...a,...t};return(0,r.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>l});var r=s(5155),a=s(2115),n=s(9434);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},7313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>l});var r=s(5155),a=s(2115),n=s(3204),i=s(9434);let l=n.bL,c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});c.displayName=n.B8.displayName;let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});o.displayName=n.l9.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=n.UC.displayName},7357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let r=s(5155),a=s(2115),n=s(2146);function i(e){return{default:e&&"default"in e?e.default:e}}s(255);let l={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},s=(0,a.lazy)(()=>t.loader().then(i)),c=t.loading;function o(e){let i=c?(0,r.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,o=l?a.Suspense:a.Fragment,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(s,{...e})]}):(0,r.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(o,{...l?{fallback:i}:{},children:d})}return o.displayName="LoadableComponent",o}},7828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,s(4054).createAsyncLocalStorage)()},9351:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>R});var r=s(5155),a=s(2115);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}validateModuleResolution(){try{let e=[];if(["react","next","@/components/providers/AuthProvider","@/store/authStore","@/lib/apiClient"].forEach(t=>{try{t.includes("@/")&&!this.checkModuleAvailability(t)&&e.push(t)}catch(s){e.push(t),this.moduleErrors.push("".concat(t,": ").concat(s.message))}}),0===e.length)return{status:"healthy",message:"All critical modules resolved successfully",timestamp:new Date};return{status:"error",message:"Failed to resolve ".concat(e.length," modules"),details:{failedModules:e,errors:this.moduleErrors},timestamp:new Date}}catch(e){return{status:"error",message:"Module resolution check failed",details:{error:e.message},timestamp:new Date}}}checkModuleAvailability(e){return!0}monitorHotReload(){let e=this.hotReloadIssues;return 0===e?{status:"healthy",message:"No hot reload issues detected",timestamp:new Date}:e<5?{status:"warning",message:"".concat(e," hot reload issues detected"),details:{reloadCount:e},timestamp:new Date}:{status:"error",message:"Excessive hot reload issues: ".concat(e),details:{reloadCount:e},timestamp:new Date}}reportHotReloadIssue(){this.hotReloadIssues++}reset(){this.moduleErrors=[],this.hotReloadIssues=0,this.lastCheck=new Date}constructor(){this.moduleErrors=[],this.hotReloadIssues=0,this.lastCheck=new Date}}class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}checkExtensionCompatibility(){let e=[],t=[];try{if(this.detectAugmentExtension()&&(e.push("IA AUGMENTE"),this.checkAugmentConflicts()&&t.push("IA AUGMENTE - Module resolution conflicts")),0===t.length)return{status:e.length>0?"warning":"healthy",message:e.length>0?"Extensions detected but no conflicts: ".concat(e.join(", ")):"No problematic extensions detected",details:{detectedExtensions:e},timestamp:new Date};return{status:"error",message:"Problematic extensions detected: ".concat(t.length),details:{detectedExtensions:e,problematicExtensions:t},timestamp:new Date}}catch(e){return{status:"warning",message:"Extension compatibility check failed",details:{error:e.message},timestamp:new Date}}}detectAugmentExtension(){return null!==document.querySelector("[data-augment]")||window.location.search.includes("augment")}checkAugmentConflicts(){return!1}}class l{static checkDependencies(){try{let e=[{name:"React",check:()=>void 0!==window.React},{name:"Next.js",check:()=>void 0!==window.next},{name:"Zustand",check:()=>!0},{name:"Axios",check:()=>!0}].filter(e=>{try{return!e.check()}catch(e){return!0}});if(0===e.length)return{status:"healthy",message:"All critical dependencies available",timestamp:new Date};return{status:"error",message:"Missing dependencies: ".concat(e.map(e=>e.name).join(", ")),details:{failedDeps:e.map(e=>e.name)},timestamp:new Date}}catch(e){return{status:"error",message:"Dependency check failed",details:{error:e.message},timestamp:new Date}}}}class c{static async checkAPIHealth(){try{let e=await fetch("/api/health",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)return{status:"error",message:"API returned ".concat(e.status,": ").concat(e.statusText),timestamp:new Date};{let t=await e.json();return{status:"healthy",message:"API is responding correctly",details:t,timestamp:new Date}}}catch(e){return{status:"error",message:"API health check failed",details:{error:e.message},timestamp:new Date}}}}class o{static checkLocalStorage(){try{let e="__health_check_test__",t="test";localStorage.setItem(e,t);let s=localStorage.getItem(e);if(localStorage.removeItem(e),s===t)return{status:"healthy",message:"localStorage is working correctly",timestamp:new Date};return{status:"error",message:"localStorage read/write test failed",timestamp:new Date}}catch(e){return{status:"error",message:"localStorage is not available",details:{error:e.message},timestamp:new Date}}}}class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}async performFullHealthCheck(){let e=this.webpackMonitor.validateModuleResolution(),t=this.extensionChecker.checkExtensionCompatibility(),s=l.checkDependencies(),r=await c.checkAPIHealth(),a=o.checkLocalStorage(),n=[e,t,s,r,a],i=n.filter(e=>"healthy"===e.status).length,d=n.filter(e=>"warning"===e.status).length,u=n.filter(e=>"error"===e.status).length,m=Math.round((100*i+50*d+0*u)/n.length),h="healthy",x="All systems operational";return u>0?(h="error",x="".concat(u," critical issues detected")):d>0&&(h="warning",x="".concat(d," warnings detected")),{webpack:e,extensions:t,dependencies:s,api:r,localStorage:a,overall:{status:h,message:x,details:{healthyCount:i,warningCount:d,errorCount:u},timestamp:new Date},confidence:m}}reportHotReloadIssue(){this.webpackMonitor.reportHotReloadIssue()}reset(){this.webpackMonitor.reset()}constructor(){this.webpackMonitor=n.getInstance(),this.extensionChecker=i.getInstance()}}var u=s(6695),m=s(6126),h=s(285),x=s(9946);let f=(0,x.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),g=(0,x.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),p=(0,x.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),j=(0,x.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var v=s(5525);let b=(0,x.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),y=(0,x.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),N=(0,x.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),w=(0,x.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),k=(0,x.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);function C(e){var t;let{isMinimized:s=!1,onToggle:n}=e,[i,l]=(0,a.useState)(null),[c,o]=(0,a.useState)(!1),[x,C]=(0,a.useState)(null),A=d.getInstance(),S=async()=>{o(!0);try{let e=await A.performFullHealthCheck();l(e),C(new Date)}catch(e){console.error("Health check failed:",e)}finally{o(!1)}};(0,a.useEffect)(()=>{S();let e=setInterval(S,3e4);return()=>clearInterval(e)},[]);let E=e=>{switch(e){case"healthy":return(0,r.jsx)(f,{className:"h-4 w-4 text-green-500"});case"warning":return(0,r.jsx)(g,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,r.jsx)(p,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(j,{className:"h-4 w-4 text-gray-500"})}},M=e=>{switch(e){case"healthy":return"bg-green-100 text-green-800 border-green-200";case"warning":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"error":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return s?(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,r.jsxs)(h.$,{onClick:n,variant:"outline",size:"sm",className:"shadow-lg ".concat(i?M(i.overall.status):"bg-gray-100"),children:[i&&E(i.overall.status),(0,r.jsxs)("span",{className:"ml-2",children:["Health: ",i?"".concat(i.confidence,"%"):"..."]})]})}):(0,r.jsxs)(u.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-5 w-5"}),"System Health Dashboard"]}),(0,r.jsx)(u.BT,{children:"Real-time monitoring of application health and stability"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[x&&(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Last check: ",x.toLocaleTimeString()]}),(0,r.jsxs)(h.$,{onClick:S,disabled:c,size:"sm",variant:"outline",children:[(0,r.jsx)(b,{className:"h-4 w-4 mr-2 ".concat(c?"animate-spin":"")}),"Refresh"]}),n&&(0,r.jsx)(h.$,{onClick:n,size:"sm",variant:"ghost",children:"Minimize"})]})]}),(0,r.jsx)(u.Wu,{children:i?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[E(i.overall.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"Overall System Health"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:i.overall.message})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold ".concat((t=i.confidence)>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600"),children:[i.confidence,"%"]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Confidence"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(y,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Webpack"}),E(i.webpack.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.webpack.message}),(0,r.jsx)(m.E,{variant:"outline",className:M(i.webpack.status),children:i.webpack.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(j,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Extensions"}),E(i.extensions.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.extensions.message}),(0,r.jsx)(m.E,{variant:"outline",className:M(i.extensions.status),children:i.extensions.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(N,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Dependencies"}),E(i.dependencies.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.dependencies.message}),(0,r.jsx)(m.E,{variant:"outline",className:M(i.dependencies.status),children:i.dependencies.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(w,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"API"}),E(i.api.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.api.message}),(0,r.jsx)(m.E,{variant:"outline",className:M(i.api.status),children:i.api.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(k,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Storage"}),E(i.localStorage.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.localStorage.message}),(0,r.jsx)(m.E,{variant:"outline",className:M(i.localStorage.status),children:i.localStorage.status})]})]}),!1,"healthy"!==i.overall.status&&(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Recommendations"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:["error"===i.webpack.status&&(0,r.jsx)("li",{children:"• Clear webpack cache and restart development server"}),"error"===i.extensions.status&&(0,r.jsx)("li",{children:"• Disable problematic VS Code extensions temporarily"}),"error"===i.dependencies.status&&(0,r.jsx)("li",{children:"• Run npm install to resolve missing dependencies"}),"error"===i.api.status&&(0,r.jsx)("li",{children:"• Check if the backend server is running on port 4000"}),"error"===i.localStorage.status&&(0,r.jsx)("li",{children:"• Clear browser cache and cookies"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(b,{className:"h-6 w-6 animate-spin mr-2"}),(0,r.jsx)("span",{children:"Performing initial health check..."})]})})]})}var A=s(2713);let S=(0,x.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function E(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(A.A,{className:"h-5 w-5"}),"Performance Metrics Dashboard"]}),(0,r.jsx)(u.BT,{children:"Monitor and validate the defensive architecture performance"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Metrics collection and analysis coming soon..."})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"System Uptime"}),(0,r.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[98,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 24 hours"}),(0,r.jsx)(m.E,{variant:"default",className:"mt-2",children:"Excellent"})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Avg Confidence"}),(0,r.jsx)(j,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[87,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"System health score"}),(0,r.jsx)(m.E,{variant:"default",className:"mt-2",children:"Healthy"})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Avg Recovery"}),(0,r.jsx)(S,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[4.2,"min"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Time to recover from errors"}),(0,r.jsx)(m.E,{variant:"default",className:"mt-2",children:"Fast"})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Error Reduction"}),(0,r.jsx)(f,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[73,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Compared to baseline"}),(0,r.jsx)(m.E,{variant:"default",className:"mt-2",children:"Improved"})]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsx)(u.ZB,{children:"Validation Criteria"}),(0,r.jsx)(u.BT,{children:"Target metrics for defensive architecture validation"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"✅ Success Criteria"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Confidence score maintained > 80% for 24h"}),(0,r.jsx)("li",{children:"• Recovery time < 5 minutes for common errors"}),(0,r.jsx)("li",{children:"• Zero development interruptions from extensions"}),(0,r.jsx)("li",{children:"• 70% reduction in debugging time"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"\uD83D\uDCCA Current Status"}),(0,r.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(f,{className:"h-4 w-4 text-green-500"}),"Confidence: ",87,"%"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(f,{className:"h-4 w-4 text-green-500"}),"Recovery: ",4.2,"min"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(f,{className:"h-4 w-4 text-green-500"}),"Error Reduction: ",73,"%"]})]})]})]})})]})]})}var M=s(775),D=s(7313);function R(){let[e,t]=(0,a.useState)(!0),s=(0,M.W$)(),n=s.getErrors();return(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"System Health & Monitoring"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Real-time system health monitoring, error tracking, and performance metrics"})]}),(0,r.jsxs)(D.tU,{defaultValue:"health",className:"w-full",children:[(0,r.jsxs)(D.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(D.Xi,{value:"health",children:"Health Dashboard"}),(0,r.jsx)(D.Xi,{value:"metrics",children:"Performance Metrics"})]}),(0,r.jsxs)(D.av,{value:"health",className:"space-y-6",children:[(0,r.jsx)(C,{isMinimized:!e,onToggle:()=>t(!e)}),n.length>0&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsxs)(u.ZB,{className:"flex items-center justify-between",children:["Error Log (",n.length,")",(0,r.jsx)(h.$,{onClick:()=>{s.clearErrors()},variant:"outline",size:"sm",children:"Clear Errors"})]}),(0,r.jsx)(u.BT,{children:"Recent errors captured by the safe import system"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"space-y-2 max-h-64 overflow-auto",children:n.map((e,t)=>(0,r.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-red-800",children:e.context}),(0,r.jsx)("span",{className:"text-xs text-red-600",children:e.timestamp.toLocaleTimeString()})]}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:e.error.message}),!1]},t))})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsx)(u.ZB,{children:"Health Monitoring Guide"}),(0,r.jsx)(u.BT,{children:"How to use the health monitoring system"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDFE2 Healthy Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"All systems are operating normally. Confidence score above 80%."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDFE1 Warning Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Some issues detected but system is functional. Confidence score 50-80%."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDD34 Error Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Critical issues detected. Confidence score below 50%. Immediate attention required."})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Monitored Components"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Webpack:"})," Module resolution and hot reload health"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Extensions:"})," VS Code extension compatibility"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Dependencies:"})," Critical package availability"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"API:"})," Backend connectivity and response"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Storage:"})," localStorage functionality"]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Automatic Actions"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Health checks run every 30 seconds"}),(0,r.jsx)("li",{children:"• Errors are automatically logged and categorized"}),(0,r.jsx)("li",{children:"• Safe imports provide fallbacks for failed components"}),(0,r.jsx)("li",{children:"• Recommendations are generated based on detected issues"})]})]})]})})]})]}),(0,r.jsx)(D.av,{value:"metrics",className:"space-y-6",children:(0,r.jsx)(E,{})})]})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(7576),a=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[306,796,612,441,684,358],()=>t(5978)),_N_E=e.O()}]);
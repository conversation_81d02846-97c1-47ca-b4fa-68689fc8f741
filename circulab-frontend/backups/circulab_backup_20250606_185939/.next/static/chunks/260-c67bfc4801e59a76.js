"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[260],{285:(e,t,a)=>{a.d(t,{$:()=>c});var r=a(5155);a(2115);var s=a(3232),n=a(310),o=a(9434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:n,asChild:c=!1,...d}=e,l=c?s.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:t})),...d})}},2956:(e,t,a)=>{a.d(t,{u:()=>n});var r=a(3464);class s{setupInterceptors(){this.instance.interceptors.request.use(e=>{{let t=localStorage.getItem("accessToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;{let e=localStorage.getItem("refreshToken");if(e)try{let t=await this.instance.post("/auth/refresh",{refreshToken:e});if(t.data.success){let{accessToken:e,refreshToken:r}=t.data.data.tokens;return localStorage.setItem("accessToken",e),localStorage.setItem("refreshToken",r),a.headers.Authorization="Bearer ".concat(e),this.instance(a)}}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}}}return Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,a){try{return(await this.instance.post(e,t,a)).data}catch(e){throw this.handleError(e)}}async put(e,t,a){try{return(await this.instance.put(e,t,a)).data}catch(e){throw this.handleError(e)}}async patch(e,t,a){try{return(await this.instance.patch(e,t,a)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){var t,a;let r=Error((null==(t=e.response.data)?void 0:t.message)||(null==(a=e.response.data)?void 0:a.error)||"An error occurred");return r.response=e.response,r}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}constructor(){this.instance=r.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let n=new s},5534:(e,t,a)=>{a.d(t,{uE:()=>l.u,yq:()=>u,nv:()=>S,vx:()=>m,gK:()=>g,dG:()=>p});var r=a(3464),s=function(e){return e.AVAILABLE="AVAILABLE",e.PENDING_PICKUP="PENDING_PICKUP",e.IN_TRANSIT="IN_TRANSIT",e.PROCESSING="PROCESSING",e.VALORIZED="VALORIZED",e.DISPOSED="DISPOSED",e.AWAITING_ANALYSIS="AWAITING_ANALYSIS",e}({});let n={id:"1",email:"<EMAIL>",username:"root",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),lastLoginAt:new Date().toISOString(),companyId:"1",company:{id:"1",name:"CircuLab Demo Company",siret:"12345678901234",address:"123 Demo Street, Demo City",industryType:"Manufacturing",contactPerson:"John Doe",contactEmail:"<EMAIL>",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},roles:[{role:{id:"1",name:"admin",description:"Administrator role"}}]},o=[{id:"1",type:"Plastic Waste",description:"Mixed plastic containers from manufacturing process",quantity:500,unit:"kg",source:"Production Line A",location_address:"123 Factory Street, Industrial Zone",currentStatus:s.AVAILABLE,producerCompanyId:"1",producerCompany:{id:"1",name:"CircuLab Demo Company",address:"123 Demo Street, Demo City"},isHazardous:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",type:"Metal Scraps",description:"Aluminum and steel scraps from machining",quantity:200,unit:"kg",source:"Machining Department",location_address:"456 Industrial Ave, Manufacturing District",currentStatus:s.PROCESSING,producerCompanyId:"1",producerCompany:{id:"1",name:"CircuLab Demo Company",address:"123 Demo Street, Demo City"},isHazardous:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],i={overview:{totalMaterials:15,availableMaterials:8,processingMaterials:4,valorizedMaterials:3,totalQuantity:2500},materialsByType:[{type:"Plastic Waste",_count:{type:5},_sum:{quantity:1200}},{type:"Metal Scraps",_count:{type:4},_sum:{quantity:800}},{type:"Organic Waste",_count:{type:6},_sum:{quantity:500}}],materialsByStatus:[{currentStatus:s.AVAILABLE,_count:{currentStatus:8},_sum:{quantity:1500}},{currentStatus:s.PROCESSING,_count:{currentStatus:4},_sum:{quantity:700}},{currentStatus:s.VALORIZED,_count:{currentStatus:3},_sum:{quantity:300}}]},c={login:async e=>{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e.email&&"Password123"===e.password)return{success:!0,data:{user:n,tokens:{accessToken:"mock-access-token",refreshToken:"mock-refresh-token"}},message:"Login successful",timestamp:new Date().toISOString()};throw Error("Invalid credentials")},register:async e=>(await new Promise(e=>setTimeout(e,1e3)),{success:!0,data:{user:{...n,email:e.email,username:e.username},tokens:{accessToken:"mock-access-token",refreshToken:"mock-refresh-token"}},message:"Registration successful",timestamp:new Date().toISOString()}),getProfile:async()=>(await new Promise(e=>setTimeout(e,500)),{success:!0,data:n,message:"Profile fetched successfully",timestamp:new Date().toISOString()}),refreshToken:async()=>(await new Promise(e=>setTimeout(e,500)),{success:!0,data:{tokens:{accessToken:"new-mock-access-token",refreshToken:"new-mock-refresh-token"}},message:"Token refreshed successfully",timestamp:new Date().toISOString()}),getMaterials:async()=>(await new Promise(e=>setTimeout(e,800)),{success:!0,data:o,message:"Materials fetched successfully",timestamp:new Date().toISOString()}),getStatistics:async()=>(await new Promise(e=>setTimeout(e,600)),{success:!0,data:i,message:"Statistics fetched successfully",timestamp:new Date().toISOString()})},d=()=>(console.log("\uD83D\uDD0D Mock API check:",{NEXT_PUBLIC_USE_REAL_API:"true",NODE_ENV:"production",shouldUseMock:!1}),console.log("✅ Using REAL API (explicitly set)"),!1);var l=a(2956);let u={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.industryType)&&t.append("industryType",e.industryType),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=t.toString();return l.u.get("/companies".concat(a?"?".concat(a):""))},getById:async e=>l.u.get("/companies/".concat(e)),create:async e=>l.u.post("/companies",e),update:async(e,t)=>l.u.put("/companies/".concat(e),t),delete:async e=>l.u.delete("/companies/".concat(e)),getStatistics:async()=>l.u.get("/companies/statistics")},p={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.companyId)&&t.append("companyId",e.companyId),(null==e?void 0:e.role)&&t.append("role",e.role),(null==e?void 0:e.isEmailVerified)!==void 0&&t.append("isEmailVerified",e.isEmailVerified.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=t.toString();return l.u.get("/users".concat(a?"?".concat(a):""))},getById:async e=>l.u.get("/users/".concat(e)),create:async e=>l.u.post("/users",e),update:async(e,t)=>l.u.put("/users/".concat(e),t),updatePassword:async(e,t)=>l.u.put("/users/".concat(e,"/password"),t),delete:async e=>l.u.delete("/users/".concat(e)),getStatistics:async()=>l.u.get("/users/statistics")},m={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.userId)&&t.append("userId",e.userId),(null==e?void 0:e.type)&&t.append("type",e.type),(null==e?void 0:e.isRead)!==void 0&&t.append("isRead",e.isRead.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=t.toString();return l.u.get("/notifications".concat(a?"?".concat(a):""))},getMy:async e=>{let t=new URLSearchParams;(null==e?void 0:e.type)&&t.append("type",e.type),(null==e?void 0:e.isRead)!==void 0&&t.append("isRead",e.isRead.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=t.toString();return l.u.get("/notifications/my".concat(a?"?".concat(a):""))},getByUserId:async(e,t)=>{let a=new URLSearchParams;(null==t?void 0:t.type)&&a.append("type",t.type),(null==t?void 0:t.isRead)!==void 0&&a.append("isRead",t.isRead.toString()),(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.sortBy)&&a.append("sortBy",t.sortBy),(null==t?void 0:t.sortOrder)&&a.append("sortOrder",t.sortOrder);let r=a.toString();return l.u.get("/notifications/user/".concat(e).concat(r?"?".concat(r):""))},getById:async e=>l.u.get("/notifications/".concat(e)),create:async e=>l.u.post("/notifications",e),createBulk:async e=>l.u.post("/notifications/bulk",e),update:async(e,t)=>l.u.put("/notifications/".concat(e),t),markAsRead:async e=>l.u.put("/notifications/".concat(e,"/read")),markAllAsRead:async e=>l.u.put("/notifications/mark-all-read",e),delete:async e=>l.u.delete("/notifications/".concat(e)),getStatistics:async()=>l.u.get("/notifications/statistics")},g={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.wasteMaterialId)&&t.append("wasteMaterialId",e.wasteMaterialId),(null==e?void 0:e.processedByUserId)&&t.append("processedByUserId",e.processedByUserId),(null==e?void 0:e.processorCompanyId)&&t.append("processorCompanyId",e.processorCompanyId),(null==e?void 0:e.treatmentMethodId)&&t.append("treatmentMethodId",e.treatmentMethodId),(null==e?void 0:e.wasteType)&&t.append("wasteType",e.wasteType),(null==e?void 0:e.treatmentMethod)&&t.append("treatmentMethod",e.treatmentMethod),(null==e?void 0:e.dateFrom)&&t.append("dateFrom",e.dateFrom),(null==e?void 0:e.dateTo)&&t.append("dateTo",e.dateTo),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let a=t.toString();return l.u.get("/processing-history".concat(a?"?".concat(a):""))},getById:async e=>l.u.get("/processing-history/".concat(e)),create:async e=>l.u.post("/processing-history",e),update:async(e,t)=>l.u.put("/processing-history/".concat(e),t),delete:async e=>l.u.delete("/processing-history/".concat(e)),getStatistics:async()=>l.u.get("/processing-history/statistics"),getEfficiency:async e=>{let t=new URLSearchParams;(null==e?void 0:e.companyId)&&t.append("companyId",e.companyId),(null==e?void 0:e.treatmentMethodId)&&t.append("treatmentMethodId",e.treatmentMethodId),(null==e?void 0:e.dateFrom)&&t.append("dateFrom",e.dateFrom),(null==e?void 0:e.dateTo)&&t.append("dateTo",e.dateTo);let a=t.toString();return l.u.get("/processing-history/efficiency".concat(a?"?".concat(a):""))}},y="http://localhost:4000/api",h=r.A.create({baseURL:y,timeout:1e4,headers:{"Content-Type":"application/json"}});h.interceptors.request.use(e=>{let t=localStorage.getItem("accessToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),h.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refreshToken");if(e){let{accessToken:t}=(await r.A.post("".concat(y,"/auth/refresh"),{refreshToken:e})).data.data.tokens;return localStorage.setItem("accessToken",t),a.headers.Authorization="Bearer ".concat(t),h(a)}}catch(e){return localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/login",Promise.reject(e)}}return Promise.reject(e)});let f={"POST:/auth/login":e=>c.login(e),"POST:/auth/register":e=>c.register(e),"GET:/auth/profile":()=>c.getProfile(),"POST:/auth/refresh":()=>c.refreshToken(),"GET:/waste-materials":()=>c.getMaterials(),"GET:/waste-materials/statistics":()=>c.getStatistics()},S={get:async(e,t)=>{if(d()){console.log("\uD83D\uDD04 Using Mock API for GET:",e);let t=f["GET:".concat(e.split("?")[0])];if(t)try{return await t()}catch(e){throw Error(e.message||"Mock API error")}}return console.log("\uD83C\uDF10 Using Real API for GET:",e),h.get(e,t).then(e=>e.data)},post:async(e,t,a)=>{if(d()){console.log("\uD83D\uDD04 Using Mock API for POST:",e);let a=f["POST:".concat(e)];if(a)try{return await a(t)}catch(e){throw Error(e.message||"Mock API error")}}return console.log("\uD83C\uDF10 Using Real API for POST:",e),h.post(e,t,a).then(e=>e.data)},put:async(e,t,a)=>d()?{success:!0,data:t,message:"Mock update successful",timestamp:new Date().toISOString()}:h.put(e,t,a).then(e=>e.data),delete:async(e,t)=>d()?{success:!0,message:"Mock delete successful",timestamp:new Date().toISOString()}:h.delete(e,t).then(e=>e.data),patch:async(e,t,a)=>d()?{success:!0,data:t,message:"Mock patch successful",timestamp:new Date().toISOString()}:h.patch(e,t,a).then(e=>e.data)}},6695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>c,Zp:()=>o,aR:()=>i});var r=a(5155),s=a(2115),n=a(9434);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});o.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});i.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});c.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})});d.displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});l.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(7576),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);
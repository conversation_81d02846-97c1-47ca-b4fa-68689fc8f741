"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{285:(e,r,t)=>{t.d(r,{$:()=>l});var a=t(5155);t(2115);var s=t(3232),o=t(310),n=t(9434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:o,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:o,className:r})),...c})}},2523:(e,r,t)=>{t.d(r,{p:()=>n});var a=t(5155),s=t(2115),o=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});n.displayName="Input"},2956:(e,r,t)=>{t.d(r,{u:()=>o});var a=t(3464);class s{setupInterceptors(){this.instance.interceptors.request.use(e=>{{let r=localStorage.getItem("accessToken");r&&(e.headers.Authorization="Bearer ".concat(r))}return e},e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{var r;let t=e.config;if((null==(r=e.response)?void 0:r.status)===401&&!t._retry){t._retry=!0;{let e=localStorage.getItem("refreshToken");if(e)try{let r=await this.instance.post("/auth/refresh",{refreshToken:e});if(r.data.success){let{accessToken:e,refreshToken:a}=r.data.data.tokens;return localStorage.setItem("accessToken",e),localStorage.setItem("refreshToken",a),t.headers.Authorization="Bearer ".concat(e),this.instance(t)}}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}}}return Promise.reject(e)})}async get(e,r){try{return(await this.instance.get(e,r)).data}catch(e){throw this.handleError(e)}}async post(e,r,t){try{return(await this.instance.post(e,r,t)).data}catch(e){throw this.handleError(e)}}async put(e,r,t){try{return(await this.instance.put(e,r,t)).data}catch(e){throw this.handleError(e)}}async patch(e,r,t){try{return(await this.instance.patch(e,r,t)).data}catch(e){throw this.handleError(e)}}async delete(e,r){try{return(await this.instance.delete(e,r)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){var r,t;let a=Error((null==(r=e.response.data)?void 0:r.message)||(null==(t=e.response.data)?void 0:t.error)||"An error occurred");return a.response=e.response,a}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}constructor(){this.instance=a.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let o=new s},3294:(e,r,t)=>{t.d(r,{n:()=>n});var a=t(5453),s=t(6786),o=t(2956);let n=(0,a.v)()((0,s.Zr)((e,r)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async r=>{try{e({isLoading:!0,error:null});let t=await o.u.post("/auth/login",r);if(t.success&&t.data){let{user:r,tokens:a}=t.data;localStorage.setItem("accessToken",a.accessToken),localStorage.setItem("refreshToken",a.refreshToken),e({user:r,tokens:a,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Login failed")}catch(r){var t,a;throw e({error:(null==(a=r.response)||null==(t=a.data)?void 0:t.message)||r.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},register:async r=>{try{e({isLoading:!0,error:null});let t=await o.u.post("/auth/register",r);if(t.success&&t.data){let{user:r,tokens:a}=t.data;localStorage.setItem("accessToken",a.accessToken),localStorage.setItem("refreshToken",a.refreshToken),e({user:r,tokens:a,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Registration failed")}catch(r){var t,a;throw e({error:(null==(a=r.response)||null==(t=a.data)?void 0:t.message)||r.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},logout:()=>{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{let r=localStorage.getItem("refreshToken");if(!r)throw Error("No refresh token available");let t=await o.u.post("/auth/refresh",{refreshToken:r});if(t.success&&t.data){let{tokens:r}=t.data;localStorage.setItem("accessToken",r.accessToken),localStorage.setItem("refreshToken",r.refreshToken),e({tokens:r})}else throw Error("Token refresh failed")}catch(e){throw r().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let r=await o.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",r),r.success&&r.data)console.log("✅ Profile fetched successfully:",r.data.email),e({user:r.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(a){var r,t;throw console.error("❌ Profile fetch error:",a),e({error:(null==(t=a.response)||null==(r=t.data)?void 0:r.message)||a.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),a}},clearError:()=>e({error:null}),setLoading:r=>e({isLoading:r}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store...");let t=localStorage.getItem("accessToken"),a=localStorage.getItem("refreshToken");if(t&&a){console.log("\uD83D\uDD11 Found stored tokens, validating..."),e({tokens:{accessToken:t,refreshToken:a},isLoading:!0});try{await r().fetchProfile(),console.log("✅ Profile fetched successfully")}catch(e){console.log("❌ Profile fetch failed, trying to refresh token...");try{await r().refreshToken(),await r().fetchProfile(),console.log("✅ Token refreshed and profile fetched")}catch(e){console.log("❌ Token refresh failed, logging out"),r().logout()}}}else console.log("\uD83D\uDEAB No tokens found, setting loading to false"),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null})}catch(r){console.error("❌ Auth initialization error:",r),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))},5057:(e,r,t)=>{t.d(r,{J:()=>c});var a=t(5155),s=t(2115),o=t(3748),n=t(310),i=t(9434);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(o.b,{ref:r,className:(0,i.cn)(l(),t),...s})});c.displayName=o.b.displayName},6695:(e,r,t)=>{t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=t(5155),s=t(2115),o=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});n.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",t),...s})});d.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"},9434:(e,r,t)=>{t.d(r,{cn:()=>o});var a=t(7576),s=t(9688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}}]);
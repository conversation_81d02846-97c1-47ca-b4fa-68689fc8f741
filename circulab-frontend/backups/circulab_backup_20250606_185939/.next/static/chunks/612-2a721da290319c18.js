"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[612],{2713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3029:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(3257),a=n(8868),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},3204:(e,t,n)=>{n.d(t,{B8:()=>F,UC:()=>C,bL:()=>D,l9:()=>x});var r=n(2115),o=n(5677),a=n(1549),i=n(9848),u=n(3029),l=n(4347),s=n(5191),c=n(7769),d=n(3561),f=n(5155),m="Tabs",[p,v]=(0,a.A)(m,[i.RG]),b=(0,i.RG)(),[h,w]=p(m),y=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:p="automatic",...v}=e,b=(0,s.jH)(u),[w,y]=(0,c.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:m});return(0,f.jsx)(h,{scope:n,baseId:(0,d.B)(),value:w,onValueChange:y,orientation:i,dir:b,activationMode:p,children:(0,f.jsx)(l.sG.div,{dir:b,"data-orientation":i,...v,ref:t})})});y.displayName=m;var g="TabsList",N=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=w(g,n),u=b(n);return(0,f.jsx)(i.bL,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:r,children:(0,f.jsx)(l.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});N.displayName=g;var T="TabsTrigger",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...u}=e,s=w(T,n),c=b(n),d=M(s.baseId,r),m=E(s.baseId,r),p=r===s.value;return(0,f.jsx)(i.q7,{asChild:!0,...c,focusable:!a,active:p,children:(0,f.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;p||a||!e||s.onValueChange(r)})})})});R.displayName=T;var A="TabsContent",I=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...s}=e,c=w(A,n),d=M(c.baseId,o),m=E(c.baseId,o),p=o===c.value,v=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(u.C,{present:a||p,children:n=>{let{present:r}=n;return(0,f.jsx)(l.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:m,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&i})}})});function M(e,t){return"".concat(e,"-trigger-").concat(t)}function E(e,t){return"".concat(e,"-content-").concat(t)}I.displayName=A;var D=y,F=N,x=R,C=I},9848:(e,t,n)=>{n.d(t,{RG:()=>N,bL:()=>x,q7:()=>C});var r=n(2115),o=n(5677),a=n(6459),i=n(3257),u=n(1549),l=n(3561),s=n(4347),c=n(293),d=n(7769),f=n(5191),m=n(5155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[h,w,y]=(0,a.N)(b),[g,N]=(0,u.A)(b,[y]),[T,R]=g(b),A=r.forwardRef((e,t)=>(0,m.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(I,{...e,ref:t})})}));A.displayName=b;var I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:g,onEntryFocus:N,preventScrollOnEntryFocus:R=!1,...A}=e,I=r.useRef(null),M=(0,i.s)(t,I),E=(0,f.jH)(l),[D,x]=(0,d.i)({prop:h,defaultProp:null!=y?y:null,onChange:g,caller:b}),[C,j]=r.useState(!1),O=(0,c.c)(N),k=w(n),L=r.useRef(!1),[U,G]=r.useState(0);return r.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,m.jsx)(T,{scope:n,orientation:a,dir:E,loop:u,currentTabStopId:D,onItemFocus:r.useCallback(e=>x(e),[x]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>G(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:C||0===U?-1:0,"data-orientation":a,...A,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),R)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),M="RovingFocusGroupItem",E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,children:c,...d}=e,f=(0,l.B)(),p=u||f,v=R(M,n),b=v.currentTabStopId===p,y=w(n),{onFocusableItemAdd:g,onFocusableItemRemove:N,currentTabStopId:T}=v;return r.useEffect(()=>{if(a)return g(),()=>N()},[a,g,N]),(0,m.jsx)(h.ItemSlot,{scope:n,id:p,focusable:a,active:i,children:(0,m.jsx)(s.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>F(n))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=T}):c})})});E.displayName=M;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var x=A,C=E}}]);
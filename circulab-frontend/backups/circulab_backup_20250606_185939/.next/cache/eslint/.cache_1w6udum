[{"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/login/page.tsx": "1", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx": "2", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/analytics/page.tsx": "3", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx": "4", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx": "5", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx": "6", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx": "7", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx": "8", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx": "9", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/treatment-methods/page.tsx": "10", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx": "11", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/[...path]/route.ts": "12", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/login/route.ts": "13", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/profile/route.ts": "14", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/refresh/route.ts": "15", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/health/route.ts": "16", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/route.ts": "17", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/statistics/route.ts": "18", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx": "19", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx": "20", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ErrorBoundary.tsx": "21", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/DashboardContent.tsx": "22", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/DashboardFilters.tsx": "23", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/EmissionsByScope.tsx": "24", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/ProcessingChart.tsx": "25", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/WasteGeneratedMap.tsx": "26", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/WasteMetricsCard.tsx": "27", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/layout/DashboardHeader.tsx": "28", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/layout/DashboardSidebar.tsx": "29", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/providers/AuthProvider.tsx": "30", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/avatar.tsx": "31", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/badge.tsx": "32", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/button.tsx": "33", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/card.tsx": "34", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/checkbox.tsx": "35", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/dropdown-menu.tsx": "36", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/error-boundary.tsx": "37", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/export-button.tsx": "38", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/input.tsx": "39", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/label.tsx": "40", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/loading.tsx": "41", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/select.tsx": "42", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/sheet.tsx": "43", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/skeleton.tsx": "44", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/tabs.tsx": "45", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/toast.tsx": "46", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/hooks/use-toast.ts": "47", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/companies.ts": "48", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/index.ts": "49", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/notifications.ts": "50", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/processingHistory.ts": "51", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/users.ts": "52", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api.ts": "53", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/apiClient.ts": "54", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/exportUtils.ts": "55", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/mockApi.ts": "56", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/utils.ts": "57", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/store/authStore.ts": "58", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/store/wasteMaterialsStore.ts": "59", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/types/index.ts": "60", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/providers/ClientProviders.tsx": "61", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx": "62", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/HealthDashboard.tsx": "63", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/healthCheck.ts": "64", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx": "65", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/MetricsDashboard.tsx": "66", "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout 2.tsx": "67"}, {"size": 7774, "mtime": 1748181259397, "results": "68", "hashOfConfig": "69"}, {"size": 8269, "mtime": 1748165845795, "results": "70", "hashOfConfig": "69"}, {"size": 1784, "mtime": 1748205376067, "results": "71", "hashOfConfig": "69"}, {"size": 8361, "mtime": 1749154472138, "results": "72", "hashOfConfig": "69"}, {"size": 1306, "mtime": 1748164460372, "results": "73", "hashOfConfig": "69"}, {"size": 1652, "mtime": 1748205333945, "results": "74", "hashOfConfig": "69"}, {"size": 11488, "mtime": 1749147000641, "results": "75", "hashOfConfig": "69"}, {"size": 18489, "mtime": 1749152621479, "results": "76", "hashOfConfig": "69"}, {"size": 12765, "mtime": 1749154505109, "results": "77", "hashOfConfig": "69"}, {"size": 2213, "mtime": 1748205420623, "results": "78", "hashOfConfig": "69"}, {"size": 10620, "mtime": 1749146965079, "results": "79", "hashOfConfig": "69"}, {"size": 3665, "mtime": 1749152063459, "results": "80", "hashOfConfig": "69"}, {"size": 935, "mtime": 1748204575751, "results": "81", "hashOfConfig": "69"}, {"size": 1097, "mtime": 1748204708266, "results": "82", "hashOfConfig": "69"}, {"size": 952, "mtime": 1748204682093, "results": "83", "hashOfConfig": "69"}, {"size": 866, "mtime": 1748204548215, "results": "84", "hashOfConfig": "69"}, {"size": 1807, "mtime": 1748204602107, "results": "85", "hashOfConfig": "69"}, {"size": 1330, "mtime": 1748204614467, "results": "86", "hashOfConfig": "69"}, {"size": 6386, "mtime": 1749152962074, "results": "87", "hashOfConfig": "69"}, {"size": 1264, "mtime": 1749188370200, "results": "88", "hashOfConfig": "69"}, {"size": 5080, "mtime": 1749185579896, "results": "89", "hashOfConfig": "69"}, {"size": 9098, "mtime": 1749186505920, "results": "90", "hashOfConfig": "69"}, {"size": 4832, "mtime": 1749151634886, "results": "91", "hashOfConfig": "69"}, {"size": 2558, "mtime": 1749151665776, "results": "92", "hashOfConfig": "69"}, {"size": 4807, "mtime": 1749151717585, "results": "93", "hashOfConfig": "69"}, {"size": 8415, "mtime": 1749151696779, "results": "94", "hashOfConfig": "69"}, {"size": 3891, "mtime": 1749153059756, "results": "95", "hashOfConfig": "69"}, {"size": 3709, "mtime": 1748164175814, "results": "96", "hashOfConfig": "69"}, {"size": 9092, "mtime": 1749147090644, "results": "97", "hashOfConfig": "69"}, {"size": 432, "mtime": 1749149855155, "results": "98", "hashOfConfig": "69"}, {"size": 1917, "mtime": 1748179583355, "results": "99", "hashOfConfig": "69"}, {"size": 1413, "mtime": 1748163951889, "results": "100", "hashOfConfig": "69"}, {"size": 2123, "mtime": 1748162672442, "results": "101", "hashOfConfig": "69"}, {"size": 1877, "mtime": 1748162709143, "results": "102", "hashOfConfig": "69"}, {"size": 1070, "mtime": 1749151832198, "results": "103", "hashOfConfig": "69"}, {"size": 7309, "mtime": 1748179620729, "results": "104", "hashOfConfig": "69"}, {"size": 2961, "mtime": 1748164483919, "results": "105", "hashOfConfig": "69"}, {"size": 7156, "mtime": 1749154427636, "results": "106", "hashOfConfig": "69"}, {"size": 824, "mtime": 1748162719248, "results": "107", "hashOfConfig": "69"}, {"size": 710, "mtime": 1748162727658, "results": "108", "hashOfConfig": "69"}, {"size": 2679, "mtime": 1748163938730, "results": "109", "hashOfConfig": "69"}, {"size": 5629, "mtime": 1749147122647, "results": "110", "hashOfConfig": "69"}, {"size": 4355, "mtime": 1749153632677, "results": "111", "hashOfConfig": "69"}, {"size": 261, "mtime": 1748163919664, "results": "112", "hashOfConfig": "69"}, {"size": 1897, "mtime": 1749151843827, "results": "113", "hashOfConfig": "69"}, {"size": 5194, "mtime": 1748164518741, "results": "114", "hashOfConfig": "69"}, {"size": 2307, "mtime": 1749147239290, "results": "115", "hashOfConfig": "69"}, {"size": 2521, "mtime": 1749152397953, "results": "116", "hashOfConfig": "69"}, {"size": 240, "mtime": 1749147838353, "results": "117", "hashOfConfig": "69"}, {"size": 5092, "mtime": 1749152504326, "results": "118", "hashOfConfig": "69"}, {"size": 5038, "mtime": 1749152733654, "results": "119", "hashOfConfig": "69"}, {"size": 2992, "mtime": 1749152836905, "results": "120", "hashOfConfig": "69"}, {"size": 5502, "mtime": 1749148924030, "results": "121", "hashOfConfig": "69"}, {"size": 4606, "mtime": 1749153693328, "results": "122", "hashOfConfig": "69"}, {"size": 7357, "mtime": 1749154383092, "results": "123", "hashOfConfig": "69"}, {"size": 5880, "mtime": 1748195693778, "results": "124", "hashOfConfig": "69"}, {"size": 166, "mtime": 1748162435284, "results": "125", "hashOfConfig": "69"}, {"size": 8421, "mtime": 1749149968288, "results": "126", "hashOfConfig": "69"}, {"size": 6600, "mtime": 1749148036263, "results": "127", "hashOfConfig": "69"}, {"size": 5977, "mtime": 1748184584421, "results": "128", "hashOfConfig": "69"}, {"size": 763, "mtime": 1749185874127, "results": "129", "hashOfConfig": "69"}, {"size": 5825, "mtime": 1749187671793, "results": "130", "hashOfConfig": "69"}, {"size": 10622, "mtime": 1749186204207, "results": "131", "hashOfConfig": "69"}, {"size": 12429, "mtime": 1749186425849, "results": "132", "hashOfConfig": "69"}, {"size": 6407, "mtime": 1749186580907, "results": "133", "hashOfConfig": "69"}, {"size": 6602, "mtime": 1749187995383, "results": "134", "hashOfConfig": "69"}, {"size": 837, "mtime": 1749150793000, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "nl2rbs", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/login/page.tsx", ["337", "338"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx", ["339"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/analytics/page.tsx", ["340"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx", ["341", "342", "343", "344", "345"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx", ["346", "347", "348", "349"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx", ["350", "351", "352", "353", "354", "355", "356", "357", "358", "359"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/treatment-methods/page.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx", ["360", "361", "362"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/[...path]/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/login/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/profile/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/refresh/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/health/route.ts", ["363"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/statistics/route.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx", ["364", "365", "366"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/DashboardContent.tsx", ["367", "368"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/DashboardFilters.tsx", ["369", "370", "371", "372"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/EmissionsByScope.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/ProcessingChart.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/WasteGeneratedMap.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/dashboard/WasteMetricsCard.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/layout/DashboardHeader.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/layout/DashboardSidebar.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/providers/AuthProvider.tsx", ["373"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/error-boundary.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/export-button.tsx", ["374", "375", "376", "377", "378"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/input.tsx", ["379"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/loading.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/select.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ui/toast.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/hooks/use-toast.ts", ["380", "381"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/companies.ts", ["382"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/index.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/notifications.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/processingHistory.ts", ["383", "384"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api/users.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/api.ts", ["385", "386", "387", "388", "389", "390", "391", "392", "393", "394", "395", "396", "397", "398"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/apiClient.ts", ["399", "400", "401", "402", "403", "404", "405", "406", "407", "408", "409", "410", "411", "412", "413", "414", "415", "416"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/exportUtils.ts", ["417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/mockApi.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/store/authStore.ts", ["432", "433", "434", "435", "436"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/store/wasteMaterialsStore.ts", ["437", "438", "439", "440", "441", "442", "443"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/types/index.ts", ["444", "445"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/providers/ClientProviders.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx", [], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/HealthDashboard.tsx", ["446"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/healthCheck.ts", ["447", "448", "449", "450", "451"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx", ["452", "453", "454"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/MetricsDashboard.tsx", ["455"], [], "/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout 2.tsx", [], [], {"ruleId": "456", "severity": 1, "message": "457", "line": 41, "column": 14, "nodeType": null, "messageId": "458", "endLine": 41, "endColumn": 19}, {"ruleId": "459", "severity": 1, "message": "460", "line": 153, "column": 22, "nodeType": "461", "messageId": "462", "suggestions": "463"}, {"ruleId": "456", "severity": 1, "message": "457", "line": 50, "column": 14, "nodeType": null, "messageId": "458", "endLine": 50, "endColumn": 19}, {"ruleId": "459", "severity": 1, "message": "460", "line": 27, "column": 83, "nodeType": "461", "messageId": "462", "suggestions": "464"}, {"ruleId": "456", "severity": 1, "message": "465", "line": 4, "column": 35, "nodeType": null, "messageId": "458", "endLine": 4, "endColumn": 40}, {"ruleId": "456", "severity": 1, "message": "466", "line": 4, "column": 42, "nodeType": null, "messageId": "458", "endLine": 4, "endColumn": 49}, {"ruleId": "456", "severity": 1, "message": "467", "line": 9, "column": 29, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 46}, {"ruleId": "456", "severity": 1, "message": "468", "line": 19, "column": 10, "nodeType": null, "messageId": "458", "endLine": 19, "endColumn": 15}, {"ruleId": "469", "severity": 1, "message": "470", "line": 53, "column": 6, "nodeType": "471", "endLine": 53, "endColumn": 18, "suggestions": "472"}, {"ruleId": "456", "severity": 1, "message": "473", "line": 4, "column": 46, "nodeType": null, "messageId": "458", "endLine": 4, "endColumn": 52}, {"ruleId": "456", "severity": 1, "message": "474", "line": 9, "column": 33, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 58}, {"ruleId": "456", "severity": 1, "message": "468", "line": 19, "column": 10, "nodeType": null, "messageId": "458", "endLine": 19, "endColumn": 15}, {"ruleId": "469", "severity": 1, "message": "475", "line": 58, "column": 6, "nodeType": "471", "endLine": 58, "endColumn": 32, "suggestions": "476"}, {"ruleId": "456", "severity": 1, "message": "477", "line": 4, "column": 16, "nodeType": null, "messageId": "458", "endLine": 4, "endColumn": 22}, {"ruleId": "456", "severity": 1, "message": "478", "line": 9, "column": 10, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 16}, {"ruleId": "456", "severity": 1, "message": "479", "line": 9, "column": 18, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 31}, {"ruleId": "456", "severity": 1, "message": "480", "line": 9, "column": 33, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 43}, {"ruleId": "456", "severity": 1, "message": "481", "line": 9, "column": 45, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 58}, {"ruleId": "456", "severity": 1, "message": "482", "line": 9, "column": 60, "nodeType": null, "messageId": "458", "endLine": 9, "endColumn": 71}, {"ruleId": "456", "severity": 1, "message": "483", "line": 10, "column": 37, "nodeType": null, "messageId": "458", "endLine": 10, "endColumn": 62}, {"ruleId": "456", "severity": 1, "message": "468", "line": 23, "column": 10, "nodeType": null, "messageId": "458", "endLine": 23, "endColumn": 15}, {"ruleId": "469", "severity": 1, "message": "484", "line": 60, "column": 6, "nodeType": "471", "endLine": 60, "endColumn": 76, "suggestions": "485"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 254, "column": 96, "nodeType": "488", "messageId": "489", "endLine": 254, "endColumn": 99, "suggestions": "490"}, {"ruleId": "456", "severity": 1, "message": "491", "line": 10, "column": 25, "nodeType": null, "messageId": "458", "endLine": 10, "endColumn": 38}, {"ruleId": "456", "severity": 1, "message": "468", "line": 21, "column": 10, "nodeType": null, "messageId": "458", "endLine": 21, "endColumn": 15}, {"ruleId": "469", "severity": 1, "message": "492", "line": 59, "column": 6, "nodeType": "471", "endLine": 59, "endColumn": 50, "suggestions": "493"}, {"ruleId": "456", "severity": 1, "message": "494", "line": 5, "column": 27, "nodeType": null, "messageId": "458", "endLine": 5, "endColumn": 34}, {"ruleId": "486", "severity": 1, "message": "487", "line": 8, "column": 42, "nodeType": "488", "messageId": "489", "endLine": 8, "endColumn": 45, "suggestions": "495"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 9, "column": 52, "nodeType": "488", "messageId": "489", "endLine": 9, "endColumn": 55, "suggestions": "496"}, {"ruleId": "469", "severity": 1, "message": "497", "line": 37, "column": 6, "nodeType": "471", "endLine": 37, "endColumn": 8, "suggestions": "498"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 39, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 39, "endColumn": 24, "suggestions": "499"}, {"ruleId": "459", "severity": 1, "message": "460", "line": 90, "column": 18, "nodeType": "461", "messageId": "462", "suggestions": "500"}, {"ruleId": "456", "severity": 1, "message": "501", "line": 6, "column": 10, "nodeType": null, "messageId": "458", "endLine": 6, "endColumn": 14}, {"ruleId": "456", "severity": 1, "message": "502", "line": 6, "column": 16, "nodeType": null, "messageId": "458", "endLine": 6, "endColumn": 27}, {"ruleId": "456", "severity": 1, "message": "503", "line": 6, "column": 29, "nodeType": null, "messageId": "458", "endLine": 6, "endColumn": 39}, {"ruleId": "456", "severity": 1, "message": "504", "line": 6, "column": 41, "nodeType": null, "messageId": "458", "endLine": 6, "endColumn": 50}, {"ruleId": "456", "severity": 1, "message": "505", "line": 7, "column": 23, "nodeType": null, "messageId": "458", "endLine": 7, "endColumn": 32}, {"ruleId": "486", "severity": 1, "message": "487", "line": 18, "column": 9, "nodeType": "488", "messageId": "489", "endLine": 18, "endColumn": 12, "suggestions": "506"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 141, "column": 63, "nodeType": "488", "messageId": "489", "endLine": 141, "endColumn": 66, "suggestions": "507"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 169, "column": 63, "nodeType": "488", "messageId": "489", "endLine": 169, "endColumn": 66, "suggestions": "508"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 197, "column": 61, "nodeType": "488", "messageId": "489", "endLine": 197, "endColumn": 64, "suggestions": "509"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 237, "column": 59, "nodeType": "488", "messageId": "489", "endLine": 237, "endColumn": 62, "suggestions": "510"}, {"ruleId": "511", "severity": 1, "message": "512", "line": 5, "column": 18, "nodeType": "513", "messageId": "514", "endLine": 5, "endColumn": 28, "suggestions": "515"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 23, "column": 53, "nodeType": "488", "messageId": "489", "endLine": 23, "endColumn": 56, "suggestions": "516"}, {"ruleId": "456", "severity": 1, "message": "517", "line": 94, "column": 9, "nodeType": null, "messageId": "458", "endLine": 94, "endColumn": 20}, {"ruleId": "511", "severity": 1, "message": "512", "line": 21, "column": 18, "nodeType": "513", "messageId": "514", "endLine": 21, "endColumn": 35, "suggestions": "518"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 27, "column": 48, "nodeType": "488", "messageId": "489", "endLine": 27, "endColumn": 51, "suggestions": "519"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 38, "column": 48, "nodeType": "488", "messageId": "489", "endLine": 38, "endColumn": 51, "suggestions": "520"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 73, "column": 34, "nodeType": "488", "messageId": "489", "endLine": 73, "endColumn": 37, "suggestions": "521"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 77, "column": 11, "nodeType": "488", "messageId": "489", "endLine": 77, "endColumn": 14, "suggestions": "522"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 90, "column": 42, "nodeType": "488", "messageId": "489", "endLine": 90, "endColumn": 45, "suggestions": "523"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 90, "column": 58, "nodeType": "488", "messageId": "489", "endLine": 90, "endColumn": 61, "suggestions": "524"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 101, "column": 19, "nodeType": "488", "messageId": "489", "endLine": 101, "endColumn": 22, "suggestions": "525"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 109, "column": 25, "nodeType": "488", "messageId": "489", "endLine": 109, "endColumn": 28, "suggestions": "526"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 118, "column": 20, "nodeType": "488", "messageId": "489", "endLine": 118, "endColumn": 23, "suggestions": "527"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 118, "column": 45, "nodeType": "488", "messageId": "489", "endLine": 118, "endColumn": 48, "suggestions": "528"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 126, "column": 25, "nodeType": "488", "messageId": "489", "endLine": 126, "endColumn": 28, "suggestions": "529"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 135, "column": 19, "nodeType": "488", "messageId": "489", "endLine": 135, "endColumn": 22, "suggestions": "530"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 135, "column": 44, "nodeType": "488", "messageId": "489", "endLine": 135, "endColumn": 47, "suggestions": "531"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 148, "column": 22, "nodeType": "488", "messageId": "489", "endLine": 148, "endColumn": 25, "suggestions": "532"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 160, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 160, "endColumn": 24, "suggestions": "533"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 160, "column": 46, "nodeType": "488", "messageId": "489", "endLine": 160, "endColumn": 49, "suggestions": "534"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 3, "column": 34, "nodeType": "488", "messageId": "489", "endLine": 3, "endColumn": 37, "suggestions": "535"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 48, "column": 49, "nodeType": "488", "messageId": "489", "endLine": 48, "endColumn": 52, "suggestions": "536"}, {"ruleId": "456", "severity": 1, "message": "537", "line": 71, "column": 24, "nodeType": null, "messageId": "458", "endLine": 71, "endColumn": 36}, {"ruleId": "486", "severity": 1, "message": "487", "line": 88, "column": 17, "nodeType": "488", "messageId": "489", "endLine": 88, "endColumn": 20, "suggestions": "538"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 88, "column": 44, "nodeType": "488", "messageId": "489", "endLine": 88, "endColumn": 47, "suggestions": "539"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 97, "column": 18, "nodeType": "488", "messageId": "489", "endLine": 97, "endColumn": 21, "suggestions": "540"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 97, "column": 43, "nodeType": "488", "messageId": "489", "endLine": 97, "endColumn": 46, "suggestions": "541"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 97, "column": 57, "nodeType": "488", "messageId": "489", "endLine": 97, "endColumn": 60, "suggestions": "542"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 106, "column": 17, "nodeType": "488", "messageId": "489", "endLine": 106, "endColumn": 20, "suggestions": "543"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 106, "column": 42, "nodeType": "488", "messageId": "489", "endLine": 106, "endColumn": 45, "suggestions": "544"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 106, "column": 56, "nodeType": "488", "messageId": "489", "endLine": 106, "endColumn": 59, "suggestions": "545"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 115, "column": 19, "nodeType": "488", "messageId": "489", "endLine": 115, "endColumn": 22, "suggestions": "546"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 115, "column": 44, "nodeType": "488", "messageId": "489", "endLine": 115, "endColumn": 47, "suggestions": "547"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 115, "column": 58, "nodeType": "488", "messageId": "489", "endLine": 115, "endColumn": 61, "suggestions": "548"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 124, "column": 20, "nodeType": "488", "messageId": "489", "endLine": 124, "endColumn": 23, "suggestions": "549"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 124, "column": 47, "nodeType": "488", "messageId": "489", "endLine": 124, "endColumn": 50, "suggestions": "550"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 133, "column": 30, "nodeType": "488", "messageId": "489", "endLine": 133, "endColumn": 33, "suggestions": "551"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 138, "column": 20, "nodeType": "488", "messageId": "489", "endLine": 138, "endColumn": 23, "suggestions": "552"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 6, "column": 23, "nodeType": "488", "messageId": "489", "endLine": 6, "endColumn": 26, "suggestions": "553"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 18, "column": 54, "nodeType": "488", "messageId": "489", "endLine": 18, "endColumn": 57, "suggestions": "554"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 54, "column": 56, "nodeType": "488", "messageId": "489", "endLine": 54, "endColumn": 59, "suggestions": "555"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 70, "column": 31, "nodeType": "488", "messageId": "489", "endLine": 70, "endColumn": 34, "suggestions": "556"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 102, "column": 11, "nodeType": "488", "messageId": "489", "endLine": 102, "endColumn": 14, "suggestions": "557"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 117, "column": 33, "nodeType": "488", "messageId": "489", "endLine": 117, "endColumn": 36, "suggestions": "558"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 145, "column": 30, "nodeType": "488", "messageId": "489", "endLine": 145, "endColumn": 33, "suggestions": "559"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 145, "column": 50, "nodeType": "488", "messageId": "489", "endLine": 145, "endColumn": 53, "suggestions": "560"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 152, "column": 33, "nodeType": "488", "messageId": "489", "endLine": 152, "endColumn": 36, "suggestions": "561"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 191, "column": 17, "nodeType": "488", "messageId": "489", "endLine": 191, "endColumn": 20, "suggestions": "562"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 197, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 197, "endColumn": 24, "suggestions": "563"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 203, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 203, "endColumn": 24, "suggestions": "564"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 211, "column": 19, "nodeType": "488", "messageId": "489", "endLine": 211, "endColumn": 22, "suggestions": "565"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 216, "column": 20, "nodeType": "488", "messageId": "489", "endLine": 216, "endColumn": 23, "suggestions": "566"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 220, "column": 18, "nodeType": "488", "messageId": "489", "endLine": 220, "endColumn": 21, "suggestions": "567"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 63, "column": 25, "nodeType": "488", "messageId": "489", "endLine": 63, "endColumn": 28, "suggestions": "568"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 101, "column": 25, "nodeType": "488", "messageId": "489", "endLine": 101, "endColumn": 28, "suggestions": "569"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 183, "column": 25, "nodeType": "488", "messageId": "489", "endLine": 183, "endColumn": 28, "suggestions": "570"}, {"ruleId": "456", "severity": 1, "message": "457", "line": 226, "column": 22, "nodeType": null, "messageId": "458", "endLine": 226, "endColumn": 27}, {"ruleId": "456", "severity": 1, "message": "537", "line": 233, "column": 24, "nodeType": null, "messageId": "458", "endLine": 233, "endColumn": 36}, {"ruleId": "456", "severity": 1, "message": "571", "line": 35, "column": 73, "nodeType": null, "messageId": "458", "endLine": 35, "endColumn": 76}, {"ruleId": "486", "severity": 1, "message": "487", "line": 70, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 70, "endColumn": 24, "suggestions": "572"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 95, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 95, "endColumn": 24, "suggestions": "573"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 123, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 123, "endColumn": 24, "suggestions": "574"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 154, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 154, "endColumn": 24, "suggestions": "575"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 181, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 181, "endColumn": 24, "suggestions": "576"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 206, "column": 21, "nodeType": "488", "messageId": "489", "endLine": 206, "endColumn": 24, "suggestions": "577"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 97, "column": 49, "nodeType": "488", "messageId": "489", "endLine": 97, "endColumn": 52, "suggestions": "578"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 187, "column": 48, "nodeType": "488", "messageId": "489", "endLine": 187, "endColumn": 51, "suggestions": "579"}, {"ruleId": "469", "severity": 1, "message": "580", "line": 53, "column": 6, "nodeType": "471", "endLine": 53, "endColumn": 8, "suggestions": "581"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 7, "column": 13, "nodeType": "488", "messageId": "489", "endLine": 7, "endColumn": 16, "suggestions": "582"}, {"ruleId": "456", "severity": 1, "message": "583", "line": 89, "column": 35, "nodeType": null, "messageId": "458", "endLine": 89, "endColumn": 45}, {"ruleId": "456", "severity": 1, "message": "584", "line": 176, "column": 13, "nodeType": null, "messageId": "458", "endLine": 176, "endColumn": 39}, {"ruleId": "486", "severity": 1, "message": "487", "line": 233, "column": 83, "nodeType": "488", "messageId": "489", "endLine": 233, "endColumn": 86, "suggestions": "585"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 234, "column": 85, "nodeType": "488", "messageId": "489", "endLine": 234, "endColumn": 88, "suggestions": "586"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 11, "column": 28, "nodeType": "488", "messageId": "489", "endLine": 11, "endColumn": 31, "suggestions": "587"}, {"ruleId": "486", "severity": 1, "message": "487", "line": 132, "column": 38, "nodeType": "488", "messageId": "489", "endLine": 132, "endColumn": 41, "suggestions": "588"}, {"ruleId": "456", "severity": 1, "message": "589", "line": 190, "column": 21, "nodeType": null, "messageId": "458", "endLine": 190, "endColumn": 26}, {"ruleId": "456", "severity": 1, "message": "590", "line": 6, "column": 10, "nodeType": null, "messageId": "458", "endLine": 6, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["591", "592", "593", "594"], ["595", "596", "597", "598"], "'Users' is defined but never used.", "'Package' is defined but never used.", "'CompaniesResponse' is defined but never used.", "'total' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", "ArrayExpression", ["599"], "'Filter' is defined but never used.", "'UserNotificationsResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["600"], "'Search' is defined but never used.", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'ProcessingHistoryResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchProcessingHistory'. Either include it or remove the dependency array.", ["601"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["602", "603"], "'UsersResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["604"], "'request' is defined but never used.", ["605", "606"], ["607", "608"], "React Hook useEffect has a missing dependency: 'localStorage'. Either include it or remove the dependency array.", ["609"], ["610", "611"], ["612", "613", "614", "615"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'isLoading' is assigned a value but never used.", ["616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["626"], ["627", "628"], "'unsubscribe' is assigned a value but never used.", ["629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], "'refreshError' is defined but never used.", ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], ["678", "679"], ["680", "681"], ["682", "683"], ["684", "685"], ["686", "687"], ["688", "689"], ["690", "691"], ["692", "693"], ["694", "695"], ["696", "697"], ["698", "699"], ["700", "701"], ["702", "703"], ["704", "705"], ["706", "707"], ["708", "709"], ["710", "711"], ["712", "713"], ["714", "715"], ["716", "717"], ["718", "719"], ["720", "721"], ["722", "723"], ["724", "725"], ["726", "727"], ["728", "729"], ["730", "731"], "'get' is defined but never used.", ["732", "733"], ["734", "735"], ["736", "737"], ["738", "739"], ["740", "741"], ["742", "743"], ["744", "745"], ["746", "747"], "React Hook useEffect has a missing dependency: 'performHealthCheck'. Either include it or remove the dependency array.", ["748"], ["749", "750"], "'modulePath' is defined but never used.", "'knownProblematicExtensions' is assigned a value but never used.", ["751", "752"], ["753", "754"], ["755", "756"], ["757", "758"], "'props' is defined but never used.", "'Button' is defined but never used.", {"messageId": "759", "data": "760", "fix": "761", "desc": "762"}, {"messageId": "759", "data": "763", "fix": "764", "desc": "765"}, {"messageId": "759", "data": "766", "fix": "767", "desc": "768"}, {"messageId": "759", "data": "769", "fix": "770", "desc": "771"}, {"messageId": "759", "data": "772", "fix": "773", "desc": "762"}, {"messageId": "759", "data": "774", "fix": "775", "desc": "765"}, {"messageId": "759", "data": "776", "fix": "777", "desc": "768"}, {"messageId": "759", "data": "778", "fix": "779", "desc": "771"}, {"desc": "780", "fix": "781"}, {"desc": "782", "fix": "783"}, {"desc": "784", "fix": "785"}, {"messageId": "786", "fix": "787", "desc": "788"}, {"messageId": "789", "fix": "790", "desc": "791"}, {"desc": "792", "fix": "793"}, {"messageId": "786", "fix": "794", "desc": "788"}, {"messageId": "789", "fix": "795", "desc": "791"}, {"messageId": "786", "fix": "796", "desc": "788"}, {"messageId": "789", "fix": "797", "desc": "791"}, {"desc": "798", "fix": "799"}, {"messageId": "786", "fix": "800", "desc": "788"}, {"messageId": "789", "fix": "801", "desc": "791"}, {"messageId": "759", "data": "802", "fix": "803", "desc": "762"}, {"messageId": "759", "data": "804", "fix": "805", "desc": "765"}, {"messageId": "759", "data": "806", "fix": "807", "desc": "768"}, {"messageId": "759", "data": "808", "fix": "809", "desc": "771"}, {"messageId": "786", "fix": "810", "desc": "788"}, {"messageId": "789", "fix": "811", "desc": "791"}, {"messageId": "786", "fix": "812", "desc": "788"}, {"messageId": "789", "fix": "813", "desc": "791"}, {"messageId": "786", "fix": "814", "desc": "788"}, {"messageId": "789", "fix": "815", "desc": "791"}, {"messageId": "786", "fix": "816", "desc": "788"}, {"messageId": "789", "fix": "817", "desc": "791"}, {"messageId": "786", "fix": "818", "desc": "788"}, {"messageId": "789", "fix": "819", "desc": "791"}, {"messageId": "820", "fix": "821", "desc": "822"}, {"messageId": "786", "fix": "823", "desc": "788"}, {"messageId": "789", "fix": "824", "desc": "791"}, {"messageId": "820", "fix": "825", "desc": "822"}, {"messageId": "786", "fix": "826", "desc": "788"}, {"messageId": "789", "fix": "827", "desc": "791"}, {"messageId": "786", "fix": "828", "desc": "788"}, {"messageId": "789", "fix": "829", "desc": "791"}, {"messageId": "786", "fix": "830", "desc": "788"}, {"messageId": "789", "fix": "831", "desc": "791"}, {"messageId": "786", "fix": "832", "desc": "788"}, {"messageId": "789", "fix": "833", "desc": "791"}, {"messageId": "786", "fix": "834", "desc": "788"}, {"messageId": "789", "fix": "835", "desc": "791"}, {"messageId": "786", "fix": "836", "desc": "788"}, {"messageId": "789", "fix": "837", "desc": "791"}, {"messageId": "786", "fix": "838", "desc": "788"}, {"messageId": "789", "fix": "839", "desc": "791"}, {"messageId": "786", "fix": "840", "desc": "788"}, {"messageId": "789", "fix": "841", "desc": "791"}, {"messageId": "786", "fix": "842", "desc": "788"}, {"messageId": "789", "fix": "843", "desc": "791"}, {"messageId": "786", "fix": "844", "desc": "788"}, {"messageId": "789", "fix": "845", "desc": "791"}, {"messageId": "786", "fix": "846", "desc": "788"}, {"messageId": "789", "fix": "847", "desc": "791"}, {"messageId": "786", "fix": "848", "desc": "788"}, {"messageId": "789", "fix": "849", "desc": "791"}, {"messageId": "786", "fix": "850", "desc": "788"}, {"messageId": "789", "fix": "851", "desc": "791"}, {"messageId": "786", "fix": "852", "desc": "788"}, {"messageId": "789", "fix": "853", "desc": "791"}, {"messageId": "786", "fix": "854", "desc": "788"}, {"messageId": "789", "fix": "855", "desc": "791"}, {"messageId": "786", "fix": "856", "desc": "788"}, {"messageId": "789", "fix": "857", "desc": "791"}, {"messageId": "786", "fix": "858", "desc": "788"}, {"messageId": "789", "fix": "859", "desc": "791"}, {"messageId": "786", "fix": "860", "desc": "788"}, {"messageId": "789", "fix": "861", "desc": "791"}, {"messageId": "786", "fix": "862", "desc": "788"}, {"messageId": "789", "fix": "863", "desc": "791"}, {"messageId": "786", "fix": "864", "desc": "788"}, {"messageId": "789", "fix": "865", "desc": "791"}, {"messageId": "786", "fix": "866", "desc": "788"}, {"messageId": "789", "fix": "867", "desc": "791"}, {"messageId": "786", "fix": "868", "desc": "788"}, {"messageId": "789", "fix": "869", "desc": "791"}, {"messageId": "786", "fix": "870", "desc": "788"}, {"messageId": "789", "fix": "871", "desc": "791"}, {"messageId": "786", "fix": "872", "desc": "788"}, {"messageId": "789", "fix": "873", "desc": "791"}, {"messageId": "786", "fix": "874", "desc": "788"}, {"messageId": "789", "fix": "875", "desc": "791"}, {"messageId": "786", "fix": "876", "desc": "788"}, {"messageId": "789", "fix": "877", "desc": "791"}, {"messageId": "786", "fix": "878", "desc": "788"}, {"messageId": "789", "fix": "879", "desc": "791"}, {"messageId": "786", "fix": "880", "desc": "788"}, {"messageId": "789", "fix": "881", "desc": "791"}, {"messageId": "786", "fix": "882", "desc": "788"}, {"messageId": "789", "fix": "883", "desc": "791"}, {"messageId": "786", "fix": "884", "desc": "788"}, {"messageId": "789", "fix": "885", "desc": "791"}, {"messageId": "786", "fix": "886", "desc": "788"}, {"messageId": "789", "fix": "887", "desc": "791"}, {"messageId": "786", "fix": "888", "desc": "788"}, {"messageId": "789", "fix": "889", "desc": "791"}, {"messageId": "786", "fix": "890", "desc": "788"}, {"messageId": "789", "fix": "891", "desc": "791"}, {"messageId": "786", "fix": "892", "desc": "788"}, {"messageId": "789", "fix": "893", "desc": "791"}, {"messageId": "786", "fix": "894", "desc": "788"}, {"messageId": "789", "fix": "895", "desc": "791"}, {"messageId": "786", "fix": "896", "desc": "788"}, {"messageId": "789", "fix": "897", "desc": "791"}, {"messageId": "786", "fix": "898", "desc": "788"}, {"messageId": "789", "fix": "899", "desc": "791"}, {"messageId": "786", "fix": "900", "desc": "788"}, {"messageId": "789", "fix": "901", "desc": "791"}, {"messageId": "786", "fix": "902", "desc": "788"}, {"messageId": "789", "fix": "903", "desc": "791"}, {"messageId": "786", "fix": "904", "desc": "788"}, {"messageId": "789", "fix": "905", "desc": "791"}, {"messageId": "786", "fix": "906", "desc": "788"}, {"messageId": "789", "fix": "907", "desc": "791"}, {"messageId": "786", "fix": "908", "desc": "788"}, {"messageId": "789", "fix": "909", "desc": "791"}, {"messageId": "786", "fix": "910", "desc": "788"}, {"messageId": "789", "fix": "911", "desc": "791"}, {"messageId": "786", "fix": "912", "desc": "788"}, {"messageId": "789", "fix": "913", "desc": "791"}, {"messageId": "786", "fix": "914", "desc": "788"}, {"messageId": "789", "fix": "915", "desc": "791"}, {"messageId": "786", "fix": "916", "desc": "788"}, {"messageId": "789", "fix": "917", "desc": "791"}, {"messageId": "786", "fix": "918", "desc": "788"}, {"messageId": "789", "fix": "919", "desc": "791"}, {"messageId": "786", "fix": "920", "desc": "788"}, {"messageId": "789", "fix": "921", "desc": "791"}, {"messageId": "786", "fix": "922", "desc": "788"}, {"messageId": "789", "fix": "923", "desc": "791"}, {"messageId": "786", "fix": "924", "desc": "788"}, {"messageId": "789", "fix": "925", "desc": "791"}, {"messageId": "786", "fix": "926", "desc": "788"}, {"messageId": "789", "fix": "927", "desc": "791"}, {"messageId": "786", "fix": "928", "desc": "788"}, {"messageId": "789", "fix": "929", "desc": "791"}, {"messageId": "786", "fix": "930", "desc": "788"}, {"messageId": "789", "fix": "931", "desc": "791"}, {"messageId": "786", "fix": "932", "desc": "788"}, {"messageId": "789", "fix": "933", "desc": "791"}, {"messageId": "786", "fix": "934", "desc": "788"}, {"messageId": "789", "fix": "935", "desc": "791"}, {"messageId": "786", "fix": "936", "desc": "788"}, {"messageId": "789", "fix": "937", "desc": "791"}, {"messageId": "786", "fix": "938", "desc": "788"}, {"messageId": "789", "fix": "939", "desc": "791"}, {"messageId": "786", "fix": "940", "desc": "788"}, {"messageId": "789", "fix": "941", "desc": "791"}, {"messageId": "786", "fix": "942", "desc": "788"}, {"messageId": "789", "fix": "943", "desc": "791"}, {"desc": "944", "fix": "945"}, {"messageId": "786", "fix": "946", "desc": "788"}, {"messageId": "789", "fix": "947", "desc": "791"}, {"messageId": "786", "fix": "948", "desc": "788"}, {"messageId": "789", "fix": "949", "desc": "791"}, {"messageId": "786", "fix": "950", "desc": "788"}, {"messageId": "789", "fix": "951", "desc": "791"}, {"messageId": "786", "fix": "952", "desc": "788"}, {"messageId": "789", "fix": "953", "desc": "791"}, {"messageId": "786", "fix": "954", "desc": "788"}, {"messageId": "789", "fix": "955", "desc": "791"}, "replaceWithAlt", {"alt": "956"}, {"range": "957", "text": "958"}, "Replace with `&apos;`.", {"alt": "959"}, {"range": "960", "text": "961"}, "Replace with `&lsquo;`.", {"alt": "962"}, {"range": "963", "text": "964"}, "Replace with `&#39;`.", {"alt": "965"}, {"range": "966", "text": "967"}, "Replace with `&rsquo;`.", {"alt": "956"}, {"range": "968", "text": "969"}, {"alt": "959"}, {"range": "970", "text": "971"}, {"alt": "962"}, {"range": "972", "text": "973"}, {"alt": "965"}, {"range": "974", "text": "975"}, "Update the dependencies array to be: [fetchCompanies, searchTerm]", {"range": "976", "text": "977"}, "Update the dependencies array to be: [typeFilter, statusFilter, fetchNotifications]", {"range": "978", "text": "979"}, "Update the dependencies array to be: [wasteTypeFilter, treatmentMethodFilter, dateFromFilter, dateToFilter, fetchProcessingHistory]", {"range": "980", "text": "981"}, "suggestUnknown", {"range": "982", "text": "983"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "984", "text": "985"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [searchTerm, roleFilter, verificationFilter, fetchUsers]", {"range": "986", "text": "987"}, {"range": "988", "text": "983"}, {"range": "989", "text": "985"}, {"range": "990", "text": "983"}, {"range": "991", "text": "985"}, "Update the dependencies array to be: [localStorage]", {"range": "992", "text": "993"}, {"range": "994", "text": "983"}, {"range": "995", "text": "985"}, {"alt": "956"}, {"range": "996", "text": "997"}, {"alt": "959"}, {"range": "998", "text": "999"}, {"alt": "962"}, {"range": "1000", "text": "1001"}, {"alt": "965"}, {"range": "1002", "text": "1003"}, {"range": "1004", "text": "983"}, {"range": "1005", "text": "985"}, {"range": "1006", "text": "983"}, {"range": "1007", "text": "985"}, {"range": "1008", "text": "983"}, {"range": "1009", "text": "985"}, {"range": "1010", "text": "983"}, {"range": "1011", "text": "985"}, {"range": "1012", "text": "983"}, {"range": "1013", "text": "985"}, "replaceEmptyInterfaceWithSuper", {"range": "1014", "text": "1015"}, "Replace empty interface with a type alias.", {"range": "1016", "text": "983"}, {"range": "1017", "text": "985"}, {"range": "1018", "text": "1019"}, {"range": "1020", "text": "983"}, {"range": "1021", "text": "985"}, {"range": "1022", "text": "983"}, {"range": "1023", "text": "985"}, {"range": "1024", "text": "983"}, {"range": "1025", "text": "985"}, {"range": "1026", "text": "983"}, {"range": "1027", "text": "985"}, {"range": "1028", "text": "983"}, {"range": "1029", "text": "985"}, {"range": "1030", "text": "983"}, {"range": "1031", "text": "985"}, {"range": "1032", "text": "983"}, {"range": "1033", "text": "985"}, {"range": "1034", "text": "983"}, {"range": "1035", "text": "985"}, {"range": "1036", "text": "983"}, {"range": "1037", "text": "985"}, {"range": "1038", "text": "983"}, {"range": "1039", "text": "985"}, {"range": "1040", "text": "983"}, {"range": "1041", "text": "985"}, {"range": "1042", "text": "983"}, {"range": "1043", "text": "985"}, {"range": "1044", "text": "983"}, {"range": "1045", "text": "985"}, {"range": "1046", "text": "983"}, {"range": "1047", "text": "985"}, {"range": "1048", "text": "983"}, {"range": "1049", "text": "985"}, {"range": "1050", "text": "983"}, {"range": "1051", "text": "985"}, {"range": "1052", "text": "983"}, {"range": "1053", "text": "985"}, {"range": "1054", "text": "983"}, {"range": "1055", "text": "985"}, {"range": "1056", "text": "983"}, {"range": "1057", "text": "985"}, {"range": "1058", "text": "983"}, {"range": "1059", "text": "985"}, {"range": "1060", "text": "983"}, {"range": "1061", "text": "985"}, {"range": "1062", "text": "983"}, {"range": "1063", "text": "985"}, {"range": "1064", "text": "983"}, {"range": "1065", "text": "985"}, {"range": "1066", "text": "983"}, {"range": "1067", "text": "985"}, {"range": "1068", "text": "983"}, {"range": "1069", "text": "985"}, {"range": "1070", "text": "983"}, {"range": "1071", "text": "985"}, {"range": "1072", "text": "983"}, {"range": "1073", "text": "985"}, {"range": "1074", "text": "983"}, {"range": "1075", "text": "985"}, {"range": "1076", "text": "983"}, {"range": "1077", "text": "985"}, {"range": "1078", "text": "983"}, {"range": "1079", "text": "985"}, {"range": "1080", "text": "983"}, {"range": "1081", "text": "985"}, {"range": "1082", "text": "983"}, {"range": "1083", "text": "985"}, {"range": "1084", "text": "983"}, {"range": "1085", "text": "985"}, {"range": "1086", "text": "983"}, {"range": "1087", "text": "985"}, {"range": "1088", "text": "983"}, {"range": "1089", "text": "985"}, {"range": "1090", "text": "983"}, {"range": "1091", "text": "985"}, {"range": "1092", "text": "983"}, {"range": "1093", "text": "985"}, {"range": "1094", "text": "983"}, {"range": "1095", "text": "985"}, {"range": "1096", "text": "983"}, {"range": "1097", "text": "985"}, {"range": "1098", "text": "983"}, {"range": "1099", "text": "985"}, {"range": "1100", "text": "983"}, {"range": "1101", "text": "985"}, {"range": "1102", "text": "983"}, {"range": "1103", "text": "985"}, {"range": "1104", "text": "983"}, {"range": "1105", "text": "985"}, {"range": "1106", "text": "983"}, {"range": "1107", "text": "985"}, {"range": "1108", "text": "983"}, {"range": "1109", "text": "985"}, {"range": "1110", "text": "983"}, {"range": "1111", "text": "985"}, {"range": "1112", "text": "983"}, {"range": "1113", "text": "985"}, {"range": "1114", "text": "983"}, {"range": "1115", "text": "985"}, {"range": "1116", "text": "983"}, {"range": "1117", "text": "985"}, {"range": "1118", "text": "983"}, {"range": "1119", "text": "985"}, {"range": "1120", "text": "983"}, {"range": "1121", "text": "985"}, {"range": "1122", "text": "983"}, {"range": "1123", "text": "985"}, {"range": "1124", "text": "983"}, {"range": "1125", "text": "985"}, {"range": "1126", "text": "983"}, {"range": "1127", "text": "985"}, {"range": "1128", "text": "983"}, {"range": "1129", "text": "985"}, {"range": "1130", "text": "983"}, {"range": "1131", "text": "985"}, {"range": "1132", "text": "983"}, {"range": "1133", "text": "985"}, {"range": "1134", "text": "983"}, {"range": "1135", "text": "985"}, {"range": "1136", "text": "983"}, {"range": "1137", "text": "985"}, "Update the dependencies array to be: [performHealthCheck]", {"range": "1138", "text": "1139"}, {"range": "1140", "text": "983"}, {"range": "1141", "text": "985"}, {"range": "1142", "text": "983"}, {"range": "1143", "text": "985"}, {"range": "1144", "text": "983"}, {"range": "1145", "text": "985"}, {"range": "1146", "text": "983"}, {"range": "1147", "text": "985"}, {"range": "1148", "text": "983"}, {"range": "1149", "text": "985"}, "&apos;", [6374, 6415], "\n                  Don&apos;t have an account?", "&lsquo;", [6374, 6415], "\n                  Don&lsquo;t have an account?", "&#39;", [6374, 6415], "\n                  Don&#39;t have an account?", "&rsquo;", [6374, 6415], "\n                  Don&rsquo;t have an account?", [1429, 1574], "\n                      Visualisez les tendances, générez des rapports et analysez l&apos;efficacité de votre gestion des déchets.\n                    ", [1429, 1574], "\n                      Visualisez les tendances, générez des rapports et analysez l&lsquo;efficacité de votre gestion des déchets.\n                    ", [1429, 1574], "\n                      Visualisez les tendances, générez des rapports et analysez l&#39;efficacité de votre gestion des déchets.\n                    ", [1429, 1574], "\n                      Visualisez les tendances, générez des rapports et analysez l&rsquo;efficacité de votre gestion des déchets.\n                    ", [1808, 1820], "[fetchCompanies, searchTerm]", [2212, 2238], "[typeFilter, statusFilter, fetchNotifications]", [2392, 2462], "[wasteTypeFilter, treatmentMethod<PERSON>ilter, dateFromFilter, dateToFilter, fetchProcessingHistory]", [10612, 10615], "unknown", [10612, 10615], "never", [2209, 2253], "[searchTerm, roleFilter, verificationFilter, fetchUsers]", [226, 229], [226, 229], [289, 292], [289, 292], [1142, 1144], "[localStorage]", [1136, 1139], [1136, 1139], [2519, 2600], "\n            Vue d&apos;ensemble de la valorisation des déchets industriels\n          ", [2519, 2600], "\n            Vue d&lsquo;ensemble de la valorisation des déchets industriels\n          ", [2519, 2600], "\n            Vue d&#39;ensemble de la valorisation des déchets industriels\n          ", [2519, 2600], "\n            Vue d&rsquo;ensemble de la valorisation des déchets industriels\n          ", [530, 533], [530, 533], [4000, 4003], [4000, 4003], [4753, 4756], [4753, 4756], [5482, 5485], [5482, 5485], [6555, 6558], [6555, 6558], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [462, 465], [462, 465], [481, 546], "type UpdateCompanyData = Partial<CreateCompanyData>", [811, 814], [811, 814], [1102, 1105], [1102, 1105], [2087, 2090], [2087, 2090], [2156, 2159], [2156, 2159], [2398, 2401], [2398, 2401], [2414, 2417], [2414, 2417], [2852, 2855], [2852, 2855], [3215, 3218], [3215, 3218], [3452, 3455], [3452, 3455], [3477, 3480], [3477, 3480], [3819, 3822], [3819, 3822], [4063, 4066], [4063, 4066], [4088, 4091], [4088, 4091], [4529, 4532], [4529, 4532], [4962, 4965], [4962, 4965], [4987, 4990], [4987, 4990], [107, 110], [107, 110], [1239, 1242], [1239, 1242], [2672, 2675], [2672, 2675], [2699, 2702], [2699, 2702], [2917, 2920], [2917, 2920], [2942, 2945], [2942, 2945], [2956, 2959], [2956, 2959], [3180, 3183], [3180, 3183], [3205, 3208], [3205, 3208], [3219, 3222], [3219, 3222], [3444, 3447], [3444, 3447], [3469, 3472], [3469, 3472], [3483, 3486], [3483, 3486], [3711, 3714], [3711, 3714], [3738, 3741], [3738, 3741], [3971, 3974], [3971, 3974], [4222, 4225], [4222, 4225], [117, 120], [117, 120], [335, 338], [335, 338], [1313, 1316], [1313, 1316], [1666, 1669], [1666, 1669], [2557, 2560], [2557, 2560], [2910, 2913], [2910, 2913], [3691, 3694], [3691, 3694], [3711, 3714], [3711, 3714], [3868, 3871], [3868, 3871], [4742, 4745], [4742, 4745], [4879, 4882], [4879, 4882], [5012, 5015], [5012, 5015], [5238, 5241], [5238, 5241], [5391, 5394], [5391, 5394], [5459, 5462], [5459, 5462], [1875, 1878], [1875, 1878], [3061, 3064], [3061, 3064], [5591, 5594], [5591, 5594], [2175, 2178], [2175, 2178], [2862, 2865], [2862, 2865], [3670, 3673], [3670, 3673], [4673, 4676], [4673, 4676], [5531, 5534], [5531, 5534], [6202, 6205], [6202, 6205], [1946, 1949], [1946, 1949], [4236, 4239], [4236, 4239], [1463, 1465], "[performHealth<PERSON>heck]", [155, 158], [155, 158], [7011, 7014], [7011, 7014], [7123, 7126], [7123, 7126], [284, 287], [284, 287], [3578, 3581], [3578, 3581]]
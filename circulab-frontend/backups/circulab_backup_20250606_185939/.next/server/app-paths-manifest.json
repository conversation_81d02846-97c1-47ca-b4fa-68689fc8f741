{"/_not-found/page": "app/_not-found/page.js", "/api/[...path]/route": "app/api/[...path]/route.js", "/api/auth/refresh/route": "app/api/auth/refresh/route.js", "/api/health/route": "app/api/health/route.js", "/api/waste-materials/route": "app/api/waste-materials/route.js", "/api/waste-materials/statistics/route": "app/api/waste-materials/statistics/route.js", "/api/auth/profile/route": "app/api/auth/profile/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/debug/page": "app/debug/page.js", "/health/page": "app/health/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(dashboard)/notifications/page": "app/(dashboard)/notifications/page.js", "/(dashboard)/companies/page": "app/(dashboard)/companies/page.js", "/(dashboard)/analytics/page": "app/(dashboard)/analytics/page.js", "/(dashboard)/treatment-methods/page": "app/(dashboard)/treatment-methods/page.js", "/(dashboard)/users/page": "app/(dashboard)/users/page.js", "/(dashboard)/processing-history/page": "app/(dashboard)/processing-history/page.js", "/(dashboard)/page": "app/(dashboard)/page.js", "/(dashboard)/materials/page": "app/(dashboard)/materials/page.js"}
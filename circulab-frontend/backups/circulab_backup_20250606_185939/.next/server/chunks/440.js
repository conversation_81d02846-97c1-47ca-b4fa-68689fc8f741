"use strict";exports.id=440,exports.ids=[440],exports.modules={3984:(e,t,r)=>{r.d(t,{H_:()=>e7,UC:()=>e3,YJ:()=>e5,q7:()=>e8,VF:()=>tt,JU:()=>e6,ZL:()=>e4,z6:()=>e9,hN:()=>te,bL:()=>e0,wv:()=>tr,Pb:()=>ta,G5:()=>ts,ZP:()=>tn,l9:()=>e2});var a=r(43210),n=r(31499),s=r(98081),i=r(62076),o=r(66293),l=r(50837),c=r(54e3),f=r(71381),h=r(52464),u=r(25233),d=r(35393),p=r(57441),m=r(34255),g=r(28210),v=r(19697),T=r(12976),b=r(88480),w=r(15521),E=r(47342),S=r(35599),y=r(60687),_=["Enter"," "],A=["ArrowUp","PageDown","End"],x=["ArrowDown","PageUp","Home",...A],k={ltr:[..._,"ArrowRight"],rtl:[..._,"ArrowLeft"]},C={ltr:["ArrowLeft"],rtl:["ArrowRight"]},O="Menu",[R,I,N]=(0,c.N)(O),[D,P]=(0,i.A)(O,[N,m.Bk,T.RG]),M=(0,m.Bk)(),L=(0,T.RG)(),[F,U]=D(O),[B,H]=D(O),W=e=>{let{__scopeMenu:t,open:r=!1,children:n,dir:s,onOpenChange:i,modal:o=!0}=e,l=M(t),[c,h]=a.useState(null),u=a.useRef(!1),d=(0,w.c)(i),p=(0,f.jH)(s);return a.useEffect(()=>{let e=()=>{u.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>u.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,y.jsx)(m.bL,{...l,children:(0,y.jsx)(F,{scope:t,open:r,onOpenChange:d,content:c,onContentChange:h,children:(0,y.jsx)(B,{scope:t,onClose:a.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:u,dir:p,modal:o,children:n})})})};W.displayName=O;var j=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e,n=M(r);return(0,y.jsx)(m.Mz,{...n,...a,ref:t})});j.displayName="MenuAnchor";var G="MenuPortal",[V,z]=D(G,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:r,children:a,container:n}=e,s=U(G,t);return(0,y.jsx)(V,{scope:t,forceMount:r,children:(0,y.jsx)(v.C,{present:r||s.open,children:(0,y.jsx)(g.Z,{asChild:!0,container:n,children:a})})})};K.displayName=G;var Y="MenuContent",[X,J]=D(Y),q=a.forwardRef((e,t)=>{let r=z(Y,e.__scopeMenu),{forceMount:a=r.forceMount,...n}=e,s=U(Y,e.__scopeMenu),i=H(Y,e.__scopeMenu);return(0,y.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(v.C,{present:a||s.open,children:(0,y.jsx)(R.Slot,{scope:e.__scopeMenu,children:i.modal?(0,y.jsx)(Z,{...n,ref:t}):(0,y.jsx)(Q,{...n,ref:t})})})})}),Z=a.forwardRef((e,t)=>{let r=U(Y,e.__scopeMenu),i=a.useRef(null),o=(0,s.s)(t,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,E.Eq)(e)},[]),(0,y.jsx)(et,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=a.forwardRef((e,t)=>{let r=U(Y,e.__scopeMenu);return(0,y.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ee=(0,b.TL)("MenuContent.ScrollLock"),et=a.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:f,onEntryFocus:p,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:w,onDismiss:E,disableOutsideScroll:_,...k}=e,C=U(Y,r),O=H(Y,r),R=M(r),N=L(r),D=I(r),[P,F]=a.useState(null),B=a.useRef(null),W=(0,s.s)(t,B,C.onContentChange),j=a.useRef(0),G=a.useRef(""),V=a.useRef(0),z=a.useRef(null),K=a.useRef("right"),J=a.useRef(0),q=_?S.A:a.Fragment,Z=e=>{let t=G.current+e,r=D().filter(e=>!e.disabled),a=document.activeElement,n=r.find(e=>e.ref.current===a)?.textValue,s=function(e,t,r){var a;let n=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,s=r?e.indexOf(r):-1,i=(a=Math.max(s,0),e.map((t,r)=>e[(a+r)%e.length]));1===n.length&&(i=i.filter(e=>e!==r));let o=i.find(e=>e.toLowerCase().startsWith(n.toLowerCase()));return o!==r?o:void 0}(r.map(e=>e.textValue),t,n),i=r.find(e=>e.textValue===s)?.ref.current;!function e(t){G.current=t,window.clearTimeout(j.current),""!==t&&(j.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};a.useEffect(()=>()=>window.clearTimeout(j.current),[]),(0,u.Oh)();let Q=a.useCallback(e=>K.current===z.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:a}=e,n=!1;for(let e=0,s=t.length-1;e<t.length;s=e++){let i=t[e],o=t[s],l=i.x,c=i.y,f=o.x,h=o.y;c>a!=h>a&&r<(f-l)*(a-c)/(h-c)+l&&(n=!n)}return n}({x:e.clientX,y:e.clientY},t)}(e,z.current?.area),[]);return(0,y.jsx)(X,{scope:r,searchRef:G,onItemEnter:a.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:a.useCallback(e=>{Q(e)||(B.current?.focus(),F(null))},[Q]),onTriggerLeave:a.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:V,onPointerGraceIntentChange:a.useCallback(e=>{z.current=e},[]),children:(0,y.jsx)(q,{..._?{as:ee,allowPinchZoom:!0}:void 0,children:(0,y.jsx)(d.n,{asChild:!0,trapped:o,onMountAutoFocus:(0,n.m)(l,e=>{e.preventDefault(),B.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,y.jsx)(h.qW,{asChild:!0,disableOutsidePointerEvents:f,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:w,onDismiss:E,children:(0,y.jsx)(T.bL,{asChild:!0,...N,dir:O.dir,orientation:"vertical",loop:i,currentTabStopId:P,onCurrentTabStopIdChange:F,onEntryFocus:(0,n.m)(p,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eO(C.open),"data-radix-menu-content":"",dir:O.dir,...R,...k,ref:W,style:{outline:"none",...k.style},onKeyDown:(0,n.m)(k.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,a=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&a&&Z(e.key));let n=B.current;if(e.target!==n||!x.includes(e.key))return;e.preventDefault();let s=D().filter(e=>!e.disabled).map(e=>e.ref.current);A.includes(e.key)&&s.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(s)}),onBlur:(0,n.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(j.current),G.current="")}),onPointerMove:(0,n.m)(e.onPointerMove,eN(e=>{let t=e.target,r=J.current!==e.clientX;e.currentTarget.contains(t)&&r&&(K.current=e.clientX>J.current?"right":"left",J.current=e.clientX)}))})})})})})})});q.displayName=Y;var er=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,y.jsx)(l.sG.div,{role:"group",...a,ref:t})});er.displayName="MenuGroup";var ea=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,y.jsx)(l.sG.div,{...a,ref:t})});ea.displayName="MenuLabel";var en="MenuItem",es="menu.itemSelect",ei=a.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...o}=e,c=a.useRef(null),f=H(en,e.__scopeMenu),h=J(en,e.__scopeMenu),u=(0,s.s)(t,c),d=a.useRef(!1);return(0,y.jsx)(eo,{...o,ref:u,disabled:r,onClick:(0,n.m)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(es,{bubbles:!0,cancelable:!0});e.addEventListener(es,e=>i?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?d.current=!1:f.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),d.current=!0},onPointerUp:(0,n.m)(e.onPointerUp,e=>{d.current||e.currentTarget?.click()}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{let t=""!==h.searchRef.current;r||t&&" "===e.key||_.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=en;var eo=a.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:o,...c}=e,f=J(en,r),h=L(r),u=a.useRef(null),d=(0,s.s)(t,u),[p,m]=a.useState(!1),[g,v]=a.useState("");return a.useEffect(()=>{let e=u.current;e&&v((e.textContent??"").trim())},[c.children]),(0,y.jsx)(R.ItemSlot,{scope:r,disabled:i,textValue:o??g,children:(0,y.jsx)(T.q7,{asChild:!0,...h,focusable:!i,children:(0,y.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...c,ref:d,onPointerMove:(0,n.m)(e.onPointerMove,eN(e=>{i?f.onItemLeave(e):(f.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,n.m)(e.onPointerLeave,eN(e=>f.onItemLeave(e))),onFocus:(0,n.m)(e.onFocus,()=>m(!0)),onBlur:(0,n.m)(e.onBlur,()=>m(!1))})})})}),el=a.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:a,...s}=e;return(0,y.jsx)(eg,{scope:e.__scopeMenu,checked:r,children:(0,y.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eR(r)?"mixed":r,...s,ref:t,"data-state":eI(r),onSelect:(0,n.m)(s.onSelect,()=>a?.(!!eR(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ec="MenuRadioGroup",[ef,eh]=D(ec,{value:void 0,onValueChange:()=>{}}),eu=a.forwardRef((e,t)=>{let{value:r,onValueChange:a,...n}=e,s=(0,w.c)(a);return(0,y.jsx)(ef,{scope:e.__scopeMenu,value:r,onValueChange:s,children:(0,y.jsx)(er,{...n,ref:t})})});eu.displayName=ec;var ed="MenuRadioItem",ep=a.forwardRef((e,t)=>{let{value:r,...a}=e,s=eh(ed,e.__scopeMenu),i=r===s.value;return(0,y.jsx)(eg,{scope:e.__scopeMenu,checked:i,children:(0,y.jsx)(ei,{role:"menuitemradio","aria-checked":i,...a,ref:t,"data-state":eI(i),onSelect:(0,n.m)(a.onSelect,()=>s.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var em="MenuItemIndicator",[eg,ev]=D(em,{checked:!1}),eT=a.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:a,...n}=e,s=ev(em,r);return(0,y.jsx)(v.C,{present:a||eR(s.checked)||!0===s.checked,children:(0,y.jsx)(l.sG.span,{...n,ref:t,"data-state":eI(s.checked)})})});eT.displayName=em;var eb=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e;return(0,y.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...a,ref:t})});eb.displayName="MenuSeparator";var ew=a.forwardRef((e,t)=>{let{__scopeMenu:r,...a}=e,n=M(r);return(0,y.jsx)(m.i3,{...n,...a,ref:t})});ew.displayName="MenuArrow";var eE="MenuSub",[eS,ey]=D(eE),e_=e=>{let{__scopeMenu:t,children:r,open:n=!1,onOpenChange:s}=e,i=U(eE,t),o=M(t),[l,c]=a.useState(null),[f,h]=a.useState(null),u=(0,w.c)(s);return a.useEffect(()=>(!1===i.open&&u(!1),()=>u(!1)),[i.open,u]),(0,y.jsx)(m.bL,{...o,children:(0,y.jsx)(F,{scope:t,open:n,onOpenChange:u,content:f,onContentChange:h,children:(0,y.jsx)(eS,{scope:t,contentId:(0,p.B)(),triggerId:(0,p.B)(),trigger:l,onTriggerChange:c,children:r})})})};e_.displayName=eE;var eA="MenuSubTrigger",ex=a.forwardRef((e,t)=>{let r=U(eA,e.__scopeMenu),i=H(eA,e.__scopeMenu),o=ey(eA,e.__scopeMenu),l=J(eA,e.__scopeMenu),c=a.useRef(null),{pointerGraceTimerRef:f,onPointerGraceIntentChange:h}=l,u={__scopeMenu:e.__scopeMenu},d=a.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return a.useEffect(()=>d,[d]),a.useEffect(()=>{let e=f.current;return()=>{window.clearTimeout(e),h(null)}},[f,h]),(0,y.jsx)(j,{asChild:!0,...u,children:(0,y.jsx)(eo,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":o.contentId,"data-state":eO(r.open),...e,ref:(0,s.t)(t,o.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,n.m)(e.onPointerMove,eN(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||c.current||(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),d()},100)))})),onPointerLeave:(0,n.m)(e.onPointerLeave,eN(e=>{d();let t=r.content?.getBoundingClientRect();if(t){let a=r.content?.dataset.side,n="right"===a,s=t[n?"left":"right"],i=t[n?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(n?-5:5),y:e.clientY},{x:s,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:s,y:t.bottom}],side:a}),window.clearTimeout(f.current),f.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,n.m)(e.onKeyDown,t=>{let a=""!==l.searchRef.current;e.disabled||a&&" "===t.key||k[i.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});ex.displayName=eA;var ek="MenuSubContent",eC=a.forwardRef((e,t)=>{let r=z(Y,e.__scopeMenu),{forceMount:i=r.forceMount,...o}=e,l=U(Y,e.__scopeMenu),c=H(Y,e.__scopeMenu),f=ey(ek,e.__scopeMenu),h=a.useRef(null),u=(0,s.s)(t,h);return(0,y.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(v.C,{present:i||l.open,children:(0,y.jsx)(R.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(et,{id:f.contentId,"aria-labelledby":f.triggerId,...o,ref:u,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&h.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>{e.target!==f.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,n.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=C[c.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),f.trigger?.focus(),e.preventDefault())})})})})})});function eO(e){return e?"open":"closed"}function eR(e){return"indeterminate"===e}function eI(e){return eR(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return t=>"mouse"===t.pointerType?e(t):void 0}eC.displayName=ek;var eD="DropdownMenu",[eP,eM]=(0,i.A)(eD,[P]),eL=P(),[eF,eU]=eP(eD),eB=e=>{let{__scopeDropdownMenu:t,children:r,dir:n,open:s,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,f=eL(t),h=a.useRef(null),[u,d]=(0,o.i)({prop:s,defaultProp:i??!1,onChange:l,caller:eD});return(0,y.jsx)(eF,{scope:t,triggerId:(0,p.B)(),triggerRef:h,contentId:(0,p.B)(),open:u,onOpenChange:d,onOpenToggle:a.useCallback(()=>d(e=>!e),[d]),modal:c,children:(0,y.jsx)(W,{...f,open:u,onOpenChange:d,dir:n,modal:c,children:r})})};eB.displayName=eD;var eH="DropdownMenuTrigger",eW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:a=!1,...i}=e,o=eU(eH,r),c=eL(r);return(0,y.jsx)(j,{asChild:!0,...c,children:(0,y.jsx)(l.sG.button,{type:"button",id:o.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":o.open?o.contentId:void 0,"data-state":o.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...i,ref:(0,s.t)(t,o.triggerRef),onPointerDown:(0,n.m)(e.onPointerDown,e=>{!a&&0===e.button&&!1===e.ctrlKey&&(o.onOpenToggle(),o.open||e.preventDefault())}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{!a&&(["Enter"," "].includes(e.key)&&o.onOpenToggle(),"ArrowDown"===e.key&&o.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eW.displayName=eH;var ej=e=>{let{__scopeDropdownMenu:t,...r}=e,a=eL(t);return(0,y.jsx)(K,{...a,...r})};ej.displayName="DropdownMenuPortal";var eG="DropdownMenuContent",eV=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...s}=e,i=eU(eG,r),o=eL(r),l=a.useRef(!1);return(0,y.jsx)(q,{id:i.contentId,"aria-labelledby":i.triggerId,...o,...s,ref:t,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{l.current||i.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,n.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,a=2===t.button||r;(!i.modal||a)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eG;var ez=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(er,{...n,...a,ref:t})});ez.displayName="DropdownMenuGroup";var eK=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(ea,{...n,...a,ref:t})});eK.displayName="DropdownMenuLabel";var e$=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(ei,{...n,...a,ref:t})});e$.displayName="DropdownMenuItem";var eY=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(el,{...n,...a,ref:t})});eY.displayName="DropdownMenuCheckboxItem";var eX=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(eu,{...n,...a,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eJ=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(ep,{...n,...a,ref:t})});eJ.displayName="DropdownMenuRadioItem";var eq=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(eT,{...n,...a,ref:t})});eq.displayName="DropdownMenuItemIndicator";var eZ=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(eb,{...n,...a,ref:t})});eZ.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(ew,{...n,...a,ref:t})}).displayName="DropdownMenuArrow";var eQ=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(ex,{...n,...a,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var e1=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,n=eL(r);return(0,y.jsx)(eC,{...n,...a,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e1.displayName="DropdownMenuSubContent";var e0=eB,e2=eW,e4=ej,e3=eV,e5=ez,e6=eK,e8=e$,e7=eY,e9=eX,te=eJ,tt=eq,tr=eZ,ta=e=>{let{__scopeDropdownMenu:t,children:r,open:a,onOpenChange:n,defaultOpen:s}=e,i=eL(t),[l,c]=(0,o.i)({prop:a,defaultProp:s??!1,onChange:n,caller:"DropdownMenuSub"});return(0,y.jsx)(e_,{...i,open:l,onOpenChange:c,children:r})},tn=eQ,ts=e1},10022:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10345:(e,t,r)=>{let a;r.d(t,{Wp:()=>is,_h:()=>s3});var n,s,i,o,l={};l.version="0.18.5";var c=1200,f=1252,h=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],u={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},d=function(e){-1!=h.indexOf(e)&&(f=u[0]=e)},p=function(e){c=e,d(e)};function m(){p(1200),d(1252)}function g(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function v(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var T=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(255==t&&254==r){for(var a=e.slice(2),n=[],s=0;s<a.length>>1;++s)n[s]=String.fromCharCode(a.charCodeAt(2*s)+(a.charCodeAt(2*s+1)<<8));return n.join("")}return 254==t&&255==r?v(e.slice(2)):65279==t?e.slice(1):e},b=function(e){return String.fromCharCode(e)},w=function(e){return String.fromCharCode(e)},E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function S(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,l=0,c=0;c<e.length;)s=(r=e.charCodeAt(c++))>>2,i=(3&r)<<4|(a=e.charCodeAt(c++))>>4,o=(15&a)<<2|(n=e.charCodeAt(c++))>>6,l=63&n,isNaN(a)?o=l=64:isNaN(n)&&(l=64),t+=E.charAt(s)+E.charAt(i)+E.charAt(o)+E.charAt(l);return t}function y(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)t+=String.fromCharCode((s=E.indexOf(e.charAt(c++)))<<2|(i=E.indexOf(e.charAt(c++)))>>4),a=(15&i)<<4|(o=E.indexOf(e.charAt(c++)))>>2,64!==o&&(t+=String.fromCharCode(a)),n=(3&o)<<6|(l=E.indexOf(e.charAt(c++))),64!==l&&(t+=String.fromCharCode(n));return t}var _="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,A=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function x(e){return _?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function k(e){return _?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var C=function(e){return _?A(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function O(e){if("undefined"==typeof ArrayBuffer)return C(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function R(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var I=_?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:A(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else a.set(new Uint8Array(e[t]),r);return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},N=/\u0000/g,D=/[\u0001-\u0006]/g;function P(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function M(e,t){var r=""+e;return r.length>=t?r:eB("0",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:eB(" ",t-r.length)+r}function F(e,t){var r=""+e;return r.length>=t?r:r+eB(" ",t-r.length)}function U(e,t){var r,a;return e>0x100000000||e<-0x100000000?(r=""+Math.round(e)).length>=t?r:eB("0",t-r.length)+r:(a=""+Math.round(e)).length>=t?a:eB("0",t-a.length)+a}function B(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var H=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],W=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],j={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},G={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},V={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function z(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,l=1,c=0,f=0,h=Math.floor(n);c<t&&(o=(h=Math.floor(n))*i+s,f=h*c+l,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,l=c,c=f;if(f>t&&(c>t?(f=l,o=s):(f=c,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function K(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(o.u)&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var l,c,f,h=new Date(1900,0,1);h.setDate(h.getDate()+a-1),i=[h.getFullYear(),h.getMonth()+1,h.getDate()],s=h.getDay(),a<60&&(s=(s+6)%7),r&&(l=h,c=i,c[0]-=581,f=l.getDay(),l<60&&(f=(f+6)%7),s=f)}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,o.M=(n=Math.floor(n/60))%60,o.H=n=Math.floor(n/60),o.q=s,o}var Y=new Date(1899,11,31,0,0,0),X=Y.getTime(),J=new Date(1900,2,1,0,0,0);function q(e,t){var r=e.getTime();return t?r-=1262304e5:e>=J&&(r+=864e5),(r-(X+(e.getTimezoneOffset()-Y.getTimezoneOffset())*6e4))/864e5}function Z(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Q(e){var t,r,a,n,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(t=e<0?12:11,s=(r=Z(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(a=Z(e.toFixed(11))).length>(e<0?12:11)||"0"===a||"-0"===a?e.toPrecision(6):a,Z(-1==(n=s.toUpperCase()).indexOf("E")?n:n.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function ee(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):Q(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return em(14,q(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function et(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var er=/%/g,ea=/# (\?+)( ?)\/( ?)(\d+)/,en=/^#*0*\.([0#]+)/,es=/\).*[0#]/,ei=/\(###\) ###\\?-####/;function eo(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function el(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ec(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function ef(e,t,r){return(0|r)===r?function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(es)){var n,s=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",s,a):"("+e("n",s,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var i=r,o=i.length-1;44===i.charCodeAt(o-1);)--o;return ef(t,i.substr(0,o),a/Math.pow(10,3*(i.length-o)))}if(-1!==r.indexOf("%"))return c=(l=r).replace(er,""),f=l.length-c.length,ef(t,c,a*Math.pow(10,2*f))+eB("%",f);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),!(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).match(/[Ee]/)){var o=Math.floor(Math.log(r)*Math.LOG10E);-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i),a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var l,c,f,h,u,d,p,m=Math.abs(a),g=a<0?"-":"";if(r.match(/^00+$/))return g+M(m,r.length);if(r.match(/^[#?]+$/))return h=""+a,0===a&&(h=""),h.length>r.length?h:eo(r.substr(0,r.length-h.length))+h;if(u=r.match(ea))return g+(0===m?"":""+m)+eB(" ",(n=u)[1].length+2+n[4].length);if(r.match(/^#+0+$/))return g+M(m,r.length-r.indexOf("0"));if(u=r.match(en))return h=(h=(""+a).replace(/^([^\.]+)$/,"$1."+eo(u[1])).replace(/\.$/,"."+eo(u[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+eB("0",eo(u[1]).length-t.length)}),-1!==r.indexOf("0.")?h:h.replace(/^0\./,".");if(u=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return g+(""+m).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".");if(u=r.match(/^#{1,3},##0(\.?)$/))return g+et(""+m);if(u=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):et(""+a)+"."+eB("0",u[1].length);if(u=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(u=r.match(/^([0#]+)(\\?-([0#]+))+$/))return h=P(e(t,r.replace(/[\\-]/g,""),a)),d=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<h.length?h.charAt(d++):"0"===e?"0":""}));if(r.match(ei))return"("+(h=e(t,"##########",a)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var v="";if(u=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return p=z(m,Math.pow(10,d=Math.min(u[4].length,7))-1,!1),h=""+g," "==(v=ef("n",u[1],p[1])).charAt(v.length-1)&&(v=v.substr(0,v.length-1)+"0"),h+=v+u[2]+"/"+u[3],(v=F(p[2],d)).length<u[4].length&&(v=eo(u[4].substr(u[4].length-v.length))+v),h+=v;if(u=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return g+((p=z(m,Math.pow(10,d=Math.min(Math.max(u[1].length,u[4].length),7))-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?L(p[1],d)+u[2]+"/"+u[3]+F(p[2],d):eB(" ",2*d+1+u[2].length+u[3].length));if(u=r.match(/^[#0?]+$/))return(h=""+a,r.length<=h.length)?h:eo(r.substr(0,r.length-h.length))+h;if(u=r.match(/^([#0]+)\.([#0]+)$/)){d=(h=""+a.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var T=r.indexOf(".")-d,b=r.length-h.length-T;return eo(r.substr(0,T)+h+r.substr(r.length-b))}if(u=r.match(/^00,000\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):et(""+a).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(0,u[1].length);switch(r){case"###,###":case"##,###":case"#,###":var w=et(""+m);return"0"!==w?g+w:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),a)+eo(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(es)){var n,s,i,o,l,c,f=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",f,a):"("+e("n",f,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var h=r,u=h.length-1;44===h.charCodeAt(u-1);)--u;return ef(t,h.substr(0,u),a/Math.pow(10,3*(h.length-u)))}if(-1!==r.indexOf("%"))return p=(d=r).replace(er,""),m=d.length-p.length,ef(t,p,a*Math.pow(10,2*m))+eB("%",m);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i);"0."===a.substr(0,2);)a=(a=a.charAt(0)+a.substr(2,s)+"."+a.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var d,p,m,g,v,T,b,w=Math.abs(a),E=a<0?"-":"";if(r.match(/^00+$/))return E+U(w,r.length);if(r.match(/^[#?]+$/))return"0"===(g=U(a,0))&&(g=""),g.length>r.length?g:eo(r.substr(0,r.length-g.length))+g;if(v=r.match(ea))return o=Math.floor((i=Math.round(w*(s=parseInt((n=v)[4],10))))/s),l=i-o*s,E+(0===o?"":""+o)+" "+(0===l?eB(" ",n[1].length+1+n[4].length):L(l,n[1].length)+n[2]+"/"+n[3]+M(s,n[4].length));if(r.match(/^#+0+$/))return E+U(w,r.length-r.indexOf("0"));if(v=r.match(en))return g=el(a,v[1].length).replace(/^([^\.]+)$/,"$1."+eo(v[1])).replace(/\.$/,"."+eo(v[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+eB("0",eo(v[1]).length-t.length)}),-1!==r.indexOf("0.")?g:g.replace(/^0\./,".");if(v=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return E+el(w,v[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,v[1].length?"0.":".");if(v=r.match(/^#{1,3},##0(\.?)$/))return E+et(U(w,0));if(v=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):et(""+(Math.floor(a)+ +((c=v[1].length)<(""+Math.round((a-Math.floor(a))*Math.pow(10,c))).length)))+"."+M(ec(a,v[1].length),v[1].length);if(v=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(v=r.match(/^([0#]+)(\\?-([0#]+))+$/))return g=P(e(t,r.replace(/[\\-]/g,""),a)),T=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return T<g.length?g.charAt(T++):"0"===e?"0":""}));if(r.match(ei))return"("+(g=e(t,"##########",a)).substr(0,3)+") "+g.substr(3,3)+"-"+g.substr(6);var S="";if(v=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return b=z(w,Math.pow(10,T=Math.min(v[4].length,7))-1,!1),g=""+E," "==(S=ef("n",v[1],b[1])).charAt(S.length-1)&&(S=S.substr(0,S.length-1)+"0"),g+=S+v[2]+"/"+v[3],(S=F(b[2],T)).length<v[4].length&&(S=eo(v[4].substr(v[4].length-S.length))+S),g+=S;if(v=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return E+((b=z(w,Math.pow(10,T=Math.min(Math.max(v[1].length,v[4].length),7))-1,!0))[0]||(b[1]?"":"0"))+" "+(b[1]?L(b[1],T)+v[2]+"/"+v[3]+F(b[2],T):eB(" ",2*T+1+v[2].length+v[3].length));if(v=r.match(/^[#0?]+$/))return(g=U(a,0),r.length<=g.length)?g:eo(r.substr(0,r.length-g.length))+g;if(v=r.match(/^([#0?]+)\.([#0]+)$/)){T=(g=""+a.toFixed(Math.min(v[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var y=r.indexOf(".")-T,_=r.length-g.length-y;return eo(r.substr(0,y)+g+r.substr(r.length-_))}if(v=r.match(/^00,000\.([#0]*0)$/))return T=ec(a,v[1].length),a<0?"-"+e(t,r,-a):et(a<0x7fffffff&&a>-0x80000000?""+(a>=0?0|a:a-1|0):""+Math.floor(a)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(T,v[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",a);case"###,###":case"##,###":case"#,###":var A=et(U(w,0));return"0"!==A?E+A:"";case"###,###.00":return e(t,"###,##0.00",a).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",a).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var eh=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function eu(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":B(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(eh))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var ed=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ep(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function em(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:j)[e])&&(a=r.table&&r.table[G[e]]||j[G[e]]),null==a&&(a=V[e]||"General")}if(B(a,0))return ee(t,r);t instanceof Date&&(t=q(t,r.date1904));var n=function(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(ed),o=r[1].match(ed);return ep(t,i)?[a,r[0]]:ep(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}(a,t);if(B(n[1]))return ee(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],l="",c=0,f="",h="t",u="H";c<e.length;)switch(f=e.charAt(c)){case"G":if(!B(e,c))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(l="";34!==(i=e.charCodeAt(++c))&&c<e.length;)l+=String.fromCharCode(i);o[o.length]={t:"t",v:l},++c;break;case"\\":var d=e.charAt(++c),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==n&&null==(n=K(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},h=f,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==n&&null==(n=K(t,r)))return"";for(l=f;++c<e.length&&e.charAt(c).toLowerCase()===f;)l+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:l},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=K(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",c+=5,u="h"):"上午/下午"===e.substr(c,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",c+=5,u="h"):(m.t="t",++c),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(l=f;"]"!==e.charAt(c++)&&c<e.length;)l+=e.charAt(c);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(eh)){if(null==n&&null==(n=K(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",eu(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=n){for(l=f;++c<e.length&&"0"===(f=e.charAt(c));)l+=f;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=f;++c<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(c))>-1;)l+=f;o[o.length]={t:"n",v:l};break;case"?":for(l=f;e.charAt(++c)===f;)l+=f;o[o.length]={t:f,v:l},h=f;break;case"*":++c,(" "==e.charAt(c)||"*"==e.charAt(c))&&++c;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=f;c<e.length&&"0123456789".indexOf(e.charAt(++c))>-1;)l+=e.charAt(c);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:f,v:f},++c;break;case"$":o[o.length]={t:"t",v:"$"},++c;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++c}var g,v=0,T=0;for(c=o.length-1,h="t";c>=0;--c)switch(o[c].t){case"h":case"H":o[c].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[c].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[c].t;break;case"m":"s"===h&&(o[c].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[c].v.match(/[Hh]/)&&(v=1),v<2&&o[c].v.match(/[Mm]/)&&(v=2),v<3&&o[c].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var b,w="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=function(e,t,r,a){var n,s="",i=0,o=0,l=r.y,c=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:n=l%100,c=2;break;default:n=l%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,c=t.length;break;case 3:return W[r.m-1][1];case 5:return W[r.m-1][0];default:return W[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,c=t.length;break;case 3:return H[r.q][0];default:return H[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return M(r.S,t.length);if((i=Math.round((o=a>=2?3===a?1e3:100:1===a?10:1)*(r.S+r.u)))>=60*o&&(i=0),"s"===t)return 0===i?"0":""+i/o;if(s=M(i,2+a),"ss"===t)return s.substr(0,2);return"."+s.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":n=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:n=l,c=1}return c>0?M(n,c):""}(o[c].t.charCodeAt(0),o[c].v,n,T),o[c].t="t";break;case"n":case"?":for(b=c+1;null!=o[b]&&("?"===(f=o[b].t)||"D"===f||(" "===f||"t"===f)&&null!=o[b+1]&&("?"===o[b+1].t||"t"===o[b+1].t&&"/"===o[b+1].v)||"("===o[c].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[b].v||" "===o[b].v&&null!=o[b+1]&&"?"==o[b+1].t));)o[c].v+=o[b].v,o[b]={v:"",t:";"},++b;w+=o[c].v,c=b-1;break;case"G":o[c].t="t",o[c].v=ee(t,r)}var E,S,y="";if(w.length>0){40==w.charCodeAt(0)?(E=t<0&&45===w.charCodeAt(0)?-t:t,S=ef("n",w,E)):(S=ef("n",w,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),b=S.length-1;var _=o.length;for(c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&o[c].v.indexOf(".")>-1){_=c;break}var A=o.length;if(_===o.length&&-1===S.indexOf("E")){for(c=o.length-1;c>=0;--c)null!=o[c]&&-1!=="n?".indexOf(o[c].t)&&(b>=o[c].v.length-1?(b-=o[c].v.length,o[c].v=S.substr(b+1,o[c].v.length)):b<0?o[c].v="":(o[c].v=S.substr(0,b+1),b=-1),o[c].t="t",A=c);b>=0&&A<o.length&&(o[A].v=S.substr(0,b+1)+o[A].v)}else if(_!==o.length&&-1===S.indexOf("E")){for(b=S.indexOf(".")-1,c=_;c>=0;--c)if(null!=o[c]&&-1!=="n?".indexOf(o[c].t)){for(s=o[c].v.indexOf(".")>-1&&c===_?o[c].v.indexOf(".")-1:o[c].v.length-1,y=o[c].v.substr(s+1);s>=0;--s)b>=0&&("0"===o[c].v.charAt(s)||"#"===o[c].v.charAt(s))&&(y=S.charAt(b--)+y);o[c].v=y,o[c].t="t",A=c}for(b>=0&&A<o.length&&(o[A].v=S.substr(0,b+1)+o[A].v),b=S.indexOf(".")+1,c=_;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===_)){for(s=o[c].v.indexOf(".")>-1&&c===_?o[c].v.indexOf(".")+1:0,y=o[c].v.substr(0,s);s<o[c].v.length;++s)b<S.length&&(y+=S.charAt(b++));o[c].v=y,o[c].t="t",A=c}}}for(c=0;c<o.length;++c)null!=o[c]&&"n?".indexOf(o[c].t)>-1&&(E=a>1&&t<0&&c>0&&"-"===o[c-1].v?-t:t,o[c].v=ef(o[c].t,o[c].v,E),o[c].t="t");var x="";for(c=0;c!==o.length;++c)null!=o[c]&&(x+=o[c].v);return x}(n[1],t,r,n[0])}function eg(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==j[r]){t<0&&(t=r);continue}if(j[r]==e){t=r;break}}t<0&&(t=391)}return j[t]=e,t}function ev(e){for(var t=0;392!=t;++t)void 0!==e[t]&&eg(e[t],t)}function eT(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',j=e}var eb=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,ew=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],l=r[5],c=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a},e.buf=function(e,r){for(var T=-1^r,b=e.length-15,w=0;w<b;)T=v[e[w++]^255&T]^g[e[w++]^T>>8&255]^m[e[w++]^T>>16&255]^p[e[w++]^T>>>24]^d[e[w++]]^u[e[w++]]^h[e[w++]]^f[e[w++]]^c[e[w++]]^l[e[w++]]^o[e[w++]]^i[e[w++]]^s[e[w++]]^n[e[w++]]^a[e[w++]]^t[e[w++]];for(b+=15;w<b;)T=T>>>8^t[(T^e[w++])&255];return~T},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[(a^i)&255]:i<2048?a=(a=a>>>8^t[(a^(192|i>>6&31))&255])>>>8^t[(a^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[(a^(240|i>>8&7))&255])>>>8^t[(a^(128|i>>2&63))&255])>>>8^t[(a^(128|o>>6&15|(3&i)<<4))&255])>>>8^t[(a^(128|63&o))&255]):a=(a=(a=a>>>8^t[(a^(224|i>>12&15))&255])>>>8^t[(a^(128|i>>6&63))&255])>>>8^t[(a^(128|63&i))&255];return~a},e}(),eE=function(){var e,t,r={};function a(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function s(e){tZ(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};21589===a&&(1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,t[a]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return eo(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=T(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",a=(_&&Buffer.isBuffer(e)?e.toString("binary"):T(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if((s=a[n],/^Content-Location:/i.test(s))&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var o="--"+(i[1]||""),l={FileIndex:[],FullPaths:[]};c(l);var f,h=0;for(n=0;n<a.length;++n){var u=a[n];(u===o||u===o+"--")&&(h++&&function(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var c=l.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":n=c[2].trim();break;case"content-type":i=c[2].trim();break;case"content-transfer-encoding":s=c[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=C(y(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return C(t.join("\r\n"))}(t.slice(o));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var f=ec(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}(l,a.slice(f,n),r),f=n)}return l}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,a=512,n=0,s=0,i=0,o=0,f=0,h=[],m=e.slice(0,512);tZ(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(p,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==g[1])return eo(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==a&&tZ(m=e.slice(0,a),28);var v=e.slice(0,a),b=m,w=r,E=9;switch(b.l+=2,E=b.read_shift(2)){case 9:if(3!=w)throw Error("Sector Shift: Expected 9 saw "+E);break;case 12:if(4!=w)throw Error("Sector Shift: Expected 12 saw "+E);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+E)}b.chk("0600","Mini Sector Shift: "),b.chk("000000000000","Reserved: ");var S=m.read_shift(4,"i");if(3===r&&0!==S)throw Error("# Directory Sectors: Expected 0 saw "+S);m.l+=4,i=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),o=m.read_shift(4,"i"),n=m.read_shift(4,"i"),f=m.read_shift(4,"i"),s=m.read_shift(4,"i");for(var A=-1,x=0;x<109&&!((A=m.read_shift(4,"i"))<0);++x)h[x]=A;var k=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,a);!function e(t,r,a,n,s){var i=d;if(t===d){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=a[t],l=(n>>>2)-1;if(!o)return;for(var c=0;c<l&&(i=tz(o,4*c))!==d;++c)s.push(i);e(tz(o,n-4),r-1,a,n,s)}}(f,s,k,a,h);var O=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],l=[],c=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){l=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&c))throw Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m]||p[h=tz(e[m],d)])break}s[u]={nodes:o,data:tE([l])}}return s}(k,i,h,a);O[i].name="!Directory",n>0&&o!==d&&(O[o].name="!MiniFAT"),O[h[0]].name="!FAT",O.fat_addrs=h,O.ssz=a;var R=[],N=[],D=[];(function(e,t,r,a,n,s,i,o){for(var c,f=0,h=2*!!a.length,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);tZ(v,64),g=v.read_shift(2),c=ty(v,0,g-h),a.push(c);var T={name:c,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=l(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=l(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=d,T.name=""),5===T.type?(f=T.start,n>0&&f!==d&&(t[f].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===t[T.start]&&(t[T.start]=function(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,l=0,c=0;for(l=t;l>=0;){n[l]=!0,s[s.length]=l,i.push(e[l]);var f=r[Math.floor(4*l/a)];if(a<4+(c=4*l&o))throw Error("FAT boundary crossed: "+l+" 4 "+a);if(!e[f])break;l=tz(e[f],c)}return{nodes:s,data:tE([i])}}(r,T.start,t.fat_addrs,t.ssz)),t[T.start].name=T.name,T.content=t[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:f!==d&&T.start!==d&&t[f]&&(T.content=function(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*u,i*u+u)),n-=u,i=tz(r,4*i);return 0===s.length?t1(0):I(s).slice(0,e.size)}(T,t[f].data,(t[o]||{}).data))),T.content&&tZ(T.content,0),s[c]=T,i.push(T)}})(i,O,k,R,n,{},N,o),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,l=r.length,c=[],f=[];a<l;++a)c[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,c[a]===a&&(-1!==n&&c[n]!==n&&(c[a]=c[n]),-1!==s&&c[s]!==s&&(c[a]=c[s])),-1!==i&&(c[i]=a),-1!==n&&a!=c[a]&&(c[n]=c[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=c[a]&&(c[s]=c[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<l;++a)c[a]===a&&(-1!==s&&c[s]!==s?c[a]=c[s]:-1!==n&&c[n]!==n&&(c[a]=c[n]));for(a=1;a<l;++a)if(0!==e[a].type){if((o=a)!=c[o])do o=c[o],t[a]=t[o]+"/"+t[a];while(0!==o&&-1!==c[o]&&o!=c[o]);c[a]=-1}for(t[0]+="/",a=1;a<l;++a)2!==e[a].type&&(t[a]+="/")}(N,D,R),R.shift();var P={FileIndex:N,FullPaths:D};return t&&t.raw&&(P.raw={header:v,sectors:k}),P}function l(e,t){return new Date((tV(e,t+4)/1e7*0x100000000+tV(e,t)/1e7-0x2b6109100)*1e3)}function c(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!eE.find(e,"/"+t)){var r=t1(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),f(e)}}(e)}function f(e,t){c(e);for(var r=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(r=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(r=!0);break;default:r=!0}}if(r||t){var l=new Date(1987,1,19),f=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=a(u[i][0]);(s=h[d])||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:g,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||g,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(f=i+1;f<u.length&&a(e.FullPaths[f])!=m;++f);for(p.C=f>=u.length?-1:f,f=i+1;f<u.length&&a(e.FullPaths[f])!=a(m);++f);p.R=f>=u.length?-1:f,p.type=1}else a(e.FullPaths[i+1]||"")==a(m)&&(p.R=i+1),p.type=2}}}function h(e,r){var a=r||{};if("mad"==a.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(i=e.FullPaths[l].slice(s.length),(o=e.FileIndex[l]).size&&o.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var c=o.content,f=_&&Buffer.isBuffer(c)?c.toString("binary"):T(c),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&el[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&el[a[1]]?el[a[1]]:"application/octet-stream"}(o,i)),n.push(""),n.push(m?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],a=t.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0==s.length){r.push("");continue}for(var i=0;i<s.length;){var o=76,l=s.slice(i,i+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=s.slice(i,i+o),(i+=o)<s.length&&(l+="="),r.push(l)}}return r.join("\r\n")}(f):function(e){for(var t=S(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,a);if(f(e),"zip"===a.fileType)return function(e,r){var a=[],n=[],s=t1(1),i=8*!!(r||{}).compression,o=0,l=0,c=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=c,T=t1(u.length);for(l=0;l<u.length;++l)T.write_shift(1,127&u.charCodeAt(l));T=T.slice(0,T.l),p[f]=ew.buf(d.content,0);var b=d.content;8==i&&(g=b,b=t?t.deflateRawSync(g):Q(g)),(s=t1(30)).write_shift(4,0x4034b50),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,(0,p[f])),s.write_shift(4,(0,b.length)),s.write_shift(4,(0,d.content.length)),s.write_shift(2,T.length),s.write_shift(2,0),c+=s.length,a.push(s),c+=T.length,a.push(T),c+=b.length,a.push(b),(s=t1(46)).write_shift(4,0x2014b50),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[f]),s.write_shift(4,b.length),s.write_shift(4,d.content.length),s.write_shift(2,T.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,n.push(s),m+=T.length,n.push(T),++f}return(s=t1(22)).write_shift(4,0x6054b50),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,f),s.write_shift(2,f),s.write_shift(4,m),s.write_shift(4,c),s.write_shift(2,0),I([I(a),I(n),s])}(e,a);var n=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+7>>3,l=t+127>>7,c=o+r+i+l,f=c+127>>7,h=f<=109?0:Math.ceil((f-109)/127);c+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,l,i,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=t1(n[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,m[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:d),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:d),s.write_shift(4,n[1]),i=0;i<109;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(o=0;o<n[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);s.write_shift(-4,o===n[1]-1?d:o+1)}var l=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,d))};for(o=(i=0)+n[1];i<o;++i)s.write_shift(-4,v.DIFSECT);for(o+=n[2];i<o;++i)s.write_shift(-4,v.FATSECT);l(n[3]),l(n[4]);for(var c=0,h=0,u=e.FileIndex[0];c<e.FileIndex.length;++c)(u=e.FileIndex[c]).content&&((h=u.content.length)<4096||(u.start=o,l(h+511>>9)));for(l(n[6]+7>>3);511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(c=0,o=i=0;c<e.FileIndex.length;++c)(u=e.FileIndex[c]).content&&(h=u.content.length)&&!(h>=4096)&&(u.start=o,l(h+63>>6));for(;511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var p=e.FullPaths[i];if(!p||0===p.length){for(c=0;c<17;++c)s.write_shift(4,0);for(c=0;c<3;++c)s.write_shift(4,-1);for(c=0;c<12;++c)s.write_shift(4,0);continue}u=e.FileIndex[i],0===i&&(u.start=u.size?u.start-1:d);var g=0===i&&a.root||u.name;if(h=2*(g.length+1),s.write_shift(64,g,"utf16le"),s.write_shift(2,h),s.write_shift(1,u.type),s.write_shift(1,u.color),s.write_shift(-4,u.L),s.write_shift(-4,u.R),s.write_shift(-4,u.C),u.clsid)s.write_shift(16,u.clsid,"hex");else for(c=0;c<4;++c)s.write_shift(4,0);s.write_shift(4,u.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,u.start),s.write_shift(4,u.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>=4096)if(s.l=u.start+1<<9,_&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+511&-512;else{for(c=0;c<u.size;++c)s.write_shift(1,u.content[c]);for(;511&c;++c)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>0&&u.size<4096)if(_&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+63&-64;else{for(c=0;c<u.size;++c)s.write_shift(1,u.content[c]);for(;63&c;++c)s.write_shift(1,0)}if(_)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}r.version="1.2.1";var u=64,d=-2,p="d0cf11e0a1b11ae1",m=[208,207,17,224,161,177,26,225],g="00000000000000000000000000000000",v={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:p,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:g,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function T(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var b=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],w=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],E=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],O="undefined"!=typeof Uint8Array,R=O?new Uint8Array(256):[],P=0;P<256;++P)R[P]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(P);function M(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function L(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function F(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function U(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a||(i|=e[n+1]<<8-a,r<16-a||(i|=e[n+2]<<16-a,r<24-a))?i&s:(i|=e[n+3]<<24-a)&s}function B(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function H(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function W(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function j(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(_){var s=k(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(O){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function G(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function V(e,t,r){var a=1,n=0,s=0,i=0,o=0,l=e.length,c=O?new Uint16Array(32):G(32);for(s=0;s<32;++s)c[s]=0;for(s=l;s<r;++s)e[s]=0;l=e.length;var f=O?new Uint16Array(l):G(l);for(s=0;s<l;++s)c[n=e[s]]++,a<n&&(a=n),f[s]=0;for(s=1,c[0]=0;s<=a;++s)c[s+16]=o=o+c[s-1]<<1;for(s=0;s<l;++s)0!=(o=e[s])&&(f[s]=c[o+16]++);var h=0;for(s=0;s<l;++s)if(0!=(h=e[s]))for(o=function(e,t){var r=R[255&e];return t<=8?r>>>8-t:(r=r<<8|R[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|R[e>>16&255])>>>24-t}(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var z=O?new Uint16Array(512):G(512),K=O?new Uint16Array(32):G(32);if(!O){for(var Y=0;Y<512;++Y)z[Y]=0;for(Y=0;Y<32;++Y)K[Y]=0}for(var X=[],J=0;J<32;J++)X.push(5);V(X,K,32);var q=[];for(J=0;J<=143;J++)q.push(8);for(;J<=255;J++)q.push(9);for(;J<=279;J++)q.push(7);for(;J<=287;J++)q.push(8);V(q,z,288);var Z=function(){for(var e=O?new Uint8Array(32768):[],t=0,r=0;t<E.length-1;++t)for(;r<E[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=O?new Uint8Array(259):[];for(t=0,r=0;t<w.length-1;++t)for(;r<w[t+1];++r)a[r]=t;return function(t,r){if(t.length<8){for(var n=0;n<t.length;){var s=Math.min(65535,t.length-n),i=n+s==t.length;for(r.write_shift(1,+i),r.write_shift(2,s),r.write_shift(2,65535&~s);s-- >0;)r[r.l++]=t[n++]}return r.l}return function(t,r){for(var n=0,s=0,i=O?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=B(r,n,+(s+o==t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l;continue}n=B(r,n,+(s+o==t.length)+2);for(var l=0;o-- >0;){var c,f,h=t[s],u=-1,d=0;if((u=i[l=(l<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;t[u+d]==t[s+d]&&d<250;)++d;if(d>2){(h=a[d])<=22?n=H(r,n,R[h+1]>>1)-1:(H(r,n,3),H(r,n+=5,R[h-23]>>5),n+=3);var p=h<8?0:h-4>>2;p>0&&(W(r,n,d-w[h]),n+=p),n=H(r,n,R[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(W(r,n,s-u-E[h]),n+=m);for(var g=0;g<d;++g)i[l]=32767&s,l=(l<<5^t[s])&32767,++s;o-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(c=n)),r[c>>>3]|=f,n=c+1),n=H(r,n,R[h]),i[l]=32767&s,++s}n=H(r,n,0)-1}return r.l=(n+7)/8|0,r.l}(t,r)}}();function Q(e){var t=t1(50+Math.floor(1.1*e.length)),r=Z(e,t);return t.slice(0,r)}var ee=O?new Uint16Array(32768):G(32768),et=O?new Uint16Array(32768):G(32768),er=O?new Uint16Array(128):G(128),ea=1,en=1;function es(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[x(t),2];for(var r=0,a=0,n=k(t||262144),s=0,i=n.length>>>0,o=0,l=0;(1&a)==0;){if(a=M(e,r),r+=3,a>>>1==0){7&r&&(r+=8-(7&r));var c=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,c>0)for(!t&&i<s+c&&(i=(n=j(n,s+c)).length);c-- >0;)n[s++]=e[r>>>3],r+=8;continue}for(a>>1==1?(o=9,l=5):(r=function(e,t){var r,a,n,s=L(e,t)+257,i=L(e,t+=5)+1;t+=5;var o=(a=7&(r=t),((e[n=r>>>3]|(a<=4?0:e[n+1]<<8))>>>a&15)+4);t+=4;for(var l=0,c=O?new Uint8Array(19):G(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=O?new Uint8Array(8):G(8),d=O?new Uint8Array(8):G(8),p=c.length,m=0;m<o;++m)c[b[m]]=l=M(e,t),h<l&&(h=l),u[l]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=c[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=c[m])){g=R[f[m]]>>8-v;for(var T=(1<<7-v)-1;T>=0;--T)er[g|T<<v]=7&v|m<<3}var w=[];for(h=1;w.length<s+i;)switch(g=er[F(e,t)],t+=7&g,g>>>=3){case 16:for(l=3+function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}(e,t),t+=2,g=w[w.length-1];l-- >0;)w.push(g);break;case 17:for(l=3+M(e,t),t+=3;l-- >0;)w.push(0);break;case 18:for(l=11+F(e,t),t+=7;l-- >0;)w.push(0);break;default:w.push(g),h<g&&(h=g)}var E=w.slice(0,s),S=w.slice(s);for(m=s;m<286;++m)E[m]=0;for(m=i;m<30;++m)S[m]=0;return ea=V(E,ee,286),en=V(S,et,30),t}(e,r),o=ea,l=en);;){!t&&i<s+32767&&(i=(n=j(n,s+32767)).length);var f=U(e,r,o),h=a>>>1==1?z[f]:ee[f];if(r+=15&h,((h>>>=4)>>>8&255)==0)n[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+w[h];u>0&&(d+=U(e,r,u),r+=u),f=U(e,r,l),r+=15&(h=a>>>1==1?K[f]:et[f]);var p=(h>>>=4)<4?0:h-2>>1,m=E[h];for(p>0&&(m+=U(e,r,p),r+=p),!t&&i<d&&(i=(n=j(n,d+100)).length);s<d;)n[s]=n[s-m],++s}}}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ei(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function eo(e,r){tZ(e,0);var a={FileIndex:[],FullPaths:[]};c(a,{root:r.root});for(var n=e.length-4;(80!=e[n]||75!=e[n+1]||5!=e[n+2]||6!=e[n+3])&&n>=0;)--n;e.l=n+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var o=e.read_shift(4);for(n=0,e.l=o;n<i;++n){e.l+=20;var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,a,n,i){e.l+=2;var o,l,c,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(o=65535&e.read_shift(2),l=65535&e.read_shift(2),c=new Date,f=31&l,h=15&(l>>>=5),l>>>=4,c.setMilliseconds(0),c.setFullYear(l+1980),c.setMonth(h-1),c.setDate(f),u=31&o,d=63&(o>>>=5),o>>>=6,c.setHours(o),c.setMinutes(d),c.setSeconds(u<<1),c);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),T=e.read_shift(4),b=e.read_shift(4),w=e.read_shift(2),E=e.read_shift(2),S="",y=0;y<w;++y)S+=String.fromCharCode(e[e.l++]);if(E){var _=s(e.slice(e.l,e.l+E));(_[21589]||{}).mt&&(g=_[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=E;var A=e.slice(e.l,e.l+T);switch(m){case 8:A=function(e,r){if(!t)return es(e,r);var a=new t.InflateRaw,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}(e,b);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var x=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),x=!0),T=e.read_shift(4),b=e.read_shift(4)),T!=r&&ei(x,"Bad compressed size: "+r+" != "+T),b!=a&&ei(x,"Bad uncompressed size: "+a+" != "+b),ec(n,S,A,{unsafe:!0,mt:g})}(e,l,f,a,m),e.l=g}return a}var el={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ec(e,t,r,a){var s=a&&a.unsafe;s||c(e);var i=!s&&eE.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:n(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||eE.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),a=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(D);for(s=s.replace(N,""),o&&(s=s.replace(D,"!")),i=0;i<r.length;++i)if((o?r[i].replace(D,"!"):r[i]).replace(N,"")==s||(o?a[i].replace(D,"!"):a[i]).replace(N,"")==s)return e.FileIndex[i];return null},r.read=function(t,r){var a=r&&r.type;switch(!a&&_&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return i(),o(e.readFileSync(t),r);case"base64":return o(C(y(t)),r);case"binary":return o(C(t),r)}return o(t,r)},r.parse=o,r.write=function(t,r){var a=h(t,r);switch(r&&r.type||"buffer"){case"file":i(),e.writeFileSync(r.filename,a);break;case"binary":return"string"==typeof a?a:T(a);case"base64":return S("string"==typeof a?a:T(a));case"buffer":if(_)return Buffer.isBuffer(a)?a:A(a);case"array":return"string"==typeof a?C(a):a}return a},r.writeFile=function(t,r,a){i();var n=h(t,a);e.writeFileSync(r,n)},r.utils={cfb_new:function(e){var t={};return c(t,e),t},cfb_add:ec,cfb_del:function(e,t){c(e);var r=eE.find(e,t);if(r){for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0}return!1},cfb_mov:function(e,t,r){c(e);var a=eE.find(e,t);if(a){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(r),e.FullPaths[s]=r,!0}return!1},cfb_gc:function(e){f(e,!0)},ReadShift:tK,CheckField:tq,prep_blob:tZ,bconcat:I,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:Q,_inflateRaw:es,consts:v},r}();function eS(e,t,r){if(void 0!==a&&a.writeFileSync)return r?a.writeFileSync(e,t,r):a.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=O(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?ts(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var s=new Blob([function(e){if("string"==typeof e)return O(e);if(Array.isArray(e)){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}return e}(n)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(s,e);if("undefined"!=typeof saveAs)return saveAs(s,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(s);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var o=document.createElement("a");if(null!=o.download)return o.download=e,o.href=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var l=File(e);return l.open("w"),l.encoding="binary",Array.isArray(t)&&(t=R(t)),l.write(t),l.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function ey(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function e_(e,t){for(var r=[],a=ey(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function eA(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function ex(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var ek=new Date(1899,11,30,0,0,0);function eC(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(ek.getTime()+(e.getTimezoneOffset()-ek.getTimezoneOffset())*6e4))/864e5}var eO=new Date,eR=ek.getTime()+(eO.getTimezoneOffset()-ek.getTimezoneOffset())*6e4,eI=eO.getTimezoneOffset();function eN(e){var t=new Date;return t.setTime(24*e*36e5+eR),t.getTimezoneOffset()!==eI&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eI)*6e4),t}var eD=new Date("2017-02-19T19:06:09.000Z"),eP=isNaN(eD.getFullYear())?new Date("2/19/17"):eD,eM=2017==eP.getFullYear();function eL(e,t){var r=new Date(e);if(eM)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==eP.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function eF(e,t){if(_&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return ts(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return ts(v(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return ts(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return ts(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function eU(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eU(e[r]));return t}function eB(e,t){for(var r="";r.length<t;)r+=e;return r}function eH(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(a))&&isNaN(t=Number(a=a.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var eW=["january","february","march","april","may","june","july","august","september","october","november","december"];function ej(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==eW.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}function eG(e){return e?e.content&&e.type?eF(e.content,!0):e.data?T(e.data):e.asNodeBuffer&&_?T(e.asNodeBuffer().toString("binary")):e.asBinary?T(e.asBinary()):e._data&&e._data.getContent?T(eF(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function eV(e,t){for(var r=e.FullPaths||ey(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function ez(e,t){var r=eV(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eK(e,t,r){if(!r){var a;return(a=ez(e,t))&&".bin"===a.name.slice(-4)?function(e){if(!e)return null;if(e.data)return g(e.data);if(e.asNodeBuffer&&_)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?g(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}(a):eG(a)}if(!t)return null;try{return eK(e,t)}catch(e){return null}}function e$(e,t,r){if(!r)return eG(ez(e,t));if(!t)return null;try{return e$(e,t)}catch(e){return null}}function eY(e,t,r){if(e.FullPaths){if("string"==typeof r){var a;return a=_?A(r):function(e){for(var t=[],r=0,a=e.length+250,n=x(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=x(65535),a=65530)}return t.push(n.slice(0,r)),I(t)}(r),eE.utils.cfb_add(e,t,a)}eE.utils.cfb_add(e,t,r)}else e.file(t,r)}function eX(){return eE.utils.cfb_new()}var eJ='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',eq=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,eZ=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,eQ=eJ.match(eZ)?eZ:/<[^>]*>/g,e1=/<(\/?)\w+:/;function e0(e,t,r){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(eq),o=0,l="",c=0,f="",h="",u=1;if(i)for(c=0;c!=i.length;++c){for(s=0,h=i[c];s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(o=0,u=+(34==(n=h.charCodeAt(s+1))||39==n),l=h.slice(s+1+u,h.length-u);o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=l,r||(a[f.toLowerCase()]=l);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=l,r||(a[d.toLowerCase()]=l)}}return a}var e2=eA({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),e4=/[&<>'"]/g,e3=/[\u0000-\u0008\u000b-\u001f]/g;function e5(e){return(e+"").replace(e4,function(e){return e2[e]}).replace(e3,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function e6(e){return e5(e).replace(/ /g,"_x0020_")}var e8=/[\u0000-\u001f]/g;function e7(e){return(e+"").replace(e4,function(e){return e2[e]}).replace(/\n/g,"<br/>").replace(e8,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function e9(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function te(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;){if((a=e.charCodeAt(r++))<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){t+=String.fromCharCode((31&a)<<6|63&n);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s);continue}t+=String.fromCharCode(55296+((o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023)),t+=String.fromCharCode(56320+(1023&o))}return t}function tt(e){var t,r,a,n=x(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=(31&a)*64+(63&e.charCodeAt(r+1)),s=2):a<240?(t=(15&a)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),s=3):(s=4,o=55296+((t=(7&a)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function tr(e){return A(e,"binary").toString("utf8")}var ta="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",tn=_&&(tr(ta)==te(ta)&&tr||tt(ta)==te(ta)&&tt)||te,ts=_?function(e){return A(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,t.push(String.fromCharCode(240+((n=e.charCodeAt(r++)-56320+(a<<10))>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},ti=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),to=/<\/?(?:vt:)?variant>/g,tl=/<(?:vt:)([^>]*)>([\s\S]*)</;function tc(e,t){var r=e0(e),a=e.match((null)(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(e){var t=e.replace(to,"").match(tl);t&&n.push({v:tn(t[2]),t:t[1]})}),n}var tf=/(^\s|\s$|\n)/;function th(e,t){return"<"+e+(t.match(tf)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function tu(e){return ey(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function td(e,t,r){return"<"+e+(null!=r?tu(r):"")+(null!=t?(t.match(tf)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tp(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tm(e){if(_&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return tn(R(function e(t){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(t instanceof ArrayBuffer)return e(new Uint8Array(t));for(var r=Array(t.length),a=0;a<t.length;++a)r[a]=t[a];return r}(e)));throw Error("Bad input format: expected Buffer or string")}var tg=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tv={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tT=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],tb={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tw=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},tE=_?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:A(e)})):tw(e)}:tw,tS=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(tj(e,n)));return a.join("").replace(N,"")},ty=_?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(N,""):tS(e,t,r)}:tS,t_=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},tA=_?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):t_(e,t,r)}:t_,tx=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(tW(e,n)));return a.join("")},tk=_?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):tx(e,t,r)}:tx,tC=function(e,t){var r=tV(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tO=tC,tR=function(e,t){var r=tV(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tI=tR,tN=function(e,t){var r=2*tV(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tD=tN,tP=function(e,t){var r=tV(e,t);return r>0?ty(e,t+4,t+4+r):""},tM=tP,tL=function(e,t){var r=tV(e,t);return r>0?tk(e,t+4,t+4+r):""},tF=tL,tU=function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?1/0*r:NaN:(0==a?a=-1022:(a-=1023,n+=0x10000000000000),r*Math.pow(2,a-52)*n)},tB=tU,tH=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};_&&(tO=function(e,t){if(!Buffer.isBuffer(e))return tC(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tI=function(e,t){if(!Buffer.isBuffer(e))return tR(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tD=function(e,t){if(!Buffer.isBuffer(e))return tN(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tM=function(e,t){if(!Buffer.isBuffer(e))return tP(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tF=function(e,t){if(!Buffer.isBuffer(e))return tL(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},tB=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):tU(e,t)},tH=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==n&&(ty=function(e,t,r){return n.utils.decode(1200,e.slice(t,r)).replace(N,"")},tk=function(e,t,r){return n.utils.decode(65001,e.slice(t,r))},tO=function(e,t){var r=tV(e,t);return r>0?n.utils.decode(f,e.slice(t+4,t+4+r-1)):""},tI=function(e,t){var r=tV(e,t);return r>0?n.utils.decode(c,e.slice(t+4,t+4+r-1)):""},tD=function(e,t){var r=2*tV(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tM=function(e,t){var r=tV(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r)):""},tF=function(e,t){var r=tV(e,t);return r>0?n.utils.decode(65001,e.slice(t+4,t+4+r)):""});var tW=function(e,t){return e[t]},tj=function(e,t){return 256*e[t+1]+e[t]},tG=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},tV=function(e,t){return 0x1000000*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},tz=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function tK(e,t){var r,a,s,i,o,l,f="",h=[];switch(t){case"dbcs":if(l=this.l,_&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(tj(this,l)),l+=2;e*=2;break;case"utf8":f=tk(this,this.l,this.l+e);break;case"utf16le":e*=2,f=ty(this,this.l,this.l+e);break;case"wstr":if(void 0===n)return tK.call(this,e,"dbcs");f=n.utils.decode(c,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tO(this,this.l),e=4+tV(this,this.l);break;case"lpstr-cp":f=tI(this,this.l),e=4+tV(this,this.l);break;case"lpwstr":f=tD(this,this.l),e=4+2*tV(this,this.l);break;case"lpp4":e=4+tV(this,this.l),f=tM(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+tV(this,this.l),f=tF(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(s=tW(this,this.l+e++));)h.push(b(s));f=h.join("");break;case"_wstr":for(e=0,f="";0!==(s=tj(this,this.l+e));)h.push(b(s)),e+=2;e+=2,f=h.join("");break;case"dbcs-cont":for(o=0,f="",l=this.l;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return s=tW(this,l),this.l=l+1,i=tK.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),h.join("")+i;h.push(b(tj(this,l))),l+=2}f=h.join(""),e*=2;break;case"cpstr":if(void 0!==n){f=n.utils.decode(c,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(o=0,f="",l=this.l;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return s=tW(this,l),this.l=l+1,i=tK.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),h.join("")+i;h.push(b(tW(this,l))),l+=1}f=h.join("");break;default:switch(e){case 1:return r=tW(this,this.l),this.l++,r;case 2:return r=("i"===t?tG:tj)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?tz:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return a=tV(this,this.l),this.l+=4,a;case 8:case -8:if("f"===t)return a=8==e?tB(this,this.l):tB([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:f=tA(this,this.l,e)}}return this.l+=e,f}var t$=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},tY=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},tX=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function tJ(e,t,r){var a=0,s=0;if("dbcs"===r){for(s=0;s!=t.length;++s)tX(this,t.charCodeAt(s),this.l+2*s);a=2*t.length}else if("sbcs"===r){if(void 0!==n&&874==f)for(s=0;s!=t.length;++s){var i=n.utils.encode(f,t.charAt(s));this[this.l+s]=i[0]}else for(s=0,t=t.replace(/[^\x00-\x7F]/g,"_");s!=t.length;++s)this[this.l+s]=255&t.charCodeAt(s);a=t.length}else if("hex"===r){for(;s<e;++s)this[this.l++]=parseInt(t.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(s=0;s<Math.min(t.length,e);++s){var l=t.charCodeAt(s);this[this.l++]=255&l,this[this.l++]=l>>8}for(;this.l<o;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,t$(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<0x10000000000000)?n=-1022:(s-=0x10000000000000,n+=1023)):(n=2047,s=26985*!!isNaN(t));for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case -4:a=4,tY(this,t,this.l)}return this.l+=a,this}function tq(e,t){var r=tA(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function tZ(e,t){e.l=t,e.read_shift=tK,e.chk=tq,e.write_shift=tJ}function tQ(e,t){e.l+=t}function t1(e){var t=x(e);return tZ(t,0),t}function t0(){var e=[],t=_?256:2048,r=function(e){var t=t1(e);return tZ(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),I(e)},_bufs:e}}function t2(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=sb[s].p||(r||[]).length||0),n=1+ +(s>=128)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,(127&s)+128),i.write_shift(1,s>>7));for(var o=0;4!=o;++o)if(a>=128)i.write_shift(1,(127&a)+128),a>>=7;else{i.write_shift(1,a);break}a>0&&tH(r)&&e.push(r)}}function t4(e,t,r){var a=eU(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function t3(e,t,r){var a=eU(e);return a.s=t4(a.s,t.s,r),a.e=t4(a.e,t.s,r),a}function t5(e,t){if(e.cRel&&e.c<0)for(e=eU(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=eU(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rr(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function t6(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?t5(e.s,t.biff)+":"+t5(e.e,t.biff):(e.s.rRel?"":"$")+t7(e.s.r)+":"+(e.e.rRel?"":"$")+t7(e.e.r):(e.s.cRel?"":"$")+re(e.s.c)+":"+(e.e.cRel?"":"$")+re(e.e.c)}function t8(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function t7(e){return""+(e+1)}function t9(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function re(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function rt(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function rr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function ra(e){var t=e.indexOf(":");return -1==t?{s:rt(e),e:rt(e)}:{s:rt(e.slice(0,t)),e:rt(e.slice(t+1))}}function rn(e,t){return void 0===t||"number"==typeof t?rn(e.s,e.e):("string"!=typeof e&&(e=rr(e)),"string"!=typeof t&&(t=rr(t)),e==t?e:e+":"+t)}function rs(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;return t.e.r=--r,t}function ri(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=em(e.z,r?eC(t):t)}catch(e){}try{return e.w=em((e.XF||{}).numFmtId||14*!!r,r?eC(t):t)}catch(e){return""+t}}function ro(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rM[e.v]||e.v:void 0==t?ri(e,e.v):ri(e,t)}function rl(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function rc(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var l="string"==typeof a.origin?rt(a.origin):a.origin;i=l.r,o=l.c}s["!ref"]||(s["!ref"]="A1:A1")}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=rs(s["!ref"]);c.s.c=f.s.c,c.s.r=f.s.r,c.e.c=Math.max(c.e.c,f.e.c),c.e.r=Math.max(c.e.r,f.e.r),-1==i&&(c.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(c.s.r>p&&(c.s.r=p),c.s.c>m&&(c.s.c=m),c.e.r<p&&(c.e.r=p),c.e.c<m&&(c.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||j[14],a.cellDates?(d.t="d",d.w=em(d.z,eC(d.v))):(d.t="n",d.v=eC(d.v),d.w=em(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=rr({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return c.s.c<1e7&&(s["!ref"]=rn(c)),s}function rf(e,t){return rc(null,e,t)}function rh(e,t){return t||(t=t1(4)),t.write_shift(4,e),t}function ru(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rd(e,t){var r=!1;return null==t&&(r=!0,t=t1(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rp(e,t){var r=e.l,a=e.read_shift(1),n=ru(e),s=[],i={t:n,h:n};if((1&a)!=0){for(var o=e.read_shift(4),l=0;l!=o;++l)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function rm(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rg(e,t){return null==t&&(t=t1(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rv(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rT(e,t){return null==t&&(t=t1(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rb(e){var t=e.read_shift(4);return 0===t||0xffffffff===t?"":e.read_shift(t,"dbcs")}function rw(e,t){var r=!1;return null==t&&(r=!0,t=t1(127)),t.write_shift(4,e.length>0?e.length:0xffffffff),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rE(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?tB([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):tz(t,0)>>2;return r?n/100:n}function rS(e,t){null==t&&(t=t1(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-0x20000000&&e<0x20000000?a=1:n==(0|n)&&n>=-0x20000000&&n<0x20000000&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function ry(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var r_=function(e,t){return t||(t=t1(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function rA(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function rx(e,t){return(t||t1(8)).write_shift(8,e,"f")}function rk(e,t){if(t||(t=t1(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function rC(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(r>400)throw Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var rO=[80,81],rR={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rI={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rN={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rD=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rP=eU([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rM={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rL={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rF={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rU={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function rB(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function rH(e,t){var r,a=function(e){for(var t=[],r=ey(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(rF),n=[];n[n.length]=eJ,n[n.length]=td("Types",null,{xmlns:tv.CT,"xmlns:xsd":tv.xsd,"xmlns:xsi":tv.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return td("Default",null,{Extension:e[0],ContentType:e[1]})}));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=td("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:rU[a][t.bookType]||rU[a].xlsx}))},i=function(r){(e[r]||[]).forEach(function(e){n[n.length]=td("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:rU[r][t.bookType]||rU[r].xlsx})})},o=function(t){(e[t]||[]).forEach(function(e){n[n.length]=td("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})})};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var rW={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function rj(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function rG(e){var t=[eJ,td("Relationships",null,{xmlns:tv.RELS})];return ey(e["!id"]).forEach(function(r){t[t.length]=td("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function rV(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[rW.HLINK,rW.XPATH,rW.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function rz(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function rK(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+l.version+"</meta:generator></office:meta></office:document-meta>"}var r$=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function rY(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=e5(t),a[a.length]=r?td(e,t,r):th(e,t))}function rX(e,t){var r=t||{},a=[eJ,td("cp:coreProperties",null,{"xmlns:cp":tv.CORE_PROPS,"xmlns:dc":tv.dc,"xmlns:dcterms":tv.dcterms,"xmlns:dcmitype":tv.dcmitype,"xmlns:xsi":tv.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&rY("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tp(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&rY("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tp(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=r$.length;++s){var i=r$[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&rY(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var rJ=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rq=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function rZ(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=eJ,t[t.length]=td("Properties",null,{xmlns:tv.EXT_PROPS,"xmlns:vt":tv.vt}),rJ.forEach(function(r){var a;if(void 0!==e[r[1]]){switch(r[2]){case"string":a=e5(String(e[r[1]]));break;case"bool":a=e[r[1]]?"true":"false"}void 0!==a&&(t[t.length]=td(r[0],a))}}),t[t.length]=td("HeadingPairs",td("vt:vector",td("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+td("vt:variant",td("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=td("TitlesOfParts",td("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+e5(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function rQ(e){var t=[eJ,td("Properties",null,{xmlns:tv.CUST_PROPS,"xmlns:vt":tv.vt})];if(!e)return t.join("");var r=1;return ey(e).forEach(function(a){++r,t[t.length]=td("property",function(e,t){switch(typeof e){case"string":var r=td("vt:lpwstr",e5(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return td((0|e)==e?"vt:i4":"vt:r8",e5(String(e)));case"boolean":return td("vt:bool",e?"true":"false")}if(e instanceof Date)return td("vt:filetime",tp(e));throw Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:e5(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var r1={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function r0(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+t/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function r2(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function r4(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function r3(e,t,r){return 31===t?r4(e):r2(e,t,r)}function r5(e,t,r){return r3(e,t,4*(!1!==r))}function r6(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(N,"").replace(D,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function r8(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function r7(e,t,r){var a,n,s=e.read_shift(2),i=r||{};if(e.l+=2,12!==t&&s!==t&&-1===rO.indexOf(t)&&((65534&t)!=4126||(65534&s)!=4126))throw Error("Expected type "+t+" saw "+s);switch(12===t?s:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return r2(e,s,4).replace(N,"");case 31:return r4(e);case 64:return r0(e);case 65:return r8(e);case 71:return(a={}).Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a;case 80:return r5(e,s,!i.raw).replace(N,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return r3(e,t,0)})(e,s).replace(N,"");case 4108:for(var o=e.read_shift(4),l=[],c=0;c<o/2;++c)l.push(function(e){var t=e.l,r=r7(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,r7(e,3)]}(e));return l;case 4126:case 4127:return 4127==s?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(N,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(N,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function r9(e,t){var r,a,n,s,i,o=t1(4),l=t1(4);switch(o.write_shift(4,80==e?31:e),e){case 3:l.write_shift(-4,t);break;case 5:(l=t1(8)).write_shift(8,t,"f");break;case 11:l.write_shift(4,+!!t);break;case 64:a=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+0x2b6109100)%0x100000000,n=(r-a)/0x100000000*1e7,(s=(a*=1e7)/0x100000000|0)>0&&(a%=0x100000000,n+=s),(i=t1(8)).write_shift(4,a),i.write_shift(4,n),l=i;break;case 31:case 80:for((l=t1(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),l.write_shift(0,t,"dbcs");l.l!=l.length;)l.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return I([o,l])}function ae(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,l=-1,c={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var m=t[s[i][0]];if(u[m.n]=r7(e,m.t,{raw:!0}),"version"===m.p&&(u[m.n]=String(u[m.n]>>16)+"."+("0000"+String(65535&u[m.n])).slice(-4)),"CodePage"==m.n)switch(u[m.n]){case 0:u[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:p(o=u[m.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[m.n])}}else if(1===s[i][0]){if(p(o=u.CodePage=r7(e,2)),-1!==l){var g=e.l;e.l=s[l][1],c=r6(e,o),e.l=g}}else if(0===s[i][0]){if(0===o){l=i,e.l=s[i+1][1];continue}c=r6(e,o)}else{var v,T=c[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=r8(e);break;case 30:case 31:e.l+=4,v=r5(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=ai(e,4);break;case 64:e.l+=4,v=eL(r0(e));break;default:throw Error("unparsed value: "+e[e.l])}u[T]=v}}return e.l=r+a,u}var at=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function ar(e,t,r){var a=t1(8),n=[],s=[],i=8,o=0,l=t1(8),c=t1(8);if(l.write_shift(4,2),l.write_shift(4,1200),c.write_shift(4,1),s.push(l),n.push(c),i+=8+l.length,!t){(c=t1(8)).write_shift(4,0),n.unshift(c);var f=[t1(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((l=t1(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");l.l!=l.length;)l.write_shift(1,0);f.push(l)}l=I(f),s.unshift(l),i+=8+l.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]]||at.indexOf(e[o][0])>-1||rq.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(m[0]<<16)+(+m[1]||0)}l=r9(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),l=r9(g,u)}s.push(l),(c=t1(8)).write_shift(4,t?d:2+o),n.push(c),i+=8+l.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,v),v+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),I([a].concat(n).concat(s))}function aa(e,t,r){var a,n=e.content;if(!n)return{};tZ(n,0);var s,i,o,l,c=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),h=n.read_shift(16);if(h!==eE.utils.consts.HEADER_CLSID&&h!==r)throw Error("Bad PropertySet CLSID "+h);if(1!==(s=n.read_shift(4))&&2!==s)throw Error("Unrecognized #Sets: "+s);if(i=n.read_shift(16),l=n.read_shift(4),1===s&&l!==n.l)throw Error("Length mismatch: "+l+" !== "+n.l);2===s&&(o=n.read_shift(16),c=n.read_shift(4));var u=ae(n,t),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=i,1===s)return d;if(c-n.l==2&&(n.l+=2),n.l!==c)throw Error("Length mismatch 2: "+n.l+" !== "+c);try{a=ae(n,null)}catch(e){}for(p in a)d[p]=a[p];return d.FMTID=[i,o],d}function an(e,t,r,a,n,s){var i=t1(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,0x32363237),i.write_shift(16,eE.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var l=ar(e,r,a);if(o.push(l),n){var c=ar(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+l.length),o.push(c)}return I(o)}function as(e,t){return e.read_shift(t),null}function ai(e,t){return 1===e.read_shift(t)}function ao(e,t){return t||(t=t1(2)),t.write_shift(2,+!!e),t}function al(e){return e.read_shift(2,"u")}function ac(e,t){return t||(t=t1(2)),t.write_shift(2,e),t}function af(e,t){for(var r=[],a=e.l+t;e.l<a;)r.push(al(e,a-e.l));if(a!==e.l)throw Error("Slurp error");return r}function ah(e,t,r){return r||(r=t1(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,+("e"==t)),r}function au(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",s=c;r&&r.biff>=8&&(c=1200),r&&8!=r.biff?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return c=s,i}function ad(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function ap(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):ad(e,a,r)}function am(e,t,r){if(r.biff>5)return ap(e,t,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function ag(e,t,r){return r||(r=t1(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function av(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(N,""):""}function aT(e,t){t||(t=t1(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function ab(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function aw(e,t){var r=ab(e,t);return r[3]=0,r}function aE(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function aS(e,t,r,a){return a||(a=t1(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function ay(e){return[e.read_shift(2),rE(e)]}function a_(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function aA(e,t){return t||(t=t1(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function ax(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function ak(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function aC(e){e.l+=2,e.l+=e.read_shift(2)}var aO={0:aC,4:aC,5:aC,6:aC,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:aC,9:aC,10:aC,11:aC,12:aC,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:aC,15:aC,16:aC,17:aC,18:aC,19:aC,20:aC,21:ak};function aR(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function aI(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw Error("unsupported BIFF version")}var s=t1(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function aN(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function aD(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),l=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:l}}}function aP(e,t,r,a){var n=r&&5==r.biff;a||(a=t1(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function aM(e,t,r){var a,n=aE(e,6);(2==r.biff||9==t)&&++e.l;var s=(a=e.read_shift(1),1===e.read_shift(1)?a:1===a);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}var aL=function(e,t,r){return 0===t?"":am(e,t,r)};function aF(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=au(e,t,r),s=e.read_shift(2);if(s!==(a-=e.l))throw Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var aU=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function aB(e,t,r){var a,n,s,i,o,l,c,f=e.l+t,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(r&&2==r.biff?1:2),m=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),m=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var g=ad(e,d,r);32&h&&(g=aU[g.charCodeAt(0)]);var v=f-e.l;return r&&2==r.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(a=e,n=v,s=r,i=p,l=a.l+n,c=n$(a,i,s),l!==a.l&&(o=nK(a,l-a.l,c,s)),[c,o]):[]}}function aH(e,t,r){if(r.biff<8){var a,n,s,i;return a=e,n=t,s=r,3==a[a.l+1]&&a[a.l]++,3==(i=au(a,n,s)).charCodeAt(0)?i.slice(1):i}for(var o=[],l=e.l+t,c=e.read_shift(r.biff>8?4:2);0!=c--;)o.push(function(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}(e,r.biff,r));if(e.l!=l)throw Error("Bad ExternSheet: "+e.l+" != "+l);return o}function aW(e,t,r){var a=ax(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var l=n$(e,o,r);return t!==o+i&&(n=nK(e,t-o-i,l,r)),e.l=s,[l,n]}(e,t,r,a)]}var aj={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function aG(e,t,r){if(!r.cellStyles)return void(e.l+=t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),l=e.read_shift(2);2==a&&(e.l+=2);var c={s:n,e:s,w:i,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(c.level=l>>8&7),c}var aV=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=eA({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var s=rf(function(t,r){var a=[],s=x(1);switch(r.type){case"base64":s=C(y(t));break;case"binary":s=C(t);break;case"buffer":case"array":s=t}tZ(s,0);var i=s.read_shift(1),o=!!(136&i),l=!1,c=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:l=!0,o=!0;break;case 140:c=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),c&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-264*!!l),v=c?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=n.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2!=i&&!c&&(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=c?13:14),m.type){case"B":(!l||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,b=0;for(b=0,a[0]=[];b!=p.length;++b)a[0][b]=p[b].name;for(;f-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,a[++T]=[],b=0,b=0;b!=p.length;++b){var w=s.slice(s.l,s.l+p[b].len);s.l+=p[b].len,tZ(w,0);var E=n.utils.decode(d,w);switch(p[b].type){case"C":E.trim().length&&(a[T][b]=E.replace(/\s+$/,""));break;case"D":8===E.length?a[T][b]=new Date(+E.slice(0,4),E.slice(4,6)-1,+E.slice(6,8)):a[T][b]=E;break;case"F":a[T][b]=parseFloat(E.trim());break;case"+":case"I":a[T][b]=c?0x80000000^w.read_shift(-4,"i"):w.read_shift(4,"i");break;case"L":switch(E.trim().toUpperCase()){case"Y":case"T":a[T][b]=!0;break;case"N":case"F":a[T][b]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+E+"|")}break;case"M":if(!o)throw Error("DBF Unexpected MEMO for type "+i.toString(16));a[T][b]="##MEMO##"+(c?parseInt(E.trim(),10):w.read_shift(4));break;case"N":(E=E.replace(/\u0000/g,"").trim())&&"."!=E&&(a[T][b]=+E||0);break;case"@":a[T][b]=new Date(w.read_shift(-8,"f")-621356832e5);break;case"T":a[T][b]=new Date((w.read_shift(4)-2440588)*864e5+w.read_shift(4));break;case"Y":a[T][b]=w.read_shift(4,"i")/1e4+w.read_shift(4,"i")/1e4*0x100000000;break;case"O":a[T][b]=-w.read_shift(-8,"f");break;case"B":if(l&&8==p[b].len){a[T][b]=w.read_shift(8,"f");break}case"G":case"P":w.l+=p[b].len;break;case"0":if("_NullFlags"===p[b].name)break;default:throw Error("DBF Unsupported data type "+p[b].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return s["!cols"]=a.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return rl(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&p(+n.codepage),"string"==n.type)throw Error("Cannot write DBF to JS string");var s=t0(),i=s5(e,{header:1,raw:!0,cellDates:!0}),o=i[0],l=i.slice(1),c=e["!cols"]||[],h=0,u=0,d=0,m=1;for(h=0;h<o.length;++h){if(((c[h]||{}).DBF||{}).name){o[h]=c[h].DBF.name,++d;continue}if(null!=o[h]){if(++d,"number"==typeof o[h]&&(o[h]=o[h].toString(10)),"string"!=typeof o[h])throw Error("DBF Invalid column name "+o[h]+" |"+typeof o[h]+"|");if(o.indexOf(o[h])!==h){for(u=0;u<1024;++u)if(-1==o.indexOf(o[h]+"_"+u)){o[h]+="_"+u;break}}}}var g=rs(e["!ref"]),v=[],T=[],b=[];for(h=0;h<=g.e.c-g.s.c;++h){var w="",E="",S=0,y=[];for(u=0;u<l.length;++u)null!=l[u][h]&&y.push(l[u][h]);if(0==y.length||null==o[h]){v[h]="?";continue}for(u=0;u<y.length;++u){switch(typeof y[u]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=y[u]instanceof Date?"D":"C"}S=Math.max(S,String(y[u]).length),w=w&&w!=E?"C":E}S>250&&(S=250),"C"==(E=((c[h]||{}).DBF||{}).type)&&c[h].DBF.len>S&&(S=c[h].DBF.len),"B"==w&&"N"==E&&(w="N",b[h]=c[h].DBF.dec,S=c[h].DBF.len),T[h]="C"==w||"N"==E?S:a[w]||0,m+=T[h],v[h]=w}var _=s.next(32);for(_.write_shift(4,0x13021130),_.write_shift(4,l.length),_.write_shift(2,296+32*d),_.write_shift(2,m),h=0;h<4;++h)_.write_shift(4,0);for(_.write_shift(4,(+t[f]||3)<<8),h=0,u=0;h<o.length;++h)if(null!=o[h]){var A=s.next(32),x=(o[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);A.write_shift(1,x,"sbcs"),A.write_shift(1,"?"==v[h]?"C":v[h],"sbcs"),A.write_shift(4,u),A.write_shift(1,T[h]||a[v[h]]||0),A.write_shift(1,b[h]||0),A.write_shift(1,2),A.write_shift(4,0),A.write_shift(1,0),A.write_shift(4,0),A.write_shift(4,0),u+=T[h]||a[v[h]]||0}var k=s.next(264);for(k.write_shift(4,13),h=0;h<65;++h)k.write_shift(4,0);for(h=0;h<l.length;++h){var C=s.next(m);for(C.write_shift(1,0),u=0;u<o.length;++u)if(null!=o[u])switch(v[u]){case"L":C.write_shift(1,null==l[h][u]?63:l[h][u]?84:70);break;case"B":C.write_shift(8,l[h][u]||0,"f");break;case"N":var O="0";for("number"==typeof l[h][u]&&(O=l[h][u].toFixed(b[u]||0)),d=0;d<T[u]-O.length;++d)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":l[h][u]?(C.write_shift(4,("0000"+l[h][u].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(l[h][u].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+l[h][u].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=l[h][u]?l[h][u]:"").slice(0,T[u]);for(C.write_shift(1,R,"sbcs"),d=0;d<T[u]-R.length;++d)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),az=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+ey(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?w(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:w(a)};function s(e,s){var i,o=e.split(/[\n\r]+/),l=-1,c=-1,f=0,h=0,u=[],d=[],m=null,g={},v=[],T=[],b=[],w=0;for(+s.codepage>=0&&p(+s.codepage);f!==o.length;++f){w=0;var E,S=o[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),y=S.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),_=y[0];if(S.length>0)switch(_){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==y[1].charAt(0)&&d.push(S.slice(3).replace(/;;/g,";"));break;case"C":var A=!1,x=!1,k=!1,C=!1,O=-1,R=-1;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"A":case"G":break;case"X":c=parseInt(y[h].slice(1))-1,x=!0;break;case"Y":for(l=parseInt(y[h].slice(1))-1,x||(c=0),i=u.length;i<=l;++i)u[i]=[];break;case"K":'"'===(E=y[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(eH(E))?isNaN(ej(E).getDate())||(E=eL(E)):(E=eH(E),null!==m&&eu(m)&&(E=eN(E))),void 0!==n&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=n.utils.decode(s.codepage,E)),A=!0;break;case"E":C=!0;var I=nC(y[h].slice(1),{r:l,c:c});u[l][c]=[u[l][c],I];break;case"S":k=!0,u[l][c]=[u[l][c],"S5S"];break;case"R":O=parseInt(y[h].slice(1))-1;break;case"C":R=parseInt(y[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}if(A&&(u[l][c]&&2==u[l][c].length?u[l][c][0]=E:u[l][c]=E,m=null),k){if(C)throw Error("SYLK shared formula cannot have own formula");var N=O>-1&&u[O][R];if(!N||!N[1])throw Error("SYLK shared formula cannot find base");u[l][c][1]=function(e,t){return e.replace(nO,function(e,r,a,n,s,i){return r+("$"==a?a+n:re(t9(n)+t.c))+("$"==s?s+i:t7(t8(i)+t.r))})}(N[1],{r:l-O,c:c-R})}break;case"F":var D=0;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"X":c=parseInt(y[h].slice(1))-1,++D;break;case"Y":for(l=parseInt(y[h].slice(1))-1,i=u.length;i<=l;++i)u[i]=[];break;case"M":w=parseInt(y[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":m=d[parseInt(y[h].slice(1))];break;case"W":for(i=parseInt((b=y[h].slice(1).split(" "))[0],10);i<=parseInt(b[1],10);++i)w=parseInt(b[2],10),T[i-1]=0===w?{hidden:!0}:{wch:w},ns(T[i-1]);break;case"C":T[c=parseInt(y[h].slice(1))-1]||(T[c]={});break;case"R":v[l=parseInt(y[h].slice(1))-1]||(v[l]={}),w>0?(v[l].hpt=w,v[l].hpx=no(w)):0===w&&(v[l].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}D<1&&(m=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}}return v.length>0&&(g["!rows"]=v),T.length>0&&(g["!cols"]=T),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,g]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return s(y(e),t);case"binary":return s(e,t);case"buffer":return s(_&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return s(eF(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),a=r[0],n=r[1],i=rf(a,t);return ey(n).forEach(function(e){i[e]=n[e]}),i}return e["|"]=254,{to_workbook:function(e,t){return rl(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,a=["ID;PWXL;N;E"],n=[],s=rs(e["!ref"]),i=Array.isArray(e);a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&e["!cols"].forEach(function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=nt(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=nr(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}),e["!rows"]&&e["!rows"].forEach(function(e,t){var r="F;";e.hidden?r+="M0;":e.hpt?r+="M"+20*e.hpt+";":e.hpx&&(r+="M"+20*ni(e.hpx)+";"),r.length>2&&a.push(r+"R"+(t+1))}),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var o=s.s.r;o<=s.e.r;++o)for(var l=s.s.c;l<=s.e.c;++l){var c=rr({r:o,c:l});(r=i?(e[o]||[])[l]:e[c])&&(null!=r.v||r.f&&!r.F)&&n.push(function(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+nR(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}(r,0,o,l,t))}return a.join("\r\n")+"\r\n"+n.join("\r\n")+"\r\nE\r\n"}}}(),aK=function(){var e,t;function r(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){if("BOT"===r[s].trim()){i[++a]=[],n=0;continue}if(!(a<0)){for(var o=r[s].trim().split(","),l=o[0],c=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+l){case -1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(eH(c))?isNaN(ej(c).getDate())?i[a][n]=c:i[a][n]=eL(c):i[a][n]=eH(c),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function a(e,t){return rf(function(e,t){switch(t.type){case"base64":return r(y(e),t);case"binary":return r(e,t);case"buffer":return r(_&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return r(eF(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return rl(a(e,t),t)},to_sheet:a,from_sheet:(e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)},function(r){var a,n=[],s=rs(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(n,-1,0,"BOT");for(var l=s.s.c;l<=s.e.c;++l){var c=rr({r:o,c:l});if(!(a=i?(r[o]||[])[l]:r[c])){t(n,1,0,"");continue}switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?!a.f||a.F?t(n,1,0,""):t(n,1,0,"="+a.f):t(n,0,f,"V");break;case"b":t(n,0,+!!a.v,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=em(a.z||j[14],eC(eL(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}}}return t(n,-1,0,"EOD"),n.join("\r\n")})}}(),a$=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return rf(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var l=rt(o[1]);if(i.length<=l.r)for(a=i.length;a<=l.r;++a)i[a]||(i[a]=[]);switch(a=l.r,n=l.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],c])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return rl(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=ra(t["!ref"]),o=Array.isArray(t),l=i.s.r;l<=i.e.r;++l)for(var c=i.s.c;c<=i.e.c;++c)if(s=rr({r:l,c:c}),(r=o?(t[l]||[])[c]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=eC(eL(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||em(r.z||j[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),aY=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(eH(e))?isNaN(ej(e).getDate())?t[r][a]=e:t[r][a]=eL(e):t[r][a]=eH(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[i.pop()[1]]||44}function s(t,r){var s,i="",o="string"==r.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=y(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(t,r);switch(r.type){case"base64":i=y(t);break;case"binary":case"string":i=t;break;case"buffer":i=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==n?n.utils.decode(r.codepage,t):_&&Buffer.isBuffer(t)?t.toString("binary"):R(t);break;case"array":i=eF(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==o[0]&&187==o[1]&&191==o[2]?i=tn(i.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?i=tn(i):"binary"==r.type&&void 0!==n&&r.codepage&&(i=n.utils.decode(r.codepage,n.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?a$.to_sheet("string"==r.type?i:tn(i),r):(s=i,!(r&&r.PRN)||r.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,t){var r,n,s=t||{},i="",o=s.dense?[]:{},l={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(i=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(i=e.charAt(4),e=e.slice(6)):i=a(e.slice(0,1024)):i=s&&s.FS?s.FS:a(e.slice(0,1024));var c=0,f=0,h=0,u=0,d=0,p=i.charCodeAt(0),m=!1,g=0,v=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var T=null!=s.dateNF?RegExp("^"+("number"==typeof(r=s.dateNF)?j[r]:r).replace(eb,"(\\d+)")+"$"):null;function b(){var t=e.slice(u,d),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(s.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(h=eH(t)))if(!isNaN(ej(t).getDate())||T&&t.match(T)){r.z=s.dateNF||j[14];var a,n,i,m,b,w,E,S,y,_,A=0;T&&t.match(T)&&(a=s.dateNF,n=t.match(T)||[],i=-1,m=-1,b=-1,w=-1,E=-1,S=-1,(a.match(eb)||[]).forEach(function(e,t){var r=parseInt(n[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":i=r;break;case"d":b=r;break;case"h":w=r;break;case"s":S=r;break;case"m":w>=0?E=r:m=r}}),S>=0&&-1==E&&m>=0&&(E=m,m=-1),7==(y=(""+(i>=0?i:new Date().getFullYear())).slice(-4)+"-"+("00"+(m>=1?m:1)).slice(-2)+"-"+("00"+(b>=1?b:1)).slice(-2)).length&&(y="0"+y),8==y.length&&(y="20"+y),_=("00"+(w>=0?w:0)).slice(-2)+":"+("00"+(E>=0?E:0)).slice(-2)+":"+("00"+(S>=0?S:0)).slice(-2),t=-1==w&&-1==E&&-1==S?y:-1==i&&-1==m&&-1==b?_:y+"T"+_,A=1),s.cellDates?(r.t="d",r.v=eL(t,A)):(r.t="n",r.v=eC(eL(t,A))),!1!==s.cellText&&(r.w=em(r.z,r.v instanceof Date?eC(r.v):r.v)),s.cellNF||delete r.z}else r.t="s",r.v=t;else r.t="n",!1!==s.cellText&&(r.w=t),r.v=h;if("z"==r.t||(s.dense?(o[c]||(o[c]=[]),o[c][f]=r):o[rr({c:f,r:c})]=r),u=d+1,v=e.charCodeAt(u),l.e.c<f&&(l.e.c=f),l.e.r<c&&(l.e.r=c),g==p)++f;else if(f=0,++c,s.sheetRows&&s.sheetRows<=c)return!0}e:for(;d<e.length;++d)switch(g=e.charCodeAt(d)){case 34:34===v&&(m=!m);break;case p:case 10:case 13:if(!m&&b())break e}return d-u>0&&b(),o["!ref"]=rn(l),o}(s,r):rf(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,l=0,c=0;c<=i;++c)-1==(l=s[c].indexOf(" "))?l=s[c].length:l++,o=Math.max(o,l);for(c=0;c<=i;++c){n[c]=[];var f=0;for(e(s[c].slice(0,o).trim(),n,c,f,a),f=1;f<=(s[c].length-o)/10+1;++f)e(s[c].slice(o+(f-1)*10,o+10*f).trim(),n,c,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(s,r),r))}return{to_workbook:function(e,t){return rl(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],a=rs(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var l=rr({r:s,c:o});if(!(t=n?(e[s]||[])[o]:e[l])||null==t.v){i.push("          ");continue}for(var c=(t.w||(ro(t),t.w)||"").slice(0,10);c.length<10;)c+=" ";i.push(c+(0===o?" ":""))}r.push(i.join(""))}return r.join("\n")}}}(),aX=function(){function e(e,t,r){if(e){tZ(e,e.l||0);for(var a=r.Enum||h;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,l=s.f&&s.f(e,i,r);if(e.l=o,t(l,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",o=0,l={},c=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=h,e(t,function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||j[14],a.cellDates&&(e[1].t="d",e[1].v=eN(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=rn(d),l[s]=n,c.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[rr(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rr(e[0])]=e[1]}},a);else if(26==t[2]||14==t[2])a.Enum=u,14==t[2]&&(a.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=rn(d),l[s]=n,c.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((o=e[3])+1)),p>0&&e[0].r>=p)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rr(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1])}},a);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(n["!ref"]=rn(d),l[i||s]=n,c.push(i||s),!f.length)return{SheetNames:c,Sheets:l};for(var m={},g=[],v=0;v<f.length;++v)l[c[v]]?(g.push(f[v]||c[v]),m[f[v]]=l[f[v]]||l[c[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,t,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=32768&t;return t&=-32769,t=(a?e:0)+(t>=8192?t-16384:t),(a?"":"$")+(r?re(t):t7(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function o(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function l(e,t){var r=o(e,t),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&0xc0000000===n?(r[1].t="e",r[1].v=15):0===a&&0xd0000000===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function c(e,t){var r=o(e,t),a=e.read_shift(8,"f");return r[1].v=a,r}function f(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:al},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2)):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0)),a}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var o=e.l+t,l=r(e,t,a);if(l[1].v=e.read_shift(8,"f"),a.qpro)e.l=o;else{var c=e.read_shift(2);(function(e,t){tZ(e,0);for(var r=[],a=0,o="",l="",c="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:l=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(l+o);break;case 2:var u=n(t[0].c,e.read_shift(2),!0),d=n(t[0].r,e.read_shift(2),!1);l=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+l+o);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:f=r.pop(),c=r.pop(),r.push(["AND","OR"][h-20]+"("+c+","+f+")");break;default:if(h<32&&i[h])f=r.pop(),c=r.pop(),r.push(c+i[h]+f);else if(s[h]){if(69==(a=s[h][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+c),l),e.l+=c}return l}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=o(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:l},24:{n:"NUMBER18",f:function(e,t){var r=o(e,t);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=l(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=o(e,t),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:c},40:{n:"FORMULA28",f:function(e,t){var r=c(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,a,n,s,i=t||{};if(+i.codepage>=0&&p(+i.codepage),"string"==i.type)throw Error("Cannot write WK1 to JS string");var o=t0(),l=rs(e["!ref"]),c=Array.isArray(e),f=[];sE(o,0,(r=1030,(a=t1(2)).write_shift(2,1030),a)),sE(o,6,(n=l,(s=t1(8)).write_shift(2,n.s.c),s.write_shift(2,n.s.r),s.write_shift(2,n.e.c),s.write_shift(2,n.e.r),s));for(var h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=t7(u),m=l.s.c;m<=l.e.c;++m){u===l.s.r&&(f[m]=re(m));var g=f[m]+d,v=c?(e[u]||[])[m]:e[g];v&&"z"!=v.t&&("n"==v.t?(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?sE(o,13,function(e,t,r){var a=t1(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}(u,m,v.v)):sE(o,14,function(e,t,r){var a=t1(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}(u,m,v.v)):sE(o,15,function(e,t,r){var a=t1(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,m,ro(v).slice(0,239))))}return sE(o,1),o.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&p(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var a=t0();sE(a,0,function(e){var t=t1(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var l=ra(o["!ref"]);r<l.e.r&&(r=l.e.r),a<l.e.c&&(a=l.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&sE(a,27,function(e,t){var r=t1(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(o&&o["!ref"]){for(var l=rs(o["!ref"]),c=Array.isArray(o),f=[],h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=t7(u),m=l.s.c;m<=l.e.c;++m){u===l.s.r&&(f[m]=re(m));var g=f[m]+d,v=c?(o[u]||[])[m]:o[g];v&&"z"!=v.t&&("n"==v.t?sE(a,23,function(e,t,r,a){var n=t1(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,o=0,l=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),(0x80000000&(l=a>>>0))==0&&(a/=2,++i,l=a>>>0),a-=l,l|=0x80000000,l>>>=0,a*=0x100000000,o=a>>>0,n.write_shift(4,o),n.write_shift(4,l),i+=16383+32768*!!s,n.write_shift(2,i),n}(u,m,i,v.v)):sE(a,22,function(e,t,r,a){var n=t1(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,m,i,ro(v).slice(0,239))))}++i}}return sE(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(C(y(e)),r);case"binary":return t(C(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),aJ=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,aq=/<(?:\w+:)?r>/,aZ=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g,aQ=/^\s|\s$|[\t\n\r]/;function a1(e,t){if(!t.bookSST)return"";var r=[eJ];r[r.length]=td("sst",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main",count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(aQ)&&(s+=' xml:space="preserve"'),s+=">"+e5(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var a0=function(e,t){var r=!1;return null==t&&(r=!0,t=t1(15+4*e.t.length)),t.write_shift(1,0),rd(e.t,t),r?t.slice(0,t.l):t};function a2(e){if(void 0!==n)return n.utils.encode(f,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function a4(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function a3(e){var t,r,a=0,n=a2(e),s=n.length+1;for(r=1,(t=x(s))[0]=n.length;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((16384&a)!=0|a<<1&32767)^t[r];return 52811^a}var a5=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){var r;return((r=e^t)/2|128*r)&255},n=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a};return function(t){for(var r,s,i,o=a2(t),l=n(o),c=o.length,f=x(16),h=0;16!=h;++h)f[h]=0;for((1&c)==1&&(r=l>>8,f[c]=a(e[0],r),--c,r=255&l,s=o[o.length-1],f[c]=a(s,r));c>0;)--c,r=l>>8,f[c]=a(o[c],r),--c,r=255&l,f[c]=a(o[c],r);for(c=15,i=15-o.length;i>0;)r=l>>8,f[c]=a(e[i],r),--c,--i,r=255&l,f[c]=a(o[c],r),--c,--i;return f}}(),a6=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=a5(e)),s=0;s!=t.length;++s)i=((i=t[s]^a[r])>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},a8=function(e){var t=0,r=a5(e);return function(e){var a=a6("",e,t,r);return t=a[1],a[0]}},a7=function(){function e(e,r){switch(r.type){case"base64":return t(y(e),r);case"binary":return t(e,r);case"buffer":return t(_&&Buffer.isBuffer(e)?e.toString("binary"):R(e),r);case"array":return t(eF(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,o=-1;a=s.exec(e);){if("\\cell"===a[0]){var l=e.slice(i,s.lastIndex-a[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var c={v:l,t:"s"};Array.isArray(r)?r[t][o]=c:r[rr({r:t,c:o})]=c}}i=s.lastIndex}o>n.e.c&&(n.e.c=o)}),r["!ref"]=rn(n),r}return{to_workbook:function(t,r){return rl(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=rs(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=rr({r:s,c:i});(t=n?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(ro(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function a9(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var ne=6;function nt(e){return Math.floor((e+Math.round(128/ne)/256)*ne)}function nr(e){return Math.floor((e-5)/ne*100+.5)/100}function na(e){return Math.round((e*ne+5)/ne*256)/256}function nn(e){return na(nr(nt(e)))}function ns(e){e.width?(e.wpx=nt(e.width),e.wch=nr(e.wpx),e.MDW=ne):e.wpx?(e.wch=nr(e.wpx),e.width=na(e.wch),e.MDW=ne):"number"==typeof e.wch&&(e.width=na(e.wch),e.wpx=nt(e.width),e.MDW=ne),e.customWidth&&delete e.customWidth}function ni(e){return 96*e/96}function no(e){return 96*e/96}var nl={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function nc(e,t){var r,a,n,s,i,o=[eJ,td("styleSheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:vt":tv.vt})];return e.SSF&&null!=(r=e.SSF,a=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(a[a.length]=td("numFmt",null,{numFmtId:t,formatCode:e5(r[t])}))}),i=1===a.length?"":(a[a.length]="</numFmts>",a[0]=td("numFmts",null,{count:a.length-2}).replace("/>",">"),a.join("")))&&(o[o.length]=i),o[o.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',o[o.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',o[o.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',o[o.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',n=t.cellXfs,(s=[])[s.length]=td("cellXfs",null),n.forEach(function(e){s[s.length]=td("xf",null,e)}),s[s.length]="</cellXfs>",(i=2===s.length?"":(s[0]=td("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(o[o.length]=i),o[o.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',o[o.length]='<dxfs count="0"/>',o[o.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',o.length>2&&(o[o.length]="</styleSheet>",o[1]=o[1].replace("/>",">")),o.join("")}var nf=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function nh(e,t){t||(t=t1(84)),i||(i=eA(nf));var r=i[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(rk({auto:1},t),rk({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function nu(e,t,r){return r||(r=t1(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function nd(e,t){return t||(t=t1(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var np=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function nm(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(eQ)||[]).forEach(function(e){var n=e0(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[np.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+n[0]+" in clrScheme")}})}function ng(){}function nv(){}var nT=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,nb=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,nw=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,nE=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function nS(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[eJ];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ny(){var e=[eJ];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var n_=1024;function nA(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[td("xml",null,{"xmlns:v":tb.v,"xmlns:o":tb.o,"xmlns:x":tb.x,"xmlns:mv":tb.mv}).replace(/\/>/,">"),td("o:shapelayout",td("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),td("v:shapetype",[td("v:stroke",null,{joinstyle:"miter"}),td("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];n_<1e3*e;)n_+=1e3;return t.forEach(function(e){var t=rt(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?td("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=td("v:fill",a,r);++n_,n=n.concat(["<v:shape"+tu({id:"_x0000_s"+n_,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,td("v:shadow",null,{on:"t",obscured:"t"}),td("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",th("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),th("x:AutoFill","False"),th("x:Row",String(t.r)),th("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function nx(e){var t=[eJ,td("comments",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main"})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var a=e5(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(a=r.indexOf(e5(e.a))),n.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(th("t",e5(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(th("t",e5(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var nk=["xlsb","xlsm","xlam","biff8","xla"],nC=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,l=n.length>0?0|parseInt(n,10):0;return s?l+=t.c:--l,i?o+=t.r:--o,r+(s?"":"$")+re(l)+(i?"":"$")+t7(o)}return function(a,n){return t=n,a.replace(e,r)}}(),nO=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,nR=function(e,t){return e.replace(nO,function(e,r,a,n,s,i){var o=t9(n)-(a?0:t.c),l=t8(i)-(s?0:t.r);return r+"R"+(0==l?"":s?l+1:"["+l+"]")+"C"+(0==o?"":a?o+1:"["+o+"]")})};function nI(e){e.l+=1}function nN(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function nD(e,t,r){var a=2;if(r)if(r.biff>=2&&r.biff<=5)return nP(e,t,r);else 12==r.biff&&(a=4);var n=e.read_shift(a),s=e.read_shift(a),i=nN(e,2),o=nN(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function nP(e){var t=nN(e,2),r=nN(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function nM(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var a,n,s;return n=nN(a=e,2),s=a.read_shift(1),{r:n[0],c:s,cRel:n[1],rRel:n[2]}}var i=e.read_shift(r&&12==r.biff?4:2),o=nN(e,2);return{r:i,c:o[0],cRel:o[1],rRel:o[2]}}function nL(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function nF(e){return[e.read_shift(1),e.read_shift(1)]}function nU(e,t,r){var a;return e.l+=2,[{r:e.read_shift(2),c:255&(a=e.read_shift(2)),fQuoted:!!(16384&a),cRel:a>>15,rRel:a>>15}]}function nB(e){return e.l+=6,[]}function nH(e){return e.l+=2,[al(e),1&e.read_shift(2)]}var nW=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],nj={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:tQ},3:{n:"PtgAdd",f:nI},4:{n:"PtgSub",f:nI},5:{n:"PtgMul",f:nI},6:{n:"PtgDiv",f:nI},7:{n:"PtgPower",f:nI},8:{n:"PtgConcat",f:nI},9:{n:"PtgLt",f:nI},10:{n:"PtgLe",f:nI},11:{n:"PtgEq",f:nI},12:{n:"PtgGe",f:nI},13:{n:"PtgGt",f:nI},14:{n:"PtgNe",f:nI},15:{n:"PtgIsect",f:nI},16:{n:"PtgUnion",f:nI},17:{n:"PtgRange",f:nI},18:{n:"PtgUplus",f:nI},19:{n:"PtgUminus",f:nI},20:{n:"PtgPercent",f:nI},21:{n:"PtgParen",f:nI},22:{n:"PtgMissArg",f:nI},23:{n:"PtgStr",f:function(e,t,r){return e.l++,au(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rM[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,rA(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[n2[n],n0[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a,n=e[e.l++],s=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[(a=e)[a.l+1]>>7,32767&a.read_shift(2)];return[s,(0===i[0]?n0:n1)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,nM(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,nD(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:tQ},40:{n:"PtgMemNoMem",f:tQ},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,function(e,t,r){var a,n,s,i,o,l=r&&r.biff?r.biff:8;if(l>=2&&l<=5){return n=(a=e).read_shift(2),s=a.read_shift(1),i=(32768&n)>>15,o=(16384&n)>>14,n&=16383,1==i&&n>=8192&&(n-=16384),1==o&&s>=128&&(s-=256),{r:n,c:s,cRel:o,rRel:i}}var c=e.read_shift(l>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;c>524287;)c-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:c,c:f,cRel:h,rRel:u}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return nP(e,t,r);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=nN(e,2),i=nN(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var a,n,s,i;return 5==r.biff?(n=(a=e).read_shift(1)>>>5&3,s=a.read_shift(2,"i"),a.l+=8,i=a.read_shift(2),a.l+=12,[n,s,i]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,nM(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[a,n,nD(e,s,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},nG={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},nV={1:{n:"PtgElfLel",f:nH},2:{n:"PtgElfRw",f:nU},3:{n:"PtgElfCol",f:nU},6:{n:"PtgElfRwV",f:nU},7:{n:"PtgElfColV",f:nU},10:{n:"PtgElfRadical",f:nU},11:{n:"PtgElfRadicalS",f:nB},13:{n:"PtgElfColS",f:nB},15:{n:"PtgElfColSV",f:nB},16:{n:"PtgElfRadicalLel",f:nH},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=nW[r>>2&31];return{ixti:t,coltype:3&r,rt:i,idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},nz={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:nL},33:{n:"PtgAttrBaxcel",f:nL},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),nF(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),nF(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function nK(e,t,r,a){if(a.biff<8)return n=t,void(e.l+=n);for(var n,s,i=e.l+t,o=[],l=0;l!==r.length;++l)switch(r[l][0]){case"PtgArray":r[l][1]=function(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=ai(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rM[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=rA(e,8);break;case 2:r[1]=am(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}(e,0,a),o.push(r[l][1]);break;case"PtgMemArea":r[l][2]=function(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?ry:a_)(e,8));return n}(e,r[l][1],a),o.push(r[l][2]);break;case"PtgExp":a&&12==a.biff&&(r[l][1][1]=e.read_shift(4),o.push(r[l][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[l][0]}return 0!=(t=i-e.l)&&o.push((s=t,void(e.l+=s))),o}function n$(e,t,r){for(var a,n,s,i=e.l+t,o=[];i!=e.l;)(t=i-e.l,n=nj[s=e[e.l]]||nj[nG[s]],(24===s||25===s)&&(n=(24===s?nV:nz)[e[e.l+1]]),n&&n.f)?o.push([n.n,n.f(e,t,r)]):(a=t,e.l+=a);return o}var nY={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function nX(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";return n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]}}function nJ(e,t,r){var a=nX(e,t,r);return"#REF"==a?a:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function nq(e,t,r,a,n){var s,i,o,l,c=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var b=e[0][v];switch(b[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=eB(" ",e[0][m][1][1]);break;case 1:g=eB("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+nY[b[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=t4(b[1][1],f,n),h.push(t5(o,c));break;case"PtgRefN":o=r?t4(b[1][1],r,n):b[1][1],h.push(t5(o,c));break;case"PtgRef3d":u=b[1][1],o=t4(b[1][2],f,n),p=nJ(a,u,n),h.push(p+"!"+t5(o,c));break;case"PtgFunc":case"PtgFuncVar":var w=b[1][0],E=b[1][1];w||(w=0);var S=0==(w&=127)?[]:h.slice(-w);h.length-=w,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(b[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(b[1]);break;case"PtgNum":h.push(String(b[1]));break;case"PtgStr":h.push('"'+b[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=t3(b[1][1],r?{s:r}:f,n),h.push(t6(l,n));break;case"PtgArea":l=t3(b[1][1],f,n),h.push(t6(l,n));break;case"PtgArea3d":u=b[1][1],l=b[1][2],p=nJ(a,u,n),h.push(p+"!"+t6(l,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=b[1][2];var y=(a.names||[])[d-1]||(a[0]||[])[d],_=y?y.Name:"SH33TJSNAME"+String(d);_&&"_xlfn."==_.slice(0,6)&&!n.xlfn&&(_=_.slice(6)),h.push(_);break;case"PtgNameX":var A,x=b[1][1];if(d=b[1][2],n.biff<=5)x<0&&(x=-x),a[x]&&(A=a[x][d]);else{var k="";if(14849==((a[x]||[])[0]||[])[0]||(1025==((a[x]||[])[0]||[])[0]?a[x][d]&&a[x][d].itab>0&&(k=a.SheetNames[a[x][d].itab-1]+"!"):k=a.SheetNames[d-1]+"!"),a[x]&&a[x][d])k+=a[x][d].Name;else if(a[0]&&a[0][d])k+=a[0][d].Name;else{var C=(nX(a,x,n)||"").split(";;");C[d-1]?k=C[d-1]:k+="SH33TJSERRX"}h.push(k);break}A||(A={Name:"SH33TJSERRY"}),h.push(A.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:O=eB(" ",e[0][m][1][1])+O;break;case 3:O=eB("\r",e[0][m][1][1])+O;break;case 4:R=eB(" ",e[0][m][1][1])+R;break;case 5:R=eB("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:b[1][1],r:b[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[rr(o)]){var N=a.sharedf[rr(o)];h.push(nq(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if((i=a.arrayf[s],!(o.c<i[0].s.c)&&!(o.c>i[0].e.c))&&!(o.r<i[0].s.r)&&!(o.r>i[0].e.r)){h.push(nq(i[1],f,I,a,n)),D=!0;break}D||h.push(b[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}t.push(n.join(","))}return t.join(";")}(b[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+b[1].idx+"[#"+b[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(b))}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==P.indexOf(e[0][v][0])){b=e[0][m];var M=!0;switch(b[1][0]){case 4:M=!1;case 0:g=eB(" ",b[1][1]);break;case 5:M=!1;case 1:g=eB("\r",b[1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+b[1][0])}h.push((M?g:"")+h.pop()+(M?"":g)),m=-1}}if(h.length>1&&n.WTF)throw Error("bad formula stack");return h[0]}function nZ(e,t,r){var a=e.l+t,n=aE(e,6);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==tj(e,e.l+6))return[rA(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var l=n$(e,o,r);return t!==o+i&&(n=nK(e,t-o-i,l,r)),e.l=s,[l,n]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function nQ(e,t,r){var a=e.read_shift(4),n=n$(e,a,r),s=e.read_shift(4),i=s>0?nK(e,s,n,r):null;return[n,i]}var n1={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},n0={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},n2={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function n4(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function n3(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var n5={},n6="undefined"!=typeof Map;function n8(e,t,r){var a=0,n=e.length;if(r){if(n6?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=n6?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(n6?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function n7(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(ne=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=nr(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=na(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function n9(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function se(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf){for(;n<392;++n)if(null==r.ssf[n]){eg(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}var st=["objects","scenarios","selectLockedCells","selectUnlockedCells"],sr=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function sa(e,t,r,a){var n,s=[eJ,td("worksheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tv.r})],i=r.SheetNames[e],o=0,l="",c=r.Sheets[i];null==c&&(c={});var f=c["!ref"]||"A1",h=rs(f);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+f+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),f=rn(h)}a||(a={}),c["!comments"]=[];var u=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(e){}s=!0,i.codeName=ts(e5(l))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};e["!outline"].above&&(c.summaryBelow=0),e["!outline"].left&&(c.summaryRight=0),o=(o||"")+td("outlinePr",null,c)}(s||o)&&(n[n.length]=td("sheetPr",o,i))}(c,r,e,t,s),s[s.length]=td("dimension",null,{ref:f}),s[s.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),td("sheetViews",td("sheetView",null,d),{})),t.sheetFormat&&(s[s.length]=td("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=td("col",null,n7(n,r)));return a[a.length]="</cols>",a.join("")}(0,c["!cols"])),s[o=s.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(l=function(e,t,r,a){var n,s,i=[],o=[],l=rs(e["!ref"]),c="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:f},v=-1;for(d=l.s.c;d<=l.e.c;++d)h[d]=re(d);for(u=l.s.r;u<=l.e.r;++u){for(o=[],f=t7(u),d=l.s.c;d<=l.e.c;++d){n=h[d]+f;var T=m?(e[u]||[])[d]:e[n];void 0!==T&&null!=(c=function(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=rM[e.v];break;case"d":a&&a.cellDates?n=eL(e.v,-1).toISOString():((e=eU(e)).t="n",n=""+(e.v=eC(eL(e.v)))),void 0===e.z&&(e.z=j[14]);break;default:n=e.v}var o=th("v",e5(n)),l={r:t},c=se(a.cellXfs,e,a);switch(0!==c&&(l.s=c),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=th("v",""+n8(a.Strings,e.v,a.revStrings)),l.t="s";break}l.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=td("f",e5(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),td("c",o,l)}(T,n,e,t,r,a))&&o.push(c)}(o.length>0||p&&p[u])&&(g={r:f},p&&p[u]&&((s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=ni(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level)),i[i.length]=td("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=ni(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level),i[i.length]=td("row","",g));return i.join("")}(c,t,e,r,a)).length>0&&(s[s.length]=l),s.length>o+1&&(s[s.length]="</sheetData>",s[o]=s[o].replace("/>",">")),c["!protect"]&&(s[s.length]=(p=c["!protect"],m={sheet:1},st.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),sr.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=a3(p.password).toString(16).toUpperCase()),td("sheetProtection",null,m))),null!=c["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:rn(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=ra(n);i.s.r==i.e.r&&(i.e.r=ra(t["!ref"]).e.r,n=rn(i));for(var o=0;o<s.length;++o){var l=s[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),td("autoFilter",null,{ref:n})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+rn(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var d,p,m,g,v=-1,T=-1;return c["!links"].length>0&&(s[s.length]="<hyperlinks>",c["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(T=rV(a,-1,e5(e[1].Target).replace(/#.*$/,""),rW.HLINK),g["r:id"]="rId"+T),(v=e[1].Target.indexOf("#"))>-1&&(g.location=e5(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=e5(e[1].Tooltip)),s[s.length]=td("hyperlink",null,g))}),s[s.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(s[s.length]=(n9(n=c["!margins"]),td("pageMargins",null,n))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(s[s.length]=th("ignoredErrors",td("ignoredError",null,{numberStoredAsText:1,sqref:f}))),u.length>0&&(T=rV(a,-1,"../drawings/drawing"+(e+1)+".xml",rW.DRAW),s[s.length]=td("drawing",null,{"r:id":"rId"+T}),c["!drawing"]=u),c["!comments"].length>0&&(T=rV(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rW.VML),s[s.length]=td("legacyDrawing",null,{"r:id":"rId"+T}),c["!legacy"]=T),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}var sn=["left","right","top","bottom","header","footer"],ss=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function si(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=e9(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function so(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=e9(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}var sl="][*?/\\".split("");function sc(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return sl.forEach(function(a){if(-1!=e.indexOf(a)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function sf(e){var t=[eJ];t[t.length]=td("workbook",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tv.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(ss.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=td("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(s=0,t[t.length]="<bookViews>";s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(s=0,t[t.length]="<sheets>";s!=e.SheetNames.length;++s){var i={name:e5(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=td("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=td("definedName",e5(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}var sh=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,su=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function sd(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o,l=e.match(sh);if(l)for(o=0;o!=l.length;++o)-1===(s=(n=l[o].match(su))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function sp(e,t){var r,a,i,l=t||{};eT();var c=T(tm(e));("binary"==l.type||"array"==l.type||"base64"==l.type)&&(c=void 0!==n?n.utils.decode(65001,g(c)):tn(c));var f=c.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=eU(l);return u.type="string",aY.to_workbook(c,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h){var d=c,p=l,m=d.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!m||0==m.length)throw Error("Invalid HTML: could not find <table>");if(1==m.length)return rl(s_(m[0],p),p);var v=it();return m.forEach(function(e,t){ir(v,s_(e,p),"Sheet"+(t+1))}),v}o={"General Number":"General","General Date":j[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":j[15],"Short Date":j[14],"Long Time":j[19],"Medium Time":j[18],"Short Time":j[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:j[2],Standard:j[4],Percent:j[10],Scientific:j[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var b,w,E,S=[],y={},_=[],A=l.dense?[]:{},x="",k={},C={},O=sd('<Data ss:Type="String">'),R=0,I=0,N=0,D={s:{r:2e6,c:2e6},e:{r:0,c:0}},P={},M={},L="",F=0,U=[],B={},H={},W=0,G=[],V=[],z={},Y=[],X=!1,J=[],q=[],Z={},et=0,er=0,ea={Sheets:[],WBProps:{date1904:!1}},en={};tg.lastIndex=0,c=c.replace(/<!--([\s\S]*?)-->/mg,"");for(var es="";b=tg.exec(c);)switch(b[3]=(es=b[3]).toLowerCase()){case"data":if("data"==es){if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"))}else"/"!==b[0].charAt(b[0].length-2)&&S.push([b[3],!0]);break}if(S[S.length-1][1])break;"/"===b[1]?function(e,t,r,a,n,s,i,l,c,f){var h="General",u=a.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&l&&(u=l.StyleID),void 0===u&&i&&(u=i.StyleID);void 0!==s[u]&&(s[u].nf&&(h=s[u].nf),s[u].Interior&&p.push(s[u].Interior),s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=e9(e);break;case"String":a.t="s",a.r=(null)((null)(e)),a.v=e.indexOf("<")>-1?(null)(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(eL(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=(null)(e):a.v<60&&(a.v=a.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rL[e],!1!==f.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=(null)(t||e))}if(!function(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{if("e"===e.t)e.w=e.w||rM[e.v];else if("General"===t)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v);else{var a,n,s;a=t||"General",n=e.v,s=o[a]||(null)(a),e.w="General"===s?ee(n):em(s,n)}}catch(e){if(r.WTF)throw e}try{var i=o[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&eu(i)){var l=K(e.v);l&&(e.t="d",e.v=new Date(l.y,l.m-1,l.d,l.H,l.M,l.S,l.u))}}catch(e){if(r.WTF)throw e}}}(a,h,f),!1!==f.cellFormula)if(a.Formula){var g=(null)(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=nC(g,n),delete a.Formula,"RC"==a.ArrayRange?a.F=nC("RC:RC",n):a.ArrayRange&&(a.F=nC(a.ArrayRange,n),c.push([rs(a.F),a.F]))}else for(m=0;m<c.length;++m)n.r>=c[m][0].s.r&&n.r<=c[m][0].e.r&&n.c>=c[m][0].s.c&&n.c<=c[m][0].e.c&&(a.F=c[m][1]);f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}(c.slice(R,b.index),L,O,"comment"==S[S.length-1][0]?z:k,{c:I,r:N},P,Y[I],C,J,l):(L="",O=sd(b[0]),R=b.index+b[0].length);break;case"cell":if("/"===b[1])if(V.length>0&&(k.c=V),(!l.sheetRows||l.sheetRows>N)&&void 0!==k.v&&(l.dense?(A[N]||(A[N]=[]),A[N][I]=k):A[re(I)+t7(N)]=k),k.HRef&&(k.l={Target:(null)(k.HRef)},k.HRefScreenTip&&(k.l.Tooltip=k.HRefScreenTip),delete k.HRef,delete k.HRefScreenTip),(k.MergeAcross||k.MergeDown)&&(et=I+(0|parseInt(k.MergeAcross,10)),er=N+(0|parseInt(k.MergeDown,10)),U.push({s:{c:I,r:N},e:{c:et,r:er}})),l.sheetStubs)if(k.MergeAcross||k.MergeDown){for(var ei=I;ei<=et;++ei)for(var eo=N;eo<=er;++eo)(ei>I||eo>N)&&(l.dense?(A[eo]||(A[eo]=[]),A[eo][ei]={t:"z"}):A[re(ei)+t7(eo)]={t:"z"});I=et+1}else++I;else k.MergeAcross?I=et+1:++I;else(k=function(e){var t=e.split(/\s+/),r={};if(1===t.length)return r;var a,n,s,i,o=e.match(sh);if(o)for(i=0;i!=o.length;++i)-1===(n=(a=o[i].match(su))[1].indexOf(":"))?r[a[1]]=a[2].slice(1,a[2].length-1):r["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1)]=a[2].slice(1,a[2].length-1);return r}(b[0])).Index&&(I=k.Index-1),I<D.s.c&&(D.s.c=I),I>D.e.c&&(D.e.c=I),"/>"===b[0].slice(-2)&&++I,V=[];break;case"row":"/"===b[1]||"/>"===b[0].slice(-2)?(N<D.s.r&&(D.s.r=N),N>D.e.r&&(D.e.r=N),"/>"===b[0].slice(-2)&&(C=sd(b[0])).Index&&(N=C.Index-1),I=0,++N):((C=sd(b[0])).Index&&(N=C.Index-1),Z={},("0"==C.AutoFitHeight||C.Height)&&(Z.hpx=parseInt(C.Height,10),Z.hpt=ni(Z.hpx),q[N]=Z),"1"==C.Hidden&&(Z.hidden=!0,q[N]=Z));break;case"worksheet":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"));_.push(x),D.s.r<=D.e.r&&D.s.c<=D.e.c&&(A["!ref"]=rn(D),l.sheetRows&&l.sheetRows<=D.e.r&&(A["!fullref"]=A["!ref"],D.e.r=l.sheetRows-1,A["!ref"]=rn(D))),U.length&&(A["!merges"]=U),Y.length>0&&(A["!cols"]=Y),q.length>0&&(A["!rows"]=q),y[x]=A}else D={s:{r:2e6,c:2e6},e:{r:0,c:0}},N=I=0,S.push([b[3],!1]),x=(null)((w=sd(b[0])).Name),A=l.dense?[]:{},U=[],J=[],q=[],en={name:x,Hidden:0},ea.Sheets.push(en);break;case"table":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"))}else"/>"==b[0].slice(-2)||(S.push([b[3],!1]),Y=[],X=!1);break;case"style":if("/"===b[1]){var el=M;if(l.cellStyles&&el.Interior){var ec=el.Interior;ec.Pattern&&(ec.patternType=nl[ec.Pattern]||ec.Pattern)}P[el.ID]=el}else M=sd(b[0]);break;case"numberformat":M.nf=(null)(sd(b[0]).Format||"General"),o[M.nf]&&(M.nf=o[M.nf]);for(var ef=0;392!=ef&&j[ef]!=M.nf;++ef);if(392==ef){for(ef=57;392!=ef;++ef)if(null==j[ef]){eg(M.nf,ef);break}}break;case"column":if("table"!==S[S.length-1][0])break;if((E=sd(b[0])).Hidden&&(E.hidden=!0,delete E.Hidden),E.Width&&(E.wpx=parseInt(E.Width,10)),!X&&E.wpx>10){X=!0,ne=6;for(var eh=0;eh<Y.length;++eh)Y[eh]&&ns(Y[eh])}X&&ns(E),Y[E.Index-1||Y.length]=E;for(var ed=0;ed<+E.Span;++ed)Y[Y.length]=eU(E);break;case"namedrange":if("/"===b[1])break;ea.Names||(ea.Names=[]);var ep=e0(b[0]),ev={Name:ep.Name,Ref:nC(ep.RefersTo.slice(1),{r:0,c:0})};ea.Sheets.length>0&&(ev.Sheet=ea.Sheets.length-1),ea.Names.push(ev);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===b[0].slice(-2)||("/"===b[1]?L+=c.slice(F,b.index):F=b.index+b[0].length);break;case"interior":if(!l.cellStyles)break;M.Interior=sd(b[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===b[0].slice(-2)||("/"===b[1]?(r=es,a=c.slice(W,b.index),s||(s=eA(r1)),B[r=s[r]||r]=a):W=b.index+b[0].length);break;case"styles":case"workbook":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"))}else S.push([b[3],!1]);break;case"comment":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"));(i=z).t=i.v||"",i.t=i.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),i.v=i.w=i.ixfe=void 0,V.push(z)}else S.push([b[3],!1]),z={a:(w=sd(b[0])).Author};break;case"autofilter":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"))}else if("/"!==b[0].charAt(b[0].length-2)){var eb=sd(b[0]);A["!autofilter"]={ref:nC(eb.Range).replace(/\$/g,"")},S.push([b[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===b[1]){if((w=S.pop())[0]!==b[3])throw Error("Bad state: "+w.join("|"))}else"/"!==b[0].charAt(b[0].length-2)&&S.push([b[3],!0]);break;default:if(0==S.length&&"document"==b[3]||0==S.length&&"uof"==b[3])return sR(c,l);var ew=!0;switch(S[S.length-1][0]){case"officedocumentsettings":switch(b[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ew=!1}break;case"componentoptions":switch(b[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ew=!1}break;case"excelworkbook":switch(b[3]){case"date1904":ea.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ew=!1}break;case"workbookoptions":switch(b[3]){case"owcversion":case"height":case"width":break;default:ew=!1}break;case"worksheetoptions":switch(b[3]){case"visible":if("/>"===b[0].slice(-2));else if("/"===b[1])switch(c.slice(W,b.index)){case"SheetHidden":en.Hidden=1;break;case"SheetVeryHidden":en.Hidden=2}else W=b.index+b[0].length;break;case"header":A["!margins"]||n9(A["!margins"]={},"xlml"),isNaN(+e0(b[0]).Margin)||(A["!margins"].header=+e0(b[0]).Margin);break;case"footer":A["!margins"]||n9(A["!margins"]={},"xlml"),isNaN(+e0(b[0]).Margin)||(A["!margins"].footer=+e0(b[0]).Margin);break;case"pagemargins":var eE=e0(b[0]);A["!margins"]||n9(A["!margins"]={},"xlml"),isNaN(+eE.Top)||(A["!margins"].top=+eE.Top),isNaN(+eE.Left)||(A["!margins"].left=+eE.Left),isNaN(+eE.Right)||(A["!margins"].right=+eE.Right),isNaN(+eE.Bottom)||(A["!margins"].bottom=+eE.Bottom);break;case"displayrighttoleft":ea.Views||(ea.Views=[]),ea.Views[0]||(ea.Views[0]={}),ea.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":A["!outline"]||(A["!outline"]={}),A["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":A["!outline"]||(A["!outline"]={}),A["!outline"].left=!0;break;default:ew=!1}break;case"pivottable":case"pivotcache":switch(b[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ew=!1}break;case"pagebreaks":switch(b[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ew=!1}break;case"autofilter":switch(b[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ew=!1}break;case"querytable":switch(b[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ew=!1}break;case"datavalidation":switch(b[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ew=!1}break;case"sorting":case"conditionalformatting":switch(b[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ew=!1}break;case"mapinfo":case"schema":case"data":switch(b[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ew=!1}break;case"smarttags":break;default:ew=!1}if(ew||b[3].match(/!\[CDATA/))break;if(!S[S.length-1][1])throw"Unrecognized tag: "+b[3]+"|"+S.join("|");if("customdocumentproperties"===S[S.length-1][0]){"/>"===b[0].slice(-2)||("/"===b[1]?function(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=e9(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=eL(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+r[0])}e[(null)(t)]=n}(H,es,G,c.slice(W,b.index)):(G=b,W=b.index+b[0].length));break}if(l.WTF)throw"Unrecognized tag: "+b[3]+"|"+S.join("|")}var eS={};return l.bookSheets||l.bookProps||(eS.Sheets=y),eS.SheetNames=_,eS.Workbook=ea,eS.SSF=eU(j),eS.Props=B,eS.Custprops=H,eS}function sm(e){return td("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+nR(e.Ref,{r:0,c:0})})}function sg(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=j[a])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||rM[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v):e.w=em(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&a&&"n"==e.t&&eu(j[a]||String(a))){var n=K(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function sv(e,t,r){return{v:e,ixfe:t,t:r}}var sT={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"},sb={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[rm(e)]}},2:{f:function(e){return[rm(e),rE(e),"n"]}},3:{f:function(e){return[rm(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rm(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rm(e),rA(e),"n"]}},6:{f:function(e){return[rm(e),ru(e),"str"]}},7:{f:function(e){return[rm(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=rm(e);n.r=r["!row"];var s=[n,ru(e),"str"];if(r.cellFormula){e.l+=2;var i=nQ(e,a-e.l,r);s[3]=nq(i,null,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=rm(e);n.r=r["!row"];var s=[n,rA(e),"n"];if(r.cellFormula){e.l+=2;var i=nQ(e,a-e.l,r);s[3]=nq(i,null,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=rm(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=nQ(e,a-e.l,r);s[3]=nq(i,null,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=rm(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=nQ(e,a-e.l,r);s[3]=nq(i,null,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[rv(e)]}},13:{f:function(e){return[rv(e),rE(e),"n"]}},14:{f:function(e){return[rv(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rv(e),e.read_shift(1),"b"]}},16:{f:function(e){return[rv(e),rA(e),"n"]}},17:{f:function(e){return[rv(e),ru(e),"str"]}},18:{f:function(e){return[rv(e),e.read_shift(4),"s"]}},19:{f:rp},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=ru(e),i=nQ(e,0,r),o=rb(e);e.l=a;var l={Name:s,Ptg:i};return n<0xfffffff&&(l.Sheet=n),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var a,n={};n.sz=e.read_shift(2)/20;var s=(a=e.read_shift(1),e.l++,{fBold:1&a,fItalic:2&a,fUnderline:4&a,fStrikeout:8&a,fOutline:16&a,fShadow:32&a,fCondense:64&a,fExtend:128&a});switch(s.fItalic&&(n.italic=1),s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var o=e.read_shift(1);o>0&&(n.family=o);var l=e.read_shift(1);switch(l>0&&(n.charset=l),e.l++,n.color=function(e){var t={},r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=a;var l=rP[a];l&&(t.rgb=a9(l));break;case 2:t.rgb=a9([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=ru(e,t-21),n}},44:{f:function(e,t){return[e.read_shift(2),ru(e,t-2)]}},45:{f:tQ},46:{f:tQ},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:aG},62:{f:function(e){return[rm(e),rp(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rr(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:tQ,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=ru(e,t-19),r}},148:{f:ry,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?ru(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rb(e,t-8),r.name=ru(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:ry},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:ry},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:ru(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rb},357:{},358:{},359:{},360:{T:1},361:{},362:{f:aH},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=ry(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=nQ(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[ry(e,16)];if(r.cellFormula){var s=nQ(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return sn.forEach(function(r){t[r]=rA(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=ry(e,16),n=rb(e),s=ru(e),i=ru(e),o=ru(e);e.l=r;var l={rfx:a,relId:n,loc:s,display:o};return i&&(l.Tooltip=i),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rb},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:ru},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=ry(e,16);return t.rfx=r.s,t.ref=rr(r.s),e.l+=16,t}},636:{T:-1},637:{f:rp},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:ru(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},sw={6:{f:nZ},10:{f:as},12:{f:al},13:{f:al},14:{f:ai},15:{f:ai},16:{f:rA},17:{f:ai},18:{f:ai},19:{f:al},20:{f:aL},21:{f:aL},23:{f:aH},24:{f:aB},25:{f:ai},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=am(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}(e,0,r)}},29:{},34:{f:ai},35:{f:aF},38:{f:rA},39:{f:rA},40:{f:rA},41:{f:rA},42:{f:ai},43:{f:ai},47:{f:function(e,t,r){var a,n,s,i,o={Type:r.biff>=8?e.read_shift(2):0};return o.Type?(a=t-2,(n=o||{}).Info=e.read_shift(2),e.l-=2,1===n.Info?n.Data=function(e){var t={},r=t.EncryptionVersionInfo=a4(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e,a):n.Data=function(e,t){var r,a,n,s,i={},o=i.EncryptionVersionInfo=a4(e,4);if(t-=4,2!=o.Minor)throw Error("unrecognized minor version code: "+o.Minor);if(o.Major>4||o.Major<2)throw Error("unrecognized major version code: "+o.Major);i.Flags=e.read_shift(4),t-=4;var l=e.read_shift(4);return t-=4,i.EncryptionHeader=function(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}(e,l),r=e,a=t-=l,n={},s=r.l+a,r.l+=4,n.Salt=r.slice(r.l,r.l+16),r.l+=16,n.Verifier=r.slice(r.l,r.l+16),r.l+=16,r.read_shift(4),n.VerifierHash=r.slice(r.l,s),r.l=s,i.EncryptionVerifier=n,i}(e,a)):(r.biff,i={key:al(e),verificationBytes:al(e)},r.password&&(i.verifier=a3(r.password)),o.valid=i.verificationBytes===i.verifier,o.valid&&(o.insitu=a8(r.password))),o}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=au(e,0,r),a}},51:{f:al},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:ai},65:{f:function(){}},66:{f:al},77:{},80:{},81:{},82:{},85:{f:al},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=am(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8){var a,n,s,i,o,l,c;return a=e,n=t,s=r,a.l+=4,i=a.read_shift(2),o=a.read_shift(2),l=a.read_shift(2),a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=6,n-=36,(c=[]).push((aj[i]||tQ)(a,n,s)),{cmo:[o,i,l],ft:c}}var f=ak(e,22),h=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(aO[n](e,r-e.l))}catch(t){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:ai},96:{},97:{},99:{f:ai},125:{f:aG},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:al},131:{f:ai},132:{f:ai},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=au(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=rN[t]||t,t=e.read_shift(2),r[1]=rN[t]||t,r}},141:{f:al},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aw(e,8));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:al},157:{},158:{},160:{f:af},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=rA(e,8),r.footer=rA(e,8),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(ay(e));if(e.l!==r)throw Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:as},197:{},198:{},199:{},200:{},201:{},202:{f:ai},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:al},220:{},221:{f:ai},222:{},224:{f:function(e,t,r){var a,n,s,i,o,l,c={};return c.ifnt=e.read_shift(2),c.numFmtId=e.read_shift(2),c.flags=e.read_shift(2),c.fStyle=c.flags>>2&1,t-=6,c.fStyle,n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),l=e.read_shift(2),n.patternType=rD[o>>26],r.cellStyles&&(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&l,n.icvBack=l>>7&127,n.fsxButton=l>>14&1),c.data=n,c}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:as},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(a_(e,t));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(function(e){var t=c;c=1200;var r,a=e.read_shift(2),n=e.read_shift(1),s=4&n,i=8&n,o=0,l={};i&&(o=e.read_shift(2)),s&&(r=e.read_shift(4));var f=0===a?"":e.read_shift(a,2==1+(1&n)?"dbcs-cont":"sbcs-cont");return i&&(e.l+=4*o),s&&(e.l+=r),l.t=f,i||(l.raw="<t>"+l.t+"</t>",l.r=l.t),c=t,l}(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=aE(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:af},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:ai},353:{f:as},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw Error("Unexpected SupBook type: "+s);for(var i=ad(e,s),o=[];a>e.l;)o.push(ap(e));return[s,n,i,o]}},431:{f:ai},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s,i,o=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(o)?e.l+=6:(e.read_shift(1),e.l++,e.read_shift(2),e.l+=2);var l=e.read_shift(2);e.read_shift(2),al(e,2);var c=e.read_shift(2);e.l+=c;for(var f=1;f<e.lens.length-1;++f){if(e.l-a!=e.lens[f])throw Error("TxO: bad continue record");var h=e[e.l],u=ad(e,e.lens[f+1]-e.lens[f]-1);if((n+=u).length>=(h?l:2*l))break}if(n.length!==l&&n.length!==2*l)throw Error("cchText: "+l+" != "+n.length);return e.l=a+t,{t:n}}catch(r){return e.l=a+t,{t:n}}}},439:{f:ai},440:{f:function(e,t){var r=a_(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,l,c,f,h="";16&n&&(s=av(e,r-e.l)),128&n&&(i=av(e,r-e.l)),(257&n)==257&&(o=av(e,r-e.l)),(257&n)==1&&(l=function(e,t){var r,a,n,s,i=e.read_shift(16);switch(t-=16,i){case"e0c9ea79f9bace118c8200aa004ba90b":return r=e.read_shift(4),a=e.l,n=!1,r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(n=!0),e.l=a),s=e.read_shift((n?r-24:r)>>1,"utf16le").replace(N,""),n&&(e.l+=24),s;case"0303000000000000c000000000000046":for(var o=e.read_shift(2),l="";o-- >0;)l+="../";var c=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return l+c.replace(/\\/g,"/");var f=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return l+e.read_shift(f>>1,"utf16le").replace(N,"");default:throw Error("Unsupported Moniker "+i)}}(e,r-e.l)),8&n&&(h=av(e,r-e.l)),32&n&&(c=e.read_shift(16)),64&n&&(f=r0(e)),e.l=r;var u=i||o||l||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return c&&(d.guid=c),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24)]}},441:{},442:{f:ap},443:{},444:{f:al},445:{},446:{},448:{f:as},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:as},512:{f:aD},513:{f:aE},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=aE(e,6);return a.val=rA(e,8),a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=e.l+t,n=aE(e,6);return 2==r.biff&&e.l++,n.val=ap(e,a-e.l,r),n}},517:{f:aM},519:{f:ap},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:aW},549:{f:aN},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=ay(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),am(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=ax(e,6);e.l++;var n=e.read_shift(1);return[function(e,t,r){var a,n,s=e.l+t,i=e.read_shift(2),o=n$(e,i,r);return 65535==i?[[],(a=t-2,void(e.l+=a))]:(t!==i+2&&(n=nK(e,s-i-2,o,r)),[o,n])}(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=a_(e,8),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(N,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:aR},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:as},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(function(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){e.l+=t}(e,4);break;case 2:t.xclrValue=ab(e,4);break;case 3:t.xclrValue=e.read_shift(4)}return e.l+=8,t}(e,r);break;case 6:a[1]=void(e.l+=r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw Error("Unrecognized ExtProp type: "+t+" "+r)}return a}(e,r-e.l));return{ixfe:a,ext:s}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:ai,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2);return[ad(e,a,r),ad(e,n,r)]},r:12},2197:{},2198:{f:function(e,t,r){var a,n=e.l+t;if(124226!==e.read_shift(4)){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;try{a=function(e,t){switch(t.type){case"base64":return eE.read(e,{type:"base64"});case"binary":return eE.read(e,{type:"binary"});case"buffer":case"array":return eE.read(e,{type:"buffer"})}throw Error("Unrecognized type "+t.type)}(s,{type:"array"})}catch(e){return}var i=e$(a,"theme/theme/theme1.xml",!0);if(i){var o=i,l=r;o&&0!==o.length||(o=nS());var c,f,h,u={};if(!(h=o.match(nE)))throw Error("themeElements not found in theme");return c=h[0],u.themeElements={},[["clrScheme",nT,nm],["fontScheme",nb,ng],["fmtScheme",nw,nv]].forEach(function(e){if(!(f=c.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](f,u,l)}),u.raw=o,u}}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:as},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t,r,a=(t=e.read_shift(2),r=e.read_shift(2),e.l+=8,{type:t,flags:r});if(2211!=a.type)throw Error("Invalid Future Record "+a.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:al},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aw(e,8));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:aD},1:{},2:{f:function(e){var t=aE(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=aE(e,6);++e.l;var r=rA(e,8);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=aE(e,6);++e.l;var n=am(e,t-7,r);return a.t="str",a.val=n,a}},5:{f:aM},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:aR},11:{},22:{f:al},30:{f:am},31:{},32:{},33:{f:aW},36:{},37:{f:aN},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:al},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=aE(e,6),s=e.read_shift(2),i=ad(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:nZ},521:{f:aR},536:{f:aB},547:{f:aF},561:{},579:{},1030:{f:nZ},1033:{f:aR},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function sE(e,t,r,a){if(!isNaN(t)){var n=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,t),s.write_shift(2,n),n>0&&tH(r)&&e.push(r)}}function sS(e,t,r){return e||(e=t1(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function sy(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];a&&a["!ref"]&&ra(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:var s=t||{},i=[];e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eT(),ev(e.SSF),s.revssf=ex(e.SSF),s.revssf[e.SSF[65535]]=0,s.ssf=e.SSF),s.Strings=[],s.Strings.Count=0,s.Strings.Unique=0,s1(s),s.cellXfs=[],se(s.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var o=0;o<e.SheetNames.length;++o)i[i.length]=function(e,t,r){var a,n,s,i,o,l,c,f,h,u=t0(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),T=8==t.biff,b="",w=[],E=rs(p["!ref"]||"A1"),S=T?65536:16384;if(E.e.c>255||E.e.r>=S){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");E.e.c=Math.min(E.e.c,255),E.e.r=Math.min(E.e.c,S-1)}sE(u,2057,aI(r,16,t)),sE(u,13,ac(1)),sE(u,12,ac(100)),sE(u,15,ao(!0)),sE(u,17,ao(!1)),sE(u,16,rx(.001)),sE(u,95,ao(!0)),sE(u,42,ao(!1)),sE(u,43,ao(!1)),sE(u,130,ac(1)),sE(u,128,(a=[0,0],(n=t1(8)).write_shift(4,0),n.write_shift(2,a[0]?a[0]+1:0),n.write_shift(2,a[1]?a[1]+1:0),n)),sE(u,131,ao(!1)),sE(u,132,ao(!1)),T&&function(e,t){if(t){var r=0;t.forEach(function(t,a){var n,s,i;++r<=256&&t&&sE(e,125,(n=n7(a,t),(s=t1(12)).write_shift(2,a),s.write_shift(2,a),s.write_shift(2,256*n.width),s.write_shift(2,0),i=0,n.hidden&&(i|=1),s.write_shift(1,i),i=n.level||0,s.write_shift(1,i),s.write_shift(2,0),s))})}}(u,p["!cols"]),sE(u,512,((i=t1(2*(s=8!=t.biff&&t.biff?2:4)+6)).write_shift(s,E.s.r),i.write_shift(s,E.e.r+1),i.write_shift(2,E.s.c),i.write_shift(2,E.e.c+1),i.write_shift(2,0),i)),T&&(p["!links"]=[]);for(var y=E.s.r;y<=E.e.r;++y){b=t7(y);for(var _=E.s.c;_<=E.e.c;++_){y===E.s.r&&(w[_]=re(_)),h=w[_]+b;var A=v?(p[y]||[])[_]:p[h];A&&(!function(e,t,r,a,n){var s=16+se(n.cellXfs,t,n);if(null==t.v&&!t.bf)return sE(e,513,aS(r,a,s));if(t.bf)sE(e,6,function(e,t,r,a,n){var s=aS(t,r,n),i=function(e){if(null==e){var t=t1(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?rx(e):rx(0)}(e.v),o=t1(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=t1(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];return I([s,i,o,l])}(t,r,a,0,s));else switch(t.t){case"d":case"n":var i,o="d"==t.t?eC(eL(t.v)):t.v;sE(e,515,(aS(r,a,s,i=t1(14)),rx(o,i),i));break;case"b":case"e":sE(e,517,(l=t.v,c=t.t,aS(r,a,s,f=t1(8)),ah(l,c,f),f));break;case"s":case"str":if(n.bookSST){var l,c,f,h,u,d,p,m=n8(n.Strings,t.v,n.revStrings);sE(e,253,(aS(r,a,s,p=t1(10)),p.write_shift(4,m),p))}else sE(e,516,(h=(t.v||"").slice(0,255),aS(r,a,s,d=t1(8+ +(u=!n||8==n.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:sE(e,513,aS(r,a,s))}}(u,A,y,_,t),T&&A.l&&p["!links"].push([h,A.l]))}}var x=g.CodeName||g.name||d;return T&&sE(u,574,(o=(m.Views||[])[0],l=t1(18),c=1718,o&&o.RTL&&(c|=64),l.write_shift(2,c),l.write_shift(4,0),l.write_shift(4,64),l.write_shift(4,0),l.write_shift(4,0),l)),T&&(p["!merges"]||[]).length&&sE(u,229,function(e){var t=t1(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)aA(e[r],t);return t}(p["!merges"])),T&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];sE(e,440,function(e){var t=t1(24),r=rt(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return I([t,function(e){var t=t1(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)aT(a=a.slice(1),t);else if(2&s){for(r=0,i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&aT(n>-1?a.slice(n+1):"",t)}else{for(r=0,i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var l=0;"../"==a.slice(3*l,3*l+3)||"..\\"==a.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,a.length-3*l+1),r=0;r<a.length-3*l;++r)t.write_shift(1,255&a.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(a)),a[1].Tooltip&&sE(e,2048,function(e){var t=e[1].Tooltip,r=t1(10+2*(t.length+1));r.write_shift(2,2048);var a=rt(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}(a))}delete t["!links"]}(u,p),sE(u,442,ag(x,t)),T&&((f=t1(19)).write_shift(4,2151),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,1),f.write_shift(4,0),sE(u,2151,f),(f=t1(39)).write_shift(4,2152),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,0),f.write_shift(4,0),f.write_shift(2,1),f.write_shift(4,4),f.write_shift(2,0),aA(rs(p["!ref"]||"A1"),f),f.write_shift(4,4),sE(u,2152,f)),sE(u,10),u.end()}(o,s,e);return i.unshift(function(e,t,r){var a,n,s,i,o,l,c,f=t0(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;sE(f,2057,aI(e,5,r)),"xla"==r.bookType&&sE(f,135),sE(f,225,p?ac(1200):null),sE(f,193,function(e,t){t||(t=t1(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(2)),m&&sE(f,191),m&&sE(f,192),sE(f,226),sE(f,92,function(e,t){var r=!t||8==t.biff,a=t1(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,0x33336853),a.write_shift(4,5458548|0x20000000*!r);a.l<a.length;)a.write_shift(1,32*!r);return a}(0,r)),sE(f,66,ac(p?1200:1252)),p&&sE(f,353,ac(0)),p&&sE(f,448),sE(f,317,function(e){for(var t=t1(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&sE(f,211),p&&e.vbaraw&&sE(f,442,ag(d.CodeName||"ThisWorkbook",r)),sE(f,156,ac(17)),sE(f,25,ao(!1)),sE(f,18,ao(!1)),sE(f,19,ac(0)),p&&sE(f,431,ao(!1)),p&&sE(f,444,ac(0)),sE(f,61,((a=t1(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),sE(f,64,ao(!1)),sE(f,141,ac(0)),sE(f,34,ao("true"==(e.Workbook&&e.Workbook.WBProps&&e9(e.Workbook.WBProps.date1904)?"true":"false"))),sE(f,14,ao(!0)),p&&sE(f,439,ao(!1)),sE(f,218,ac(0)),sE(f,49,(s=(n={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(o=t1((i=r&&5==r.biff)?15+s.length:16+2*s.length)).write_shift(2,20*(n.sz||12)),o.write_shift(4,0),o.write_shift(2,400),o.write_shift(4,0),o.write_shift(2,0),o.write_shift(1,s.length),i||o.write_shift(1,1),o.write_shift((i?1:2)*s.length,s,i?"sbcs":"utf16le"),o)),l=e.SSF,l&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=l[t]&&sE(f,1054,function(e,t,r,a){var n=r&&5==r.biff;a||(a=t1(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}(t,l[t],r))});for(var g=0;g<16;++g)sE(f,224,aP({numFmtId:0,style:!0},0,r));r.cellXfs.forEach(function(e){sE(f,224,aP(e,0,r))}),p&&sE(f,352,ao(!1));var v=f.end(),T=t0();p&&sE(T,140,(c||(c=t1(4)),c.write_shift(2,1),c.write_shift(2,1),c)),p&&r.Strings&&function(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return sE(e,252,r,n);if(!isNaN(252)){for(var s=r.parts||[],i=0,o=0,l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;var c=e.next(4);for(c.write_shift(2,t),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<n;){for((c=e.next(4)).write_shift(2,60),l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}(T,252,function(e,t){var r=t1(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=function(e){var t=e.t||"",r=t1(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=t1(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),I([r,a])}(e[n],t);var s=I([r].concat(a));return s.parts=[r.length].concat(a.map(function(e){return e.length})),s}(r.Strings,r)),sE(T,10);var b=T.end(),w=t0(),E=0,S=0;for(S=0;S<e.SheetNames.length;++S)E+=(p?12:11)+(p?2:1)*e.SheetNames[S].length;var y=v.length+E+b.length;for(S=0;S<e.SheetNames.length;++S)sE(w,133,function(e,t){var r=!t||t.biff>=8?2:1,a=t1(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}({pos:y,hs:(u[S]||{}).Hidden||0,dt:0,name:e.SheetNames[S]},r)),y+=t[S].length;var _=w.end();if(E!=_.length)throw Error("BS8 "+E+" != "+_.length);var A=[];return v.length&&A.push(v),_.length&&A.push(_),b.length&&A.push(b),I(A)}(e,i,s)),I(i);case 4:case 3:case 2:for(var l=t||{},c=t0(),f=0,h=0;h<e.SheetNames.length;++h)e.SheetNames[h]==l.sheet&&(f=h);if(0==f&&l.sheet&&e.SheetNames[0]!=l.sheet)throw Error("Sheet not found: "+l.sheet);return sE(c,4==l.biff?1033:3==l.biff?521:9,aI(e,16,l)),!function(e,t,r,a){var n,s=Array.isArray(t),i=rs(t["!ref"]||"A1"),o="",l=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=rn(i)}for(var c=i.s.r;c<=i.e.r;++c){o=t7(c);for(var f=i.s.c;f<=i.e.c;++f){c===i.s.r&&(l[f]=re(f)),n=l[f]+o;var h=s?(t[c]||[])[f]:t[n];h&&function(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n,s,i,o,l,c,f,h="d"==t.t?eC(eL(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?sE(e,2,(sS(c=t1(9),r,a),c.write_shift(2,h),c)):sE(e,3,(sS(f=t1(15),r,a),f.write_shift(8,h,"f"),f));return;case"b":case"e":sE(e,5,(n=t.v,s=t.t,sS(i=t1(9),r,a),ah(n,s||"b",i),i));return;case"s":case"str":sE(e,4,(sS(l=t1(8+2*(o=(t.v||"").slice(0,255)).length),r,a),l.write_shift(1,o.length),l.write_shift(o.length,o,"sbcs"),l.l<l.length?l.slice(0,l.l):l));return}sE(e,1,sS(null,r,a))}(e,h,c,f,a)}}}(c,e.Sheets[e.SheetNames[f]],0,l,e),sE(c,10),c.end()}throw Error("invalid type "+n.bookType+" for BIFF")}function s_(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,l=(null)(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),c=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<l.length;++i){var m=l[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++c,r.sheetRows&&r.sheetRows<=c){--c;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var T=v[o].trim();if(T.match(/<t[dh]/i)){for(var b=T,w=0;"<"==b.charAt(0)&&(w=b.indexOf(">"))>-1;)b=b.slice(w+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<c&&c<=S.e.r&&(f=S.e.c+1,E=-1)}var y=e0(T.slice(0,T.indexOf(">")));u=y.colspan?+y.colspan:1,((h=+y.rowspan)>1||u>1)&&p.push({s:{r:c,c:f},e:{r:c+(h||1)-1,c:f+u-1}});var _=y.t||y["data-t"]||"";if(!b.length||(b=ti(b),d.s.r>c&&(d.s.r=c),d.e.r<c&&(d.e.r=c),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!b.length)){f+=u;continue}var A={t:"s",v:b};r.raw||!b.trim().length||"s"==_||("TRUE"===b?A={t:"b",v:!0}:"FALSE"===b?A={t:"b",v:!1}:isNaN(eH(b))?isNaN(ej(b).getDate())||(A={t:"d",v:eL(b)},r.cellDates||(A={t:"n",v:eC(A.v)}),A.z=r.dateNF||j[14]):A={t:"n",v:eH(b)}),r.dense?(a[c]||(a[c]=[]),a[c][f]=A):a[rr({r:c,c:f})]=A,f+=u}}}}return a["!ref"]=rn(d),p.length&&(a["!merges"]=p),a}function sA(e,t){var r,a,n,s=t||{},i=null!=s.header?s.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',o=null!=s.footer?s.footer:"</body></html>",l=[i],c=ra(e["!ref"]);s.dense=Array.isArray(e),l.push((r=0,a=0,"<table"+((n=s)&&n.id?' id="'+n.id+'"':"")+">"));for(var f=c.s.r;f<=c.e.r;++f)l.push(function(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,l=0,c=0;c<n.length;++c)if(!(n[c].s.r>r)&&!(n[c].s.c>i)&&!(n[c].e.r<r)&&!(n[c].e.c<i)){if(n[c].s.r<r||n[c].s.c<i){o=-1;break}o=n[c].e.r-n[c].s.r+1,l=n[c].e.c-n[c].s.c+1;break}if(!(o<0)){var f=rr({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||e7(h.w||(ro(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(td("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}(e,c,f,s));return l.push("</table>"+o),l.join("")}function sx(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?rt(a.origin):a.origin;n=i.r,s=i.c}var o=t.getElementsByTagName("tr"),l=Math.min(a.sheetRows||1e7,o.length),c={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=ra(e["!ref"]);c.s.r=Math.min(c.s.r,f.s.r),c.s.c=Math.min(c.s.c,f.s.c),c.e.r=Math.max(c.e.r,f.e.r),c.e.c=Math.max(c.e.c,f.e.c),-1==n&&(c.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,T=0,b=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<l;++p){var w=o[p];if(sC(w)){if(a.display)continue;d[m]={hidden:!0}}var E=w.children;for(g=v=0;g<E.length;++g){var S=E[g];if(!(a.display&&sC(S))){var y=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):ti(S.innerHTML),_=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var A=h[u];A.s.c==v+s&&A.s.r<m+n&&m+n<=A.e.r&&(v=A.e.c+1-s,u=-1)}b=+S.getAttribute("colspan")||1,((T=+S.getAttribute("rowspan")||1)>1||b>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(T||1)-1,c:v+s+(b||1)-1}});var x={t:"s",v:y},k=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=y&&(0==y.length?x.t=k||"z":a.raw||0==y.trim().length||"s"==k||("TRUE"===y?x={t:"b",v:!0}:"FALSE"===y?x={t:"b",v:!1}:isNaN(eH(y))?isNaN(ej(y).getDate())||(x={t:"d",v:eL(y)},a.cellDates||(x={t:"n",v:eC(x.v)}),x.z=a.dateNF||j[14]):x={t:"n",v:eH(y)})),void 0===x.z&&null!=_&&(x.z=_);var C="",O=S.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(C=O[R].getAttribute("href")).charAt(0));++R);C&&"#"!=C.charAt(0)&&(x.l={Target:C}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=x):e[rr({c:v+s,r:m+n})]=x,c.e.c<v+s&&(c.e.c=v+s),v+=b}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),c.e.r=Math.max(c.e.r,m-1+n),e["!ref"]=rn(c),m>=l&&(e["!fullref"]=rn((c.e.r=o.length-p+m-1+n,c))),e}function sk(e,t){return sx((t||{}).dense?[]:{},e,t)}function sC(e){var t,r="",a=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return a&&(r=a(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var sO={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function sR(e,t){var r=t||{},a,n,s,i,o,l,c,f=tm(e),h=[],u={name:""},d="",p=0,m={},g=[],v=r.dense?[]:{},T={value:""},b="",w=0,E=[],S=-1,y=-1,_={s:{r:1e6,c:1e7},e:{r:0,c:0}},A=0,x={},k=[],C={},O=0,R=0,I=[],N=1,D=1,P=[],M={Names:[]},L={},F=["",""],U=[],B={},H="",W=0,j=!1,G=!1,V=0;for(tg.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");o=tg.exec(f);)switch(o[3]=o[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===o[1]?(_.e.c>=_.s.c&&_.e.r>=_.s.r?v["!ref"]=rn(_):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=_.e.r&&(v["!fullref"]=v["!ref"],_.e.r=r.sheetRows-1,v["!ref"]=rn(_)),k.length&&(v["!merges"]=k),I.length&&(v["!rows"]=I),s.name=s["名称"]||s.name,"undefined"!=typeof JSON&&JSON.stringify(s),g.push(s.name),m[s.name]=v,G=!1):"/"!==o[0].charAt(o[0].length-2)&&(s=e0(o[0],!1),S=y=-1,_.s.r=_.s.c=1e7,_.e.r=_.e.c=0,v=r.dense?[]:{},k=[],I=[],G=!0);break;case"table-row-group":"/"===o[1]?--A:++A;break;case"table-row":case"行":if("/"===o[1]){S+=N,N=1;break}if((i=e0(o[0],!1))["行号"]?S=i["行号"]-1:-1==S&&(S=0),(N=+i["number-rows-repeated"]||1)<10)for(V=0;V<N;++V)A>0&&(I[S+V]={level:A});y=-1;break;case"covered-table-cell":"/"!==o[1]&&++y,r.sheetStubs&&(r.dense?(v[S]||(v[S]=[]),v[S][y]={t:"z"}):v[rr({r:S,c:y})]={t:"z"}),b="",E=[];break;case"table-cell":case"数据":if("/"===o[0].charAt(o[0].length-2))++y,D=parseInt((T=e0(o[0],!1))["number-columns-repeated"]||"1",10),l={t:"z",v:null},T.formula&&!1!=r.cellFormula&&(l.f=n4((null)(T.formula))),"string"==(T["数据类型"]||T["value-type"])&&(l.t="s",l.v=(null)(T["string-value"]||""),r.dense?(v[S]||(v[S]=[]),v[S][y]=l):v[rr({r:S,c:y})]=l),y+=D-1;else if("/"!==o[1]){b="",w=0,E=[],D=1;var z=N?S+N-1:S;if(++y>_.e.c&&(_.e.c=y),y<_.s.c&&(_.s.c=y),S<_.s.r&&(_.s.r=S),z>_.e.r&&(_.e.r=z),T=e0(o[0],!1),U=[],B={},l={t:T["数据类型"]||T["value-type"],v:null},r.cellFormula)if(T.formula&&(T.formula=(null)(T.formula)),T["number-matrix-columns-spanned"]&&T["number-matrix-rows-spanned"]&&(C={s:{r:S,c:y},e:{r:S+(O=parseInt(T["number-matrix-rows-spanned"],10)||0)-1,c:y+(parseInt(T["number-matrix-columns-spanned"],10)||0)-1}},l.F=rn(C),P.push([C,l.F])),T.formula)l.f=n4(T.formula);else for(V=0;V<P.length;++V)S>=P[V][0].s.r&&S<=P[V][0].e.r&&y>=P[V][0].s.c&&y<=P[V][0].e.c&&(l.F=P[V][1]);switch((T["number-columns-spanned"]||T["number-rows-spanned"])&&(C={s:{r:S,c:y},e:{r:S+(O=parseInt(T["number-rows-spanned"],10)||0)-1,c:y+(parseInt(T["number-columns-spanned"],10)||0)-1}},k.push(C)),T["number-columns-repeated"]&&(D=parseInt(T["number-columns-repeated"],10)),l.t){case"boolean":l.t="b",l.v=e9(T["boolean-value"]);break;case"float":case"percentage":case"currency":l.t="n",l.v=parseFloat(T.value);break;case"date":l.t="d",l.v=eL(T["date-value"]),r.cellDates||(l.t="n",l.v=eC(l.v)),l.z="m/d/yy";break;case"time":l.t="n",l.v=function(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}(T["time-value"])/86400,r.cellDates&&(l.t="d",l.v=eN(l.v)),l.z="HH:MM:SS";break;case"number":l.t="n",l.v=parseFloat(T["数据数值"]);break;default:if("string"!==l.t&&"text"!==l.t&&l.t)throw Error("Unsupported value type "+l.t);l.t="s",null!=T["string-value"]&&(b=(null)(T["string-value"]),E=[])}}else{if(j=!1,"s"===l.t&&(l.v=b||"",E.length&&(l.R=E),j=0==w),L.Target&&(l.l=L),U.length>0&&(l.c=U,U=[]),b&&!1!==r.cellText&&(l.w=b),j&&(l.t="z",delete l.v),(!j||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=S))for(var K=0;K<N;++K){if(D=parseInt(T["number-columns-repeated"]||"1",10),r.dense)for(v[S+K]||(v[S+K]=[]),v[S+K][y]=0==K?l:eU(l);--D>0;)v[S+K][y+D]=eU(l);else for(v[rr({r:S+K,c:y})]=l;--D>0;)v[rr({r:S+K,c:y+D})]=eU(l);_.e.c<=y&&(_.e.c=y)}y+=(D=parseInt(T["number-columns-repeated"]||"1",10))-1,D=0,l={},b="",E=[]}L={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!0]);break;case"annotation":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a;B.t=b,E.length&&(B.R=E),B.a=H,U.push(B)}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);H="",W=0,b="",w=0,E=[];break;case"creator":"/"===o[1]?H=f.slice(W,o.index):W=o.index+o[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);b="",w=0,E=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===o[1]){if(x[u.name]=d,(a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&(d="",u=e0(o[0],!1),h.push([o[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":n=e0(o[0],!1),d+=sO[o[3]][+("long"===n.style)]}break;case"text":if("/>"===o[0].slice(-2));else if("/"===o[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,o.index)}else p=o.index+o[0].length;break;case"named-range":F=n3((n=e0(o[0],!1))["cell-range-address"]);var Y={Name:n.name,Ref:F[0]+"!"+F[1]};G&&(Y.Sheet=g.length),M.Names.push(Y);break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==o[1]||T&&T["string-value"])e0(o[0],!1),w=o.index+o[0].length;else{var X=[(null)(f.slice(w,o.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];b=(b.length>0?b+"\n":"")+X[0]}break;case"database-range":if("/"===o[1])break;try{m[(F=n3(e0(o[0])["target-range-address"]))[0]]["!autofilter"]={ref:F[1]}}catch(e){}break;case"a":if("/"!==o[1]){if(!(L=e0(o[0],!1)).href)break;L.Target=(null)(L.href),delete L.href,"#"==L.Target.charAt(0)&&L.Target.indexOf(".")>-1?(F=n3(L.Target.slice(1)),L.Target="#"+F[0]+"!"+F[1]):L.Target.match(/^\.\.[\\\/]/)&&(L.Target=L.Target.slice(3))}break;default:switch(o[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw Error(o)}}var J={Sheets:m,SheetNames:g,Workbook:M};return r.bookSheets&&delete J.Sheets,J}var sI=function(){var e="<office:document-styles "+tu({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return eJ+e}}(),sN=function(){var e="          <table:table-cell />\n",t=function(t,r,a){var n=[];n.push('      <table:table table:name="'+e5(r.SheetNames[a])+'" table:style-name="ta1">\n');var s=0,i=0,o=ra(t["!ref"]||"A1"),l=t["!merges"]||[],c=0,f=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)n.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)n.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(c=0;c!=l.length;++c)if(!(l[c].s.c>i)&&!(l[c].s.r>s)&&!(l[c].e.c<i)&&!(l[c].e.r<s)){(l[c].s.c!=i||l[c].s.r!=s)&&(d=!0),p["table:number-columns-spanned"]=l[c].e.c-l[c].s.c+1,p["table:number-rows-spanned"]=l[c].e.r-l[c].s.r+1;break}if(d){n.push("          <table:covered-table-cell/>\n");continue}var g=rr({r:s,c:i}),v=f?(t[s]||[])[i]:t[g];if(v&&v.f&&(p["table:formula"]=e5(("of:="+v.f.replace(nO,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var T=ra(v.F);p["table:number-matrix-columns-spanned"]=T.e.c-T.s.c+1,p["table:number-matrix-rows-spanned"]=T.e.r-T.s.r+1}if(!v){n.push(e);continue}switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||eL(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=eL(v.v).toISOString(),p["table:style-name"]="ce1";break;default:n.push(e);continue}var b=e5(m).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var w=v.l.Target;"#"==(w="#"==w.charAt(0)?"#"+w.slice(1).replace(/\./,"!"):w).charAt(0)||w.match(/^\w+:/)||(w="../"+w),b=td("text:a",b,{"xlink:href":w.replace(/&/g,"&amp;")})}n.push("          "+td("table:table-cell",td("text:p",b,{}),p)+"\n")}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")},r=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!cols"]){for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;ns(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}});var a=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!rows"]){for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}}}),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,a){var n=[eJ],s=tu({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=tu({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==a.bookType?(n.push("<office:document"+s+i+">\n"),n.push(rK().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+s+">\n"),r(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)n.push(t(e.Sheets[e.SheetNames[o]],e,o,a));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==a.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function sD(e,t){if("fods"==t.bookType)return sN(e,t);var r=eX(),a="",n=[],s=[];return eY(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),eY(r,a="content.xml",sN(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),eY(r,a="styles.xml",sI(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),eY(r,a="meta.xml",eJ+rK()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),eY(r,a="manifest.rdf",function(e){var t=[eJ];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(rz(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(rz("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),n.push([a,"application/rdf+xml"]),eY(r,a="META-INF/manifest.xml",function(e){var t=[eJ];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function sP(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function sM(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):tn(R(e))}function sL(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function sF(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function sU(e,t){var r=t?t[0]:0,a=127&e[r];t:if(e[r++]>=128&&(a|=(127&e[r])<<7,e[r++]<128||(a|=(127&e[r])<<14,e[r++]<128)||(a|=(127&e[r])<<21,e[r++]<128)||(a+=(127&e[r])*0x10000000,++r,e[r++]<128)||(a+=(127&e[r])*0x800000000,++r,e[r++]<128)||(a+=(127&e[r])*0x40000000000,++r,e[r++]<128)))break t;return t&&(t[0]=r),a}function sB(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;r:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=0xfffffff)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=0x7ffffffff)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=0x3ffffffffff))break r;t[r-1]|=128,t[r]=e/0x1000000>>>21&127,++r}return t.slice(0,r)}function sH(e){var t=0,r=127&e[0];t:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break t;r|=(127&e[t])<<28}return r}function sW(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=sU(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var l=r[0];e[r[0]++]>=128;);a=e.slice(l,r[0]);break;case 5:o=4,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=sU(e,r),a=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var c={data:a,type:i};null==t[s]?t[s]=[c]:t[s].push(c)}return t}function sj(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(sB(8*r+e.type)),2==e.type&&t.push(sB(e.data.length)),t.push(e.data))})}),sL(t)}function sG(e,t){return(null==e?void 0:e.map(function(e){return t(e.data)}))||[]}function sV(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=sU(e,a),s=sW(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:sH(s[1][0].data),messages:[]};s[2].forEach(function(t){var r=sW(t.data),n=sH(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n}),(null==(t=s[3])?void 0:t[0])&&(i.merge=sH(s[3][0].data)>>>0>0),r.push(i)}return r}function sz(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:sB(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:sB(+!!e.merge),type:0}]);var a=[];e.messages.forEach(function(e){a.push(e.data),e.meta[3]=[{type:0,data:sB(e.data.length)}],r[2].push({data:sj(e.meta),type:2})});var n=sj(r);t.push(sB(n.length)),t.push(n),a.forEach(function(e){return t.push(e)})}),sL(t)}function sK(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=sU(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0==s){var i=t[r[0]++]>>2;if(i<60)++i;else{var o=i-59;i=t[r[0]],o>1&&(i|=t[r[0]+1]<<8),o>2&&(i|=t[r[0]+2]<<16),o>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=o}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}var l=0,c=0;if(1==s?(c=(t[r[0]]>>2&7)+4,l=(224&t[r[0]++])<<3|t[r[0]++]):(c=(t[r[0]++]>>2)+1,2==s?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[sL(n)],0==l)throw Error("Invalid offset 0");if(l>n[0].length)throw Error("Invalid offset beyond length");if(c>=l)for(n.push(n[0].slice(-l)),c-=l;c>=n[n.length-1].length;)n.push(n[n.length-1]),c-=n[n.length-1].length;n.push(n[0].slice(-l,-l+c))}var f=sL(n);if(f.length!=a)throw Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw Error("data is not a valid framed stream!");return sL(t)}function s$(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,0xfffffff),n=new Uint8Array(4);t.push(n);var s=sB(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=0x1000000?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=0x100000000&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return sL(t)}function sY(e,t){var r=new Uint8Array(32),a=sP(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,+!!e.v,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function sX(e,t){var r=new Uint8Array(32),a=sP(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,+!!e.v,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function sJ(e){return sU(sW(e)[1][0].data)}function sq(e,t){var r=sW(t.data),a=sH(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(t){var r=sW(t.data),n=sH(r[1][0].data)>>>0;switch(a){case 1:s[n]=sM(r[3][0].data);break;case 8:var i=sW(e[sJ(r[9][0].data)][0].data),o=e[sJ(i[1][0].data)][0],l=sH(o.meta[1][0].data);if(2001!=l)throw Error("2000 unexpected reference to ".concat(l));var c=sW(o.data);s[n]=c[3].map(function(e){return sM(e.data)}).join("")}}),s}function sZ(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function sQ(e){sZ([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function s1(e){sZ([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function s0(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return eS(t.file,eE.write(e,{type:_?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return eE.write(e,t)}function s2(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return S(ts(a));case"binary":return ts(a);case"string":return e;case"file":return eS(t.file,a,"utf8");case"buffer":if(_)return A(a,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(a);return s2(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function s4(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?S(r):"string"==t.type?tn(r):r;case"file":return eS(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function s3(e,t,r){var a=r||{};return a.type="file",a.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType=({xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"})[e.bookType]||e.bookType}}(a),function e(t,r){m(),function(e){if(!e||!e.SheetNames||!e.Sheets)throw Error("Invalid Workbook");if(!e.SheetNames.length)throw Error("Workbook is empty");var t,r,a=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=!!e.vbaraw,t.forEach(function(e,n){sc(e);for(var s=0;s<n;++s)if(e==t[s])throw Error("Duplicate Sheet Name: "+e);if(r){var i=a&&a[n]&&a[n].CodeName||e;if(95==i.charCodeAt(0)&&i.length>22)throw Error("Bad Code Name: Worksheet"+i)}});for(var n=0;n<e.SheetNames.length;++n)!function(e,t,r){if(e&&e["!ref"]){var a=rs(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw Error("Bad range ("+r+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[n]],e.SheetNames[n],n)}(t);var a,n,s=eU(r||{});if(s.cellStyles&&(s.cellNF=!0,s.sheetStubs=!0),"array"==s.type){s.type="binary";var i=e(t,s);return s.type="array",O(i)}var o=0;if(s.sheet&&(o="number"==typeof s.sheet?s.sheet:t.SheetNames.indexOf(s.sheet),!t.SheetNames[o]))throw Error("Sheet not found: "+s.sheet+" : "+typeof s.sheet);switch(s.bookType||"xlsb"){case"xml":case"xlml":return s2(function(e,t){t||(t={}),e.SSF||(e.SSF=eU(j)),e.SSF&&(eT(),ev(e.SSF),t.revssf=ex(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],se(t.cellXfs,{},{revssf:{General:0}}));var r,a,n,s,i,o,l,c,f,h,u=[];u.push((r=t,a=[],e.Props&&a.push((n=e.Props,s=[],ey(r1).map(function(e){for(var t=0;t<r$.length;++t)if(r$[t][1]==e)return r$[t];for(t=0;t<rJ.length;++t)if(rJ[t][1]==e)return rJ[t];throw e}).forEach(function(e){if(null!=n[e[1]]){var t=r&&r.Props&&null!=r.Props[e[1]]?r.Props[e[1]]:n[e[1]];"date"===e[2]&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof t?t=String(t):!0===t||!1===t?t=t?"1":"0":t instanceof Date&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"")),s.push(th(r1[e[1]]||e[1],t))}}),td("DocumentProperties",s.join(""),{xmlns:tb.o}))),e.Custprops&&a.push((i=e.Props,o=e.Custprops,l=["Worksheets","SheetNames"],c="CustomDocumentProperties",f=[],i&&ey(i).forEach(function(e){if(Object.prototype.hasOwnProperty.call(i,e)){for(var t=0;t<r$.length;++t)if(e==r$[t][1])return;for(t=0;t<rJ.length;++t)if(e==rJ[t][1])return;for(t=0;t<l.length;++t)if(e==l[t])return;var r=i[e],a="string";"number"==typeof r?(a="float",r=String(r)):!0===r||!1===r?(a="boolean",r=r?"1":"0"):r=String(r),f.push(td(e6(e),r,{"dt:dt":a}))}}),o&&ey(o).forEach(function(e){if(Object.prototype.hasOwnProperty.call(o,e)&&!(i&&Object.prototype.hasOwnProperty.call(i,e))){var t=o[e],r="string";"number"==typeof t?(r="float",t=String(t)):!0===t||!1===t?(r="boolean",t=t?"1":"0"):t instanceof Date?(r="dateTime.tz",t=t.toISOString()):t=String(t),f.push(td(e6(e),t,{"dt:dt":r}))}}),"<"+c+' xmlns="'+tb.o+'">'+f.join("")+"</"+c+">")),a.join(""))),u.push(""),u.push(""),u.push("");for(var d=0;d<e.SheetNames.length;++d)u.push(td("Worksheet",function(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(sm(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),(i=s?function(e,t,r,a){if(!e["!ref"])return"";var n=rs(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach(function(e,t){ns(e);var r=!!e.width,a=n7(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=nt(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push(td("Column",null,n))});for(var l=Array.isArray(e),c=n.s.r;c<=n.e.r;++c){for(var f=[function(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=no(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}(c,(e["!rows"]||[])[c])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>c)&&!(s[i].e.c<h)&&!(s[i].e.r<c)){(s[i].s.c!=h||s[i].s.r!=c)&&(u=!0);break}if(!u){var d={r:c,c:h},p=rr(d),m=l?(e[c]||[])[h]:e[p];f.push(function(e,t,r,a,n,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+e5(nR(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var l=rt(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==i.r?"":"["+(l.r-i.r)+"]")+"C"+(l.c==i.c?"":"["+(l.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=e5(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=e5(e.l.Tooltip))),r["!merges"])for(var c=r["!merges"],f=0;f!=c.length;++f)c[f].s.c==i.c&&c[f].s.r==i.r&&(c[f].e.c>c[f].s.c&&(o["ss:MergeAcross"]=c[f].e.c-c[f].s.c),c[f].e.r>c[f].s.r&&(o["ss:MergeDown"]=c[f].e.r-c[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=rM[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||j[14]);break;case"s":h="String",u=((e.v||"")+"").replace(e4,function(e){return e2[e]}).replace(e8,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var d=se(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map(function(e){var t=td("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return td("Comment",t,{"ss:Author":e.a})}).join("")),td("Cell",m,o)}(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(s,t,0,0):"").length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(td("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(td("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(td("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(td("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return(((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(th("ProtectContents","True")),e["!protect"].objects&&n.push(th("ProtectObjects","True")),e["!protect"].scenarios&&n.push(th("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(th("EnableSelection","UnlockedCells")):n.push(th("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")})),0==n.length)?"":td("WorksheetOptions",n.join(""),{xmlns:tb.x})}(s,0,e,r)),a.join("")}(d,t,e),{"ss:Name":e5(e.SheetNames[d])}));return u[2]=(h=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var r=[];r.push(td("NumberFormat",null,{"ss:Format":e5(j[e.numFmtId])})),h.push(td("Style",r.join(""),{"ss:ID":"s"+(21+t)}))}),td("Styles",h.join(""))),u[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(sm(n)))}return td("Names",r.join(""))}(e,t),eJ+td("Workbook",u.join(""),{xmlns:tb.ss,"xmlns:o":tb.o,"xmlns:x":tb.x,"xmlns:ss":tb.ss,"xmlns:dt":tb.dt,"xmlns:html":tb.html})}(t,s),s);case"slk":case"sylk":return s2(az.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"htm":case"html":return s2(sA(t.Sheets[t.SheetNames[o]],s),s);case"txt":var c=s7(t.Sheets[t.SheetNames[o]],s);switch(s.type){case"base64":return S(c);case"binary":case"string":return c;case"file":return eS(s.file,c,"binary");case"buffer":if(_)return A(c,"binary");return c.split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+s.type);case"csv":return s2(s8(t.Sheets[t.SheetNames[o]],s),s,"\uFEFF");case"dif":return s2(aK.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"dbf":return s4(aV.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"prn":return s2(aY.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"rtf":return s2(a7.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"eth":return s2(a$.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"fods":return s2(sD(t,s),s);case"wk1":return s4(aX.sheet_to_wk1(t.Sheets[t.SheetNames[o]],s),s);case"wk3":return s4(aX.book_to_wk3(t,s),s);case"biff2":s.biff||(s.biff=2);case"biff3":s.biff||(s.biff=3);case"biff4":return s.biff||(s.biff=4),s4(sy(t,s),s);case"biff5":s.biff||(s.biff=5);case"biff8":case"xla":case"xls":return s.biff||(s.biff=8),s0(function(e,t){var r,a=t||{},n=eE.utils.cfb_new({root:"R"}),s="/Workbook";switch(a.bookType||"xls"){case"xls":a.bookType="biff8";case"xla":a.bookType||(a.bookType="xla");case"biff8":s="/Workbook",a.biff=8;break;case"biff5":s="/Book",a.biff=5;break;default:throw Error("invalid type "+a.bookType+" for XLS CFB")}return eE.utils.cfb_add(n,s,sy(e,a)),8==a.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=e_(rR,"n"),l=e_(rI,"n");if(e.Props)for(i=0,r=ey(e.Props);i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(l,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(i=0,r=ey(e.Custprops);i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(l,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var c=[];for(i=0;i<s.length;++i)at.indexOf(s[i][0])>-1||rq.indexOf(s[i][0])>-1||null!=s[i][1]&&c.push(s[i]);n.length&&eE.utils.cfb_add(t,"/\x05SummaryInformation",an(n,sT.SI,l,rI)),(a.length||c.length)&&eE.utils.cfb_add(t,"/\x05DocumentSummaryInformation",an(a,sT.DSI,o,rR,c.length?c:null,sT.UDI))}(e,n),8==a.biff&&e.vbaraw&&(r=eE.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})).FullPaths.forEach(function(e,t){if(0!=t){var a=e.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&eE.utils.cfb_add(n,a,r.FileIndex[t].content)}}),n}(t,a=s||{}),a);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r={},a=_?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw Error("Unrecognized type "+t.type)}var n=e.FullPaths?eE.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(O(n))}return t.password&&"undefined"!=typeof encrypt_agile?s0(encrypt_agile(n,t.password),t):"file"===t.type?eS(t.file,n):"string"==t.type?tn(n):n}("ods"==(n=eU(s||{})).bookType?sD(t,n):"numbers"==n.bookType?function(e,t){if(!t||!t.numbers)throw Error("Must pass a `numbers` option -- check the README");var r,a=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=ra(a["!ref"]);n.s.r=n.s.c=0;var s=!1;n.e.c>9&&(s=!0,n.e.c=9),n.e.r>49&&(s=!0,n.e.r=49),s&&console.error("The Numbers writer is currently limited to ".concat(rn(n)));var i=s5(a,{range:n,header:1}),o=["~Sh33tJ5~"];i.forEach(function(e){return e.forEach(function(e){"string"==typeof e&&o.push(e)})});var l={},c=[],f=eE.read(t.numbers,{type:"base64"});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&sV(sK(t.content)).forEach(function(e){c.push(e.id),l[e.id]={deps:[],location:r,type:sH(e.messages[0].meta[1][0].data)}})}),c.sort(function(e,t){return e-t});var h=c.filter(function(e){return e>1}).map(function(e){return[e,sB(e)]});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&sV(sK(t.content)).forEach(function(e){e.messages.forEach(function(t){h.forEach(function(t){e.messages.some(function(e){return 11006!=sH(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}(e.data,t[1])})&&l[t[0]].deps.push(e.id)})})})});for(var u=eE.find(f,l[1].location),d=sV(sK(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(r=m)}var g=sJ(sW(r.messages[0].data)[1][0].data);for(p=0,d=sV(sK((u=eE.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sJ(sW(r.messages[0].data)[2][0].data),d=sV(sK((u=eE.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sJ(sW(r.messages[0].data)[2][0].data),d=sV(sK((u=eE.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);var v=sW(r.messages[0].data);v[6][0].data=sB(n.e.r+1),v[7][0].data=sB(n.e.c+1);for(var T=sJ(v[46][0].data),b=eE.find(f,l[T].location),w=sV(sK(b.content)),E=0;E<w.length&&w[E].id!=T;++E);if(w[E].id!=T)throw"Bad ColumnRowUIDMapArchive";var S=sW(w[E].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var y=0;y<=n.e.c;++y){var _=[];_[1]=_[2]=[{type:0,data:sB(y+420690)}],S[1].push({type:2,data:sj(_)}),S[2].push({type:0,data:sB(y)}),S[3].push({type:0,data:sB(y)})}S[4]=[],S[5]=[],S[6]=[];for(var A=0;A<=n.e.r;++A)(_=[])[1]=_[2]=[{type:0,data:sB(A+726270)}],S[4].push({type:2,data:sj(_)}),S[5].push({type:0,data:sB(A)}),S[6].push({type:0,data:sB(A)});w[E].messages[0].data=sj(S),b.content=s$(sz(w)),b.size=b.content.length,delete v[46];var x=sW(v[4][0].data);x[7][0].data=sB(n.e.r+1);var k=sJ(sW(x[1][0].data)[2][0].data);if((w=sV(sK((b=eE.find(f,l[k].location)).content)))[0].id!=k)throw"Bad HeaderStorageBucket";var O=sW(w[0].messages[0].data);for(A=0;A<i.length;++A){var R=sW(O[2][0].data);R[1][0].data=sB(A),R[4][0].data=sB(i[A].length),O[2][A]={type:O[2][0].type,data:sj(R)}}w[0].messages[0].data=sj(O),b.content=s$(sz(w)),b.size=b.content.length;var I=sJ(x[2][0].data);if((w=sV(sK((b=eE.find(f,l[I].location)).content)))[0].id!=I)throw"Bad HeaderStorageBucket";for(y=0,O=sW(w[0].messages[0].data);y<=n.e.c;++y)(R=sW(O[2][0].data))[1][0].data=sB(y),R[4][0].data=sB(n.e.r+1),O[2][y]={type:O[2][0].type,data:sj(R)};w[0].messages[0].data=sj(O),b.content=s$(sz(w)),b.size=b.content.length;var N=sJ(x[4][0].data);!function(){for(var e,t=eE.find(f,l[N].location),r=sV(sK(t.content)),a=0;a<r.length;++a){var n=r[a];n.id==N&&(e=n)}var s=sW(e.messages[0].data);s[3]=[];var i=[];o.forEach(function(e,t){i[1]=[{type:0,data:sB(t)}],i[2]=[{type:0,data:sB(1)}],i[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(e):C(ts(e))}],s[3].push({type:2,data:sj(i)})}),e.messages[0].data=sj(s),t.content=s$(sz(r)),t.size=t.content.length}();var D=sW(x[3][0].data),P=D[1][0];delete D[2];var M=sW(P.data),L=sJ(M[2][0].data);!function(){for(var e,t=eE.find(f,l[L].location),r=sV(sK(t.content)),a=0;a<r.length;++a){var s=r[a];s.id==L&&(e=s)}var c=sW(e.messages[0].data);delete c[6],delete D[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=sW(h);u+=function(e,t,r){if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&sH(e[8][0].data)>0)throw"Math only works with normal offsets";for(var a,n,s,i,o,l,c=0,f=sP(e[7][0].data),h=0,u=[],d=sP(e[4][0].data),p=0,m=[],g=0;g<t.length;++g){if(null==t[g]){f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);continue}switch(f.setUint16(2*g,h,!0),d.setUint16(2*g,p,!0),typeof t[g]){case"string":o=sY({t:"s",v:t[g]},r),l=sX({t:"s",v:t[g]},r);break;case"number":o=sY({t:"n",v:t[g]},r),l=sX({t:"n",v:t[g]},r);break;case"boolean":o=sY({t:"b",v:t[g]},r),l=sX({t:"b",v:t[g]},r);break;default:throw Error("Unsupported value "+t[g])}u.push(o),h+=o.length,m.push(l),p+=l.length,++c}for(e[2][0].data=sB(c);g<e[7][0].data.length/2;++g)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return e[6][0].data=sL(u),e[3][0].data=sL(m),c}(p,i[d],o),p[1][0].data=sB(d),c[5].push({data:sj(p),type:2})}c[1]=[{type:0,data:sB(n.e.c+1)}],c[2]=[{type:0,data:sB(n.e.r+1)}],c[3]=[{type:0,data:sB(u)}],c[4]=[{type:0,data:sB(n.e.r+1)}],e.messages[0].data=sj(c),t.content=s$(sz(r)),t.size=t.content.length}(),P.data=sj(M),x[3][0].data=sj(D),v[4][0].data=sj(x),r.messages[0].data=sj(v);var F=s$(sz(d));return u.content=F,u.size=u.content.length,f}(t,n):"xlsb"==n.bookType?function(e,t){n_=1024,e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eT(),ev(e.SSF),t.revssf=ex(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,n6?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a,n,s,i,o,c,f="xlsb"==t.bookType?"bin":"xml",h=nk.indexOf(t.bookType)>-1,u=rB();s1(t=t||{});var d=eX(),p="",m=0;if(t.cellXfs=[],se(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eY(d,p="docProps/core.xml",rX(e.Props,t)),u.coreprops.push(p),rV(t.rels,2,p,rW.CORE_PROPS),p="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var g=[],v=0;v<e.SheetNames.length;++v)2!=(e.Workbook.Sheets[v]||{}).Hidden&&g.push(e.SheetNames[v]);e.Props.SheetNames=g}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,eY(d,p,rZ(e.Props,t)),u.extprops.push(p),rV(t.rels,3,p,rW.EXT_PROPS),e.Custprops!==e.Props&&ey(e.Custprops||{}).length>0&&(eY(d,p="docProps/custom.xml",rQ(e.Custprops,t)),u.custprops.push(p),rV(t.rels,4,p,rW.CUST_PROPS)),m=1;m<=e.SheetNames.length;++m){var T={"!id":{}},b=e.Sheets[e.SheetNames[m-1]];if((b||{})["!type"],eY(d,p="xl/worksheets/sheet"+m+"."+f,(w=m-1,E=p,S=t,(".bin"===E.slice(-4)?function(e,t,r,a){var n,s,i,o,l,c,f,h,u,d=t0(),p=r.SheetNames[e],m=r.Sheets[p]||{},g=p;try{r&&r.Workbook&&(g=r.Workbook.Sheets[e].CodeName||g)}catch(e){}var v=rs(m["!ref"]||"A1");if(v.e.c>16383||v.e.r>1048575){if(t.WTF)throw Error("Range "+(m["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");v.e.c=Math.min(v.e.c,16383),v.e.r=Math.min(v.e.c,1048575)}m["!links"]=[],m["!comments"]=[],t2(d,129),(r.vbaraw||m["!outline"])&&t2(d,147,function(e,t,r){null==r&&(r=t1(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return rk({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),rd(e,r),r.slice(0,r.l)}(g,m["!outline"])),t2(d,148,r_(v)),n=r.Workbook,t2(d,133),t2(d,137,(null==s&&(s=t1(30)),i=924,(((n||{}).Views||[])[0]||{}).RTL&&(i|=32),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(1,0),s.write_shift(1,0),s.write_shift(2,0),s.write_shift(2,100),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s)),t2(d,138),t2(d,134),m&&m["!cols"]&&(t2(d,390),m["!cols"].forEach(function(e,t){var r,a,n;e&&t2(d,60,(null==r&&(r=t1(18)),a=n7(t,e),r.write_shift(-4,t),r.write_shift(-4,t),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0),n=0,e.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),e.level&&(n|=e.level<<8),r.write_shift(2,n),r))}),t2(d,391)),function(e,t,r,a){var n,s=rs(t["!ref"]||"A1"),i="",o=[];t2(e,145);var l=Array.isArray(t),c=s.e.r;t["!rows"]&&(c=Math.max(s.e.r,t["!rows"].length-1));for(var f=s.s.r;f<=c;++f){i=t7(f),function(e,t,r,a){var n=function(e,t,r){var a=t1(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*ni(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,l=a.l;a.l+=4;for(var c={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10)&&!(t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)c.c=d,(Array.isArray(r)?(r[c.r]||[])[c.c]:r[rr(c)])&&(h<0&&(h=d),u=d);h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var p=a.l;return a.l=l,a.write_shift(4,o),a.l=p,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&t2(e,0,n)}(e,t,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=re(u)),n=o[u]+i;var d=l?(t[f]||[])[u]:t[n];if(!d){h=!1;continue}h=function(e,t,r,a,n,s,i){if(void 0===t.v)return!1;var o,l,c,f,h,u,d,p,m,g,v,T,b,w,E,S,y,_,A,x,k,C,O,R,I="";switch(t.t){case"b":I=t.v?"1":"0";break;case"d":(t=eU(t)).z=t.z||j[14],t.v=eC(eL(t.v)),t.t="n";break;case"n":case"e":I=""+t.v;break;default:I=t.v}var N={r:r,c:a};switch(N.s=se(n.cellXfs,t,n),t.l&&s["!links"].push([rr(N),t.l]),t.c&&s["!comments"].push([rr(N),t.c]),t.t){case"s":case"str":return n.bookSST?(I=n8(n.Strings,t.v,n.revStrings),N.t="s",N.v=I,i)?t2(e,18,(null==o&&(o=t1(8)),rT(N,o),o.write_shift(4,N.v),o)):t2(e,7,(null==l&&(l=t1(12)),rg(N,l),l.write_shift(4,N.v),l)):(N.t="str",i)?t2(e,17,(c=t,null==f&&(f=t1(8+4*c.v.length)),rT(N,f),rd(c.v,f),f.length>f.l?f.slice(0,f.l):f)):t2(e,6,(h=t,null==u&&(u=t1(12+4*h.v.length)),rg(N,u),rd(h.v,u),u.length>u.l?u.slice(0,u.l):u)),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?t2(e,13,(d=t,null==p&&(p=t1(8)),rT(N,p),rS(d.v,p),p)):t2(e,2,(m=t,null==g&&(g=t1(12)),rg(N,g),rS(m.v,g),g)):i?t2(e,16,(v=t,null==T&&(T=t1(12)),rT(N,T),rx(v.v,T),T)):t2(e,5,(b=t,null==w&&(w=t1(16)),rg(N,w),rx(b.v,w),w)),!0;case"b":return(N.t="b",i)?t2(e,15,(E=t,null==S&&(S=t1(5)),rT(N,S),S.write_shift(1,+!!E.v),S)):t2(e,4,(y=t,null==_&&(_=t1(9)),rg(N,_),_.write_shift(1,+!!y.v),_)),!0;case"e":return(N.t="e",i)?t2(e,14,(A=t,null==x&&(x=t1(8)),rT(N,x),x.write_shift(1,A.v),x.write_shift(2,0),x.write_shift(1,0),x)):t2(e,3,(k=t,null==C&&(C=t1(9)),rg(N,C),C.write_shift(1,k.v),C)),!0}return i?t2(e,12,(null==O&&(O=t1(4)),rT(N,O))):t2(e,1,(null==R&&(R=t1(8)),rg(N,R))),!0}(e,d,f,u,a,t,h)}}t2(e,146)}(d,m,0,t,r);m["!protect"]&&t2(d,535,(o=m["!protect"],null==l&&(l=t1(66)),l.write_shift(2,o.password?a3(o.password):0),l.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?l.write_shift(4,+(null!=o[e[0]]&&!o[e[0]])):l.write_shift(4,null!=o[e[0]]&&o[e[0]]?0:1)}),l)),!function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"==typeof n.ref?n.ref:rn(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=ra(s);o.s.r==o.e.r&&(o.e.r=ra(t["!ref"]).e.r,s=rn(o));for(var l=0;l<i.length;++l){var c=i[l];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+s;break}}l==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),t2(e,161,r_(rs(s))),t2(e,162)}}(d,m,r,e);m&&m["!merges"]&&(t2(d,177,(c=m["!merges"].length,null==f&&(f=t1(4)),f.write_shift(4,c),f)),m["!merges"].forEach(function(e){t2(d,176,r_(e))}),t2(d,178)),m["!links"].forEach(function(e){if(e[1].Target){var t,r,n=rV(a,-1,e[1].Target.replace(/#.*$/,""),rW.HLINK);t2(d,494,(t=t1(50+4*(e[1].Target.length+(e[1].Tooltip||"").length)),r_({s:rt(e[0]),e:rt(e[0])},t),rw("rId"+n,t),rd((-1==(r=e[1].Target.indexOf("#"))?"":e[1].Target.slice(r+1))||"",t),rd(e[1].Tooltip||"",t),rd("",t),t.slice(0,t.l)))}}),delete m["!links"],m["!margins"]&&t2(d,476,(h=m["!margins"],null==u&&(u=t1(48)),n9(h),sn.forEach(function(e){rx(h[e],u)}),u)),(!t||t.ignoreEC||void 0==t.ignoreEC)&&function(e,t){if(t&&t["!ref"]){var r,a;t2(e,648),t2(e,649,(r=rs(t["!ref"]),(a=t1(24)).write_shift(4,4),a.write_shift(4,1),r_(r,a),a)),t2(e,650)}}(d,m);if(m["!comments"].length>0){var T=rV(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rW.VML);t2(d,551,rw("rId"+T)),m["!legacy"]=T}return t2(d,130),d.end()}:sa)(w,S,e,T))),u.sheets.push(p),rV(t.wbrels,-1,"worksheets/sheet"+m+"."+f,rW.WS[0]),b){var w,E,S,y,_,A=b["!comments"],x=!1,k="";A&&A.length>0&&(eY(d,k="xl/comments"+m+"."+f,(y=k,_=t,(".bin"===y.slice(-4)?function(e){var t=t0(),r=[];return t2(t,628),t2(t,630),e.forEach(function(e){e[1].forEach(function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),t2(t,632,rd(e.a.slice(0,54))))})}),t2(t,631),t2(t,633),e.forEach(function(e){e[1].forEach(function(a){var n,s,i,o,l,c;a.iauthor=r.indexOf(a.a),t2(t,635,(n=[{s:rt(e[0]),e:rt(e[0])},a],null==s&&(s=t1(36)),s.write_shift(4,n[1].iauthor),r_(n[0],s),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s)),a.t&&a.t.length>0&&t2(t,637,(o=!1,null==i&&(o=!0,i=t1(23+4*a.t.length)),i.write_shift(1,1),rd(a.t,i),i.write_shift(4,1),l={ich:0,ifnt:0},(c=i)||(c=t1(4)),c.write_shift(2,l.ich||0),c.write_shift(2,l.ifnt||0),o?i.slice(0,i.l):i)),t2(t,636),delete a.iauthor})}),t2(t,634),t2(t,629),t.end()}:nx)(A,_))),u.comments.push(k),rV(T,-1,"../comments"+m+"."+f,rW.CMNT),x=!0),b["!legacy"]&&x&&eY(d,"xl/drawings/vmlDrawing"+m+".vml",nA(m,b["!comments"])),delete b["!comments"],delete b["!legacy"]}T["!id"].rId1&&eY(d,rj(p),rG(T))}return null!=t.Strings&&t.Strings.length>0&&(eY(d,p="xl/sharedStrings."+f,(r=t.Strings,a=p,n=t,(".bin"===a.slice(-4)?function(e){var t,r=t0();t2(r,159,(t||(t=t1(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t));for(var a=0;a<e.length;++a)t2(r,19,a0(e[a]));return t2(r,160),r.end()}:a1)(r,n))),u.strs.push(p),rV(t.wbrels,-1,"sharedStrings."+f,rW.SST)),eY(d,p="xl/workbook."+f,(s=p,i=t,(".bin"===s.slice(-4)?function(e,t){var r=t0();t2(r,131),t2(r,128,function(e,t){t||(t=t1(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return rd("SheetJS",t),rd(l.version,t),rd(l.version,t),rd("7262",t),t.length>t.l?t.slice(0,t.l):t}()),t2(r,153,(n=e.Workbook&&e.Workbook.WBProps||null,s||(s=t1(72)),i=0,n&&n.filterPrivacy&&(i|=8),s.write_shift(4,i),s.write_shift(4,0),rd(n&&n.CodeName||"ThisWorkbook",s),s.slice(0,s.l))),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)n[s]&&(n[s].Hidden||-1!=i)?1==n[s].Hidden&&-1==o&&(o=s):i=s;o>i||(t2(e,135),t2(e,158,(r=i,a||(a=t1(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),t2(e,136))}}(r,e,t);t2(r,143);for(var a=0;a!=e.SheetNames.length;++a){var n,s,i,o={Hidden:e.Workbook&&e.Workbook.Sheets&&e.Workbook.Sheets[a]&&e.Workbook.Sheets[a].Hidden||0,iTabID:a+1,strRelID:"rId"+(a+1),name:e.SheetNames[a]},c=void 0;t2(r,156,(c||(c=t1(127)),c.write_shift(4,o.Hidden),c.write_shift(4,o.iTabID),rw(o.strRelID,c),rd(o.name.slice(0,31),c),c.length>c.l?c.slice(0,c.l):c))}return t2(r,144),t2(r,132),r.end()}:sf)(e,i))),u.workbooks.push(p),rV(t.rels,1,p,rW.WB),eY(d,p="xl/theme/theme1.xml",nS(e.Themes,t)),u.themes.push(p),rV(t.wbrels,-1,"theme/theme1.xml",rW.THEME),eY(d,p="xl/styles."+f,(o=p,c=t,(".bin"===o.slice(-4)?function(e,t){var r,a,n,s,i,o,l,c=t0();return t2(c,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r}),0!=r&&(t2(e,615,rh(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&t2(e,44,function(e,t,r){r||(r=t1(6+4*t.length)),r.write_shift(2,e),rd(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}(a,t[a]))}),t2(e,616))}}(c,e.SSF),function(e){var t,r,a,n,s,i;t2(e,611,rh(1)),t2(e,43,(t={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},r||(r=t1(153)),r.write_shift(2,20*t.sz),(a=r)||(a=t1(2)),n=2*!!t.italic|8*!!t.strike|16*!!t.outline|32*!!t.shadow|64*!!t.condense|128*!!t.extend,a.write_shift(1,n),a.write_shift(1,0),r.write_shift(2,t.bold?700:400),s=0,"superscript"==t.vertAlign?s=1:"subscript"==t.vertAlign&&(s=2),r.write_shift(2,s),r.write_shift(1,t.underline||0),r.write_shift(1,t.family||0),r.write_shift(1,t.charset||0),r.write_shift(1,0),rk(t.color,r),i=0,"major"==t.scheme&&(i=1),"minor"==t.scheme&&(i=2),r.write_shift(1,i),rd(t.name,r),r.length>r.l?r.slice(0,r.l):r)),t2(e,612)}(c,e),t2(c,603,rh(2)),t2(c,45,nh({patternType:"none"})),t2(c,45,nh({patternType:"gray125"})),t2(c,604),t2(c,613,rh(1)),t2(c,46,(r||(r=t1(51)),r.write_shift(1,0),nd(null,r),nd(null,r),nd(null,r),nd(null,r),nd(null,r),r.length>r.l?r.slice(0,r.l):r)),t2(c,614),t2(c,626,rh(1)),t2(c,47,nu({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),t2(c,627),t2(c,617,rh((a=t.cellXfs).length)),a.forEach(function(e){t2(c,47,nu(e,0))}),t2(c,618),t2(c,619,rh(1)),t2(c,48,(n={xfId:0,builtinId:0,name:"Normal"},s||(s=t1(52)),s.write_shift(4,n.xfId),s.write_shift(2,1),s.write_shift(1,+n.builtinId),s.write_shift(1,0),rw(n.name||"",s),s.length>s.l?s.slice(0,s.l):s)),t2(c,620),t2(c,505,rh(0)),t2(c,506),t2(c,508,(i="TableStyleMedium9",o="PivotStyleMedium4",(l=t1(2052)).write_shift(4,0),rw(i,l),rw(o,l),l.length>l.l?l.slice(0,l.l):l)),t2(c,509),t2(c,279),c.end()}:nc)(e,c))),u.styles.push(p),rV(t.wbrels,-1,"styles."+f,rW.STY),e.vbaraw&&h&&(eY(d,p="xl/vbaProject.bin",e.vbaraw),u.vba.push(p),rV(t.wbrels,-1,"vbaProject.bin",rW.VBA)),eY(d,p="xl/metadata."+f,(".bin"===p.slice(-4)?function(){var e,t,r,a,n,s=t0();return t2(s,332),t2(s,334,rh(1)),t2(s,335,((t=t1(12+2*(e={name:"XLDAPR",version:12e4,flags:0xd06ac0b0}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),rd(e.name,t),t.slice(0,t.l))),t2(s,336),t2(s,339,((a=t1(8+2*(r="XLDAPR").length)).write_shift(4,1),rd(r,a),a.slice(0,a.l))),t2(s,52),t2(s,35,rh(514)),t2(s,4096,rh(0)),t2(s,4097,ac(1)),t2(s,36),t2(s,53),t2(s,340),t2(s,337,((n=t1(8)).write_shift(4,1),n.write_shift(4,1),n)),t2(s,51,function(e){var t=t1(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),t2(s,338),t2(s,333),s.end()}:ny)()),u.metadata.push(p),rV(t.wbrels,-1,"metadata."+f,rW.XLMETA),eY(d,"[Content_Types].xml",rH(u,t)),eY(d,"_rels/.rels",rG(t.rels)),eY(d,"xl/_rels/workbook."+f+".rels",rG(t.wbrels)),delete t.revssf,delete t.ssf,d}(t,n):function(e,t){n_=1024,e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eT(),ev(e.SSF),t.revssf=ex(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,n6?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a=nk.indexOf(t.bookType)>-1,n=rB();s1(t=t||{});var s=eX(),i="",o=0;if(t.cellXfs=[],se(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eY(s,i="docProps/core.xml",rX(e.Props,t)),n.coreprops.push(i),rV(t.rels,2,i,rW.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&l.push(e.SheetNames[c]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,eY(s,i,rZ(e.Props,t)),n.extprops.push(i),rV(t.rels,3,i,rW.EXT_PROPS),e.Custprops!==e.Props&&ey(e.Custprops||{}).length>0&&(eY(s,i="docProps/custom.xml",rQ(e.Custprops,t)),n.custprops.push(i),rV(t.rels,4,i,rW.CUST_PROPS));var f=["SheetJ5"];for(o=1,t.tcid=0;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];if((u||{})["!type"],eY(s,i="xl/worksheets/sheet"+o+".xml",sa(o-1,t,e,h)),n.sheets.push(i),rV(t.wbrels,-1,"worksheets/sheet"+o+".xml",rW.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach(function(e){e[1].forEach(function(e){!0==e.T&&(g=!0)})}),g&&(eY(s,m="xl/threadedComments/threadedComment"+o+".xml",function(e,t,r){var a=[eJ,td("ThreadedComments",null,{xmlns:tv.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(e){var n="";(e[1]||[]).forEach(function(s,i){if(!s.T)return void delete s.ID;s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(td("threadedComment",th("text",s.t||""),o))})}),a.push("</ThreadedComments>"),a.join("")}(d,f,t)),n.threadedcomments.push(m),rV(h,-1,"../threadedComments/threadedComment"+o+".xml",rW.TCMNT)),eY(s,m="xl/comments"+o+".xml",nx(d,t)),n.comments.push(m),rV(h,-1,"../comments"+o+".xml",rW.CMNT),p=!0}u["!legacy"]&&p&&eY(s,"xl/drawings/vmlDrawing"+o+".vml",nA(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&eY(s,rj(i),rG(h))}return null!=t.Strings&&t.Strings.length>0&&(eY(s,i="xl/sharedStrings.xml",a1(t.Strings,t)),n.strs.push(i),rV(t.wbrels,-1,"sharedStrings.xml",rW.SST)),eY(s,i="xl/workbook.xml",sf(e,t)),n.workbooks.push(i),rV(t.rels,1,i,rW.WB),eY(s,i="xl/theme/theme1.xml",nS(e.Themes,t)),n.themes.push(i),rV(t.wbrels,-1,"theme/theme1.xml",rW.THEME),eY(s,i="xl/styles.xml",nc(e,t)),n.styles.push(i),rV(t.wbrels,-1,"styles.xml",rW.STY),e.vbaraw&&a&&(eY(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),rV(t.wbrels,-1,"vbaProject.bin",rW.VBA)),eY(s,i="xl/metadata.xml",ny()),n.metadata.push(i),rV(t.wbrels,-1,"metadata.xml",rW.XLMETA),f.length>1&&(eY(s,i="xl/persons/person.xml",(r=[eJ,td("personList",null,{xmlns:tv.TCMNT,"xmlns:x":"http://schemas.openxmlformats.org/spreadsheetml/2006/main"}).replace(/[\/]>/,">")],f.forEach(function(e,t){r.push(td("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))}),r.push("</personList>"),r.join(""))),n.people.push(i),rV(t.wbrels,-1,"persons/person.xml",rW.PEOPLE)),eY(s,"[Content_Types].xml",rH(n,t)),eY(s,"_rels/.rels",rG(t.rels)),eY(s,"xl/_rels/workbook.xml.rels",rG(t.wbrels)),delete t.revssf,delete t.ssf,s}(t,n),n);default:throw Error("Unrecognized bookType |"+s.bookType+"|")}}(e,a)}function s5(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},f=null!=c.range?c.range:e["!ref"];switch(1===c.header?a=1:"A"===c.header?a=2:Array.isArray(c.header)?a=3:null==c.header&&(a=0),typeof f){case"string":l=rs(f);break;case"number":(l=rs(e["!ref"])).s.r=f;break;default:l=f}a>0&&(n=0);var h=t7(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,b={};g&&!e[v]&&(e[v]=[]);var w=c.skipHidden&&e["!cols"]||[],E=c.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(w[T]||{}).hidden)switch(u[T]=re(T),r=g?e[v][T]:e[u[T]+h],a){case 1:s[T]=T-l.s.c;break;case 2:s[T]=u[T];break;case 3:s[T]=c.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=ro(r,null,c),m=b[i]||0){do o=i+"_"+m++;while(b[o]);b[i]=m,b[o]=1}else b[i]=1;s[T]=o}for(v=l.s.r+n;v<=l.e.r;++v)if(!(E[v]||{}).hidden){var S=function(e,t,r,a,n,s,i,o){var l=t7(r),c=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+l];if(void 0===p||void 0===p.t){if(void 0===c)continue;null!=s[d]&&(u[s[d]]=c);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==c)u[s[d]]=c;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:ro(p,m,o);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,l,v,u,a,s,g,c);(!1===S.isempty||(1===a?!1!==c.blankrows:c.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var s6=/"/g;function s8(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=rs(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",l=o.charCodeAt(0),c=RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=re(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)!(d[g]||{}).hidden&&null!=(f=function(e,t,r,a,n,s,i,o){for(var l=!0,c=[],f="",h=t7(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=o.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){l=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:ro(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(s6,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(l=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(s6,'""')+'"'));c.push(f)}return!1===o.blankrows&&l?null:c.join(i)}(e,n,g,h,i,l,s,a))&&(a.strip&&(f=f.replace(c,"")),(f||!1!==a.blankrows)&&r.push((m++?o:"")+f));return delete a.dense,r.join("")}function s7(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=s8(e,t);if(void 0===n||"string"==t.type)return r;var a=n.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function s9(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},o=0,l=0;if(i&&null!=n.origin)if("number"==typeof n.origin)o=n.origin;else{var c="string"==typeof n.origin?rt(n.origin):n.origin;o=c.r,l=c.c}var f={s:{c:0,r:0},e:{c:l,r:o+t.length-1+s}};if(i["!ref"]){var h=rs(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else -1==o&&(o=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach(function(e,t){ey(e).forEach(function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var c=e[r],f="z",h="",p=rr({c:l+d,r:o+t+s});a=ie(i,p),!c||"object"!=typeof c||c instanceof Date?("number"==typeof c?f="n":"boolean"==typeof c?f="b":"string"==typeof c?f="s":c instanceof Date?(f="d",n.cellDates||(f="n",c=eC(c)),h=n.dateNF||j[14]):null===c&&n.nullError&&(f="e",c=0),a?(a.t=f,a.v=c,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:c},h&&(a.z=h)):i[p]=c})}),f.e.c=Math.max(f.e.c,l+u.length-1);var p=t7(o);if(s)for(d=0;d<u.length;++d)i[re(d+l)+p]={t:"s",v:u[d]};return i["!ref"]=rn(f),i}function ie(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=rt(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return"number"!=typeof t?ie(e,rr(t)):ie(e,rr({r:t,c:r||0}))}function it(){return{SheetNames:[],Sheets:{}}}function ir(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(sc(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function ia(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var is={encode_col:re,encode_row:t7,encode_cell:rr,encode_range:rn,decode_col:t9,decode_row:t8,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:rt,decode_range:ra,format_cell:ro,sheet_add_aoa:rc,sheet_add_json:s9,sheet_add_dom:sx,aoa_to_sheet:rf,json_to_sheet:function(e,t){return s9(null,e,t)},table_to_sheet:sk,table_to_book:function(e,t){return rl(sk(e,t),t)},sheet_to_csv:s8,sheet_to_txt:s7,sheet_to_json:s5,sheet_to_html:sA,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=rs(e["!ref"]),i="",o=[],l=[],c=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=re(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=t7(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,t=c?(e[f]||[])[n]:e[r],a="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else if("z"==t.t)continue;else if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}l[l.length]=r+"="+a}return l},sheet_to_row_object_array:s5,sheet_get_cell:ie,book_new:it,book_append_sheet:ir,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw Error("Cannot find sheet name |"+t+"|")}throw Error("Cannot find sheet |"+t+"|")}(e,t);switch(!e.Workbook.Sheets[a]&&(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:ia,cell_set_internal_link:function(e,t,r){return ia(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:rs(t),s="string"==typeof t?t:rn(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var l=ie(e,i,o);l.t="n",l.F=s,delete l.v,i==n.s.r&&o==n.s.c&&(l.f=r,a&&(l.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};l.version},12976:(e,t,r)=>{r.d(t,{RG:()=>E,bL:()=>R,q7:()=>I});var a=r(43210),n=r(31499),s=r(54e3),i=r(98081),o=r(62076),l=r(57441),c=r(50837),f=r(15521),h=r(66293),u=r(71381),d=r(60687),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,T,b]=(0,s.N)(g),[w,E]=(0,o.A)(g,[b]),[S,y]=w(g),_=a.forwardRef((e,t)=>(0,d.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(A,{...e,ref:t})})}));_.displayName=g;var A=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:s,loop:o=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:E,preventScrollOnEntryFocus:y=!1,..._}=e,A=a.useRef(null),x=(0,i.s)(t,A),k=(0,u.jH)(l),[C,R]=(0,h.i)({prop:v,defaultProp:b??null,onChange:w,caller:g}),[I,N]=a.useState(!1),D=(0,f.c)(E),P=T(r),M=a.useRef(!1),[L,F]=a.useState(0);return a.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,D),()=>e.removeEventListener(p,D)},[D]),(0,d.jsx)(S,{scope:r,orientation:s,dir:k,loop:o,currentTabStopId:C,onItemFocus:a.useCallback(e=>R(e),[R]),onItemShiftTab:a.useCallback(()=>N(!0),[]),onFocusableItemAdd:a.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>F(e=>e-1),[]),children:(0,d.jsx)(c.sG.div,{tabIndex:I||0===L?-1:0,"data-orientation":s,..._,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),y)}}M.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>N(!1))})})}),x="RovingFocusGroupItem",k=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:s=!0,active:i=!1,tabStopId:o,children:f,...h}=e,u=(0,l.B)(),p=o||u,m=y(x,r),g=m.currentTabStopId===p,b=T(r),{onFocusableItemAdd:w,onFocusableItemRemove:E,currentTabStopId:S}=m;return a.useEffect(()=>{if(s)return w(),()=>E()},[s,w,E]),(0,d.jsx)(v.ItemSlot,{scope:r,id:p,focusable:s,active:i,children:(0,d.jsx)(c.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...h,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{s?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return C[n]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>O(r))}}),children:"function"==typeof f?f({isCurrentTabStop:g,hasTabStop:null!=S}):f})})});k.displayName=x;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var R=_,I=k},14952:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17313:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19697:(e,t,r)=>{r.d(t,{C:()=>i});var a=r(43210),n=r(98081),s=r(3562),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[n,i]=a.useState(),l=a.useRef(null),c=a.useRef(e),f=a.useRef("none"),[h,u]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>r[e][t]??e,t));return a.useEffect(()=>{let e=o(l.current);f.current="mounted"===h?e:"none"},[h]),(0,s.N)(()=>{let t=l.current,r=c.current;if(r!==e){let a=f.current,n=o(t);e?u("MOUNT"):"none"===n||t?.display==="none"?u("UNMOUNT"):r&&a!==n?u("ANIMATION_OUT"):u("UNMOUNT"),c.current=e}},[e,u]),(0,s.N)(()=>{if(n){let e,t=n.ownerDocument.defaultView??window,r=r=>{let a=o(l.current).includes(r.animationName);if(r.target===n&&a&&(u("ANIMATION_END"),!c.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},a=e=>{e.target===n&&(f.current=o(l.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:a.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof r?r({present:i.isPresent}):a.Children.only(r),c=(0,n.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||i.isPresent?a.cloneElement(l,{ref:c}):null};function o(e){return e?.animationName||"none"}i.displayName="Presence"},31158:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},37911:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},41862:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},63143:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},65822:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},96474:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};
"use strict";exports.id=617,exports.ids=[617],exports.modules={29617:(e,t,a)=>{a.d(t,{uE:()=>d.u,yq:()=>u,nv:()=>h,vx:()=>l,gK:()=>g,dG:()=>m});var s=a(51060),r=function(e){return e.AVAILABLE="AVAILABLE",e.PENDING_PICKUP="PENDING_PICKUP",e.IN_TRANSIT="IN_TRANSIT",e.PROCESSING="PROCESSING",e.VALORIZED="VALORIZED",e.DISPOSED="DISPOSED",e.AWAITING_ANALYSIS="AWAITING_ANALYSIS",e}({});let n={id:"1",email:"<EMAIL>",username:"root",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),lastLoginAt:new Date().toISOString(),companyId:"1",company:{id:"1",name:"CircuLab Demo Company",siret:"12345678901234",address:"123 Demo Street, Demo City",industryType:"Manufacturing",contactPerson:"John Doe",contactEmail:"<EMAIL>",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},roles:[{role:{id:"1",name:"admin",description:"Administrator role"}}]},o=[{id:"1",type:"Plastic Waste",description:"Mixed plastic containers from manufacturing process",quantity:500,unit:"kg",source:"Production Line A",location_address:"123 Factory Street, Industrial Zone",currentStatus:r.AVAILABLE,producerCompanyId:"1",producerCompany:{id:"1",name:"CircuLab Demo Company",address:"123 Demo Street, Demo City"},isHazardous:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",type:"Metal Scraps",description:"Aluminum and steel scraps from machining",quantity:200,unit:"kg",source:"Machining Department",location_address:"456 Industrial Ave, Manufacturing District",currentStatus:r.PROCESSING,producerCompanyId:"1",producerCompany:{id:"1",name:"CircuLab Demo Company",address:"123 Demo Street, Demo City"},isHazardous:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],i={overview:{totalMaterials:15,availableMaterials:8,processingMaterials:4,valorizedMaterials:3,totalQuantity:2500},materialsByType:[{type:"Plastic Waste",_count:{type:5},_sum:{quantity:1200}},{type:"Metal Scraps",_count:{type:4},_sum:{quantity:800}},{type:"Organic Waste",_count:{type:6},_sum:{quantity:500}}],materialsByStatus:[{currentStatus:r.AVAILABLE,_count:{currentStatus:8},_sum:{quantity:1500}},{currentStatus:r.PROCESSING,_count:{currentStatus:4},_sum:{quantity:700}},{currentStatus:r.VALORIZED,_count:{currentStatus:3},_sum:{quantity:300}}]},c={login:async e=>{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e.email&&"Password123"===e.password)return{success:!0,data:{user:n,tokens:{accessToken:"mock-access-token",refreshToken:"mock-refresh-token"}},message:"Login successful",timestamp:new Date().toISOString()};throw Error("Invalid credentials")},register:async e=>(await new Promise(e=>setTimeout(e,1e3)),{success:!0,data:{user:{...n,email:e.email,username:e.username},tokens:{accessToken:"mock-access-token",refreshToken:"mock-refresh-token"}},message:"Registration successful",timestamp:new Date().toISOString()}),getProfile:async()=>(await new Promise(e=>setTimeout(e,500)),{success:!0,data:n,message:"Profile fetched successfully",timestamp:new Date().toISOString()}),refreshToken:async()=>(await new Promise(e=>setTimeout(e,500)),{success:!0,data:{tokens:{accessToken:"new-mock-access-token",refreshToken:"new-mock-refresh-token"}},message:"Token refreshed successfully",timestamp:new Date().toISOString()}),getMaterials:async()=>(await new Promise(e=>setTimeout(e,800)),{success:!0,data:o,message:"Materials fetched successfully",timestamp:new Date().toISOString()}),getStatistics:async()=>(await new Promise(e=>setTimeout(e,600)),{success:!0,data:i,message:"Statistics fetched successfully",timestamp:new Date().toISOString()})},p=()=>(console.log("\uD83D\uDD0D Mock API check:",{NEXT_PUBLIC_USE_REAL_API:"true",NODE_ENV:"production",shouldUseMock:!1}),console.log("✅ Using REAL API (explicitly set)"),!1);var d=a(12234);let u={getAll:async e=>{let t=new URLSearchParams;e?.search&&t.append("search",e.search),e?.industryType&&t.append("industryType",e.industryType),e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString();return d.u.get(`/companies${a?`?${a}`:""}`)},getById:async e=>d.u.get(`/companies/${e}`),create:async e=>d.u.post("/companies",e),update:async(e,t)=>d.u.put(`/companies/${e}`,t),delete:async e=>d.u.delete(`/companies/${e}`),getStatistics:async()=>d.u.get("/companies/statistics")},m={getAll:async e=>{let t=new URLSearchParams;e?.search&&t.append("search",e.search),e?.companyId&&t.append("companyId",e.companyId),e?.role&&t.append("role",e.role),e?.isEmailVerified!==void 0&&t.append("isEmailVerified",e.isEmailVerified.toString()),e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString();return d.u.get(`/users${a?`?${a}`:""}`)},getById:async e=>d.u.get(`/users/${e}`),create:async e=>d.u.post("/users",e),update:async(e,t)=>d.u.put(`/users/${e}`,t),updatePassword:async(e,t)=>d.u.put(`/users/${e}/password`,t),delete:async e=>d.u.delete(`/users/${e}`),getStatistics:async()=>d.u.get("/users/statistics")},l={getAll:async e=>{let t=new URLSearchParams;e?.userId&&t.append("userId",e.userId),e?.type&&t.append("type",e.type),e?.isRead!==void 0&&t.append("isRead",e.isRead.toString()),e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString();return d.u.get(`/notifications${a?`?${a}`:""}`)},getMy:async e=>{let t=new URLSearchParams;e?.type&&t.append("type",e.type),e?.isRead!==void 0&&t.append("isRead",e.isRead.toString()),e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString();return d.u.get(`/notifications/my${a?`?${a}`:""}`)},getByUserId:async(e,t)=>{let a=new URLSearchParams;t?.type&&a.append("type",t.type),t?.isRead!==void 0&&a.append("isRead",t.isRead.toString()),t?.page&&a.append("page",t.page.toString()),t?.limit&&a.append("limit",t.limit.toString()),t?.sortBy&&a.append("sortBy",t.sortBy),t?.sortOrder&&a.append("sortOrder",t.sortOrder);let s=a.toString();return d.u.get(`/notifications/user/${e}${s?`?${s}`:""}`)},getById:async e=>d.u.get(`/notifications/${e}`),create:async e=>d.u.post("/notifications",e),createBulk:async e=>d.u.post("/notifications/bulk",e),update:async(e,t)=>d.u.put(`/notifications/${e}`,t),markAsRead:async e=>d.u.put(`/notifications/${e}/read`),markAllAsRead:async e=>d.u.put("/notifications/mark-all-read",e),delete:async e=>d.u.delete(`/notifications/${e}`),getStatistics:async()=>d.u.get("/notifications/statistics")},g={getAll:async e=>{let t=new URLSearchParams;e?.wasteMaterialId&&t.append("wasteMaterialId",e.wasteMaterialId),e?.processedByUserId&&t.append("processedByUserId",e.processedByUserId),e?.processorCompanyId&&t.append("processorCompanyId",e.processorCompanyId),e?.treatmentMethodId&&t.append("treatmentMethodId",e.treatmentMethodId),e?.wasteType&&t.append("wasteType",e.wasteType),e?.treatmentMethod&&t.append("treatmentMethod",e.treatmentMethod),e?.dateFrom&&t.append("dateFrom",e.dateFrom),e?.dateTo&&t.append("dateTo",e.dateTo),e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString();return d.u.get(`/processing-history${a?`?${a}`:""}`)},getById:async e=>d.u.get(`/processing-history/${e}`),create:async e=>d.u.post("/processing-history",e),update:async(e,t)=>d.u.put(`/processing-history/${e}`,t),delete:async e=>d.u.delete(`/processing-history/${e}`),getStatistics:async()=>d.u.get("/processing-history/statistics"),getEfficiency:async e=>{let t=new URLSearchParams;e?.companyId&&t.append("companyId",e.companyId),e?.treatmentMethodId&&t.append("treatmentMethodId",e.treatmentMethodId),e?.dateFrom&&t.append("dateFrom",e.dateFrom),e?.dateTo&&t.append("dateTo",e.dateTo);let a=t.toString();return d.u.get(`/processing-history/efficiency${a?`?${a}`:""}`)}},y="http://localhost:4000/api",S=s.A.create({baseURL:y,timeout:1e4,headers:{"Content-Type":"application/json"}});S.interceptors.request.use(e=>{let t=localStorage.getItem("accessToken");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),S.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refreshToken");if(e){let{accessToken:a}=(await s.A.post(`${y}/auth/refresh`,{refreshToken:e})).data.data.tokens;return localStorage.setItem("accessToken",a),t.headers.Authorization=`Bearer ${a}`,S(t)}}catch(e){return localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/login",Promise.reject(e)}}return Promise.reject(e)});let I={"POST:/auth/login":e=>c.login(e),"POST:/auth/register":e=>c.register(e),"GET:/auth/profile":()=>c.getProfile(),"POST:/auth/refresh":()=>c.refreshToken(),"GET:/waste-materials":()=>c.getMaterials(),"GET:/waste-materials/statistics":()=>c.getStatistics()},h={get:async(e,t)=>{if(p()){console.log("\uD83D\uDD04 Using Mock API for GET:",e);let t=I[`GET:${e.split("?")[0]}`];if(t)try{return await t()}catch(e){throw Error(e.message||"Mock API error")}}return console.log("\uD83C\uDF10 Using Real API for GET:",e),S.get(e,t).then(e=>e.data)},post:async(e,t,a)=>{if(p()){console.log("\uD83D\uDD04 Using Mock API for POST:",e);let a=I[`POST:${e}`];if(a)try{return await a(t)}catch(e){throw Error(e.message||"Mock API error")}}return console.log("\uD83C\uDF10 Using Real API for POST:",e),S.post(e,t,a).then(e=>e.data)},put:async(e,t,a)=>p()?{success:!0,data:t,message:"Mock update successful",timestamp:new Date().toISOString()}:S.put(e,t,a).then(e=>e.data),delete:async(e,t)=>p()?{success:!0,message:"Mock delete successful",timestamp:new Date().toISOString()}:S.delete(e,t).then(e=>e.data),patch:async(e,t,a)=>p()?{success:!0,data:t,message:"Mock patch successful",timestamp:new Date().toISOString()}:S.patch(e,t,a).then(e=>e.data)}}};
"use strict";exports.id=52,exports.ids=[52],exports.modules={9052:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(60687);r(43210);var s=r(99720);function a({children:e}){let{initialize:t,isLoading:r}=(0,s.n)();return(0,n.jsx)(n.Fragment,{children:e})}},12234:(e,t,r)=>{r.d(t,{u:()=>a});var n=r(51060);class s{constructor(){this.instance=n.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{let t=e.config;return e.response?.status!==401||t._retry||(t._retry=!0),Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,r){try{return(await this.instance.post(e,t,r)).data}catch(e){throw this.handleError(e)}}async put(e,t,r){try{return(await this.instance.put(e,t,r)).data}catch(e){throw this.handleError(e)}}async patch(e,t,r){try{return(await this.instance.patch(e,t,r)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){let t=Error(e.response.data?.message||e.response.data?.error||"An error occurred");return t.response=e.response,t}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}}let a=new s},26787:(e,t,r)=>{r.d(t,{v:()=>l});var n=r(43210);let s=e=>{let t,r=new Set,n=(e,n)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=n?n:"object"!=typeof s||null===s)?s:Object.assign({},t,s),r.forEach(r=>r(t,e))}},s=()=>t,a={setState:n,getState:s,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(n,s,a);return a},a=e=>e?s(e):s,i=e=>e,o=e=>{let t=a(e),r=e=>(function(e,t=i){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o},59350:(e,t,r)=>{r.d(t,{Zr:()=>s});let n=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>n(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>n(t)(e)}}},s=(e,t)=>(r,s,a)=>{let i,o={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),s=null!=(t=r.getItem(e))?t:null;return s instanceof Promise?s.then(n):n(s)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,c=new Set,u=new Set,d=o.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},s,a);let h=()=>{let e=o.partialize({...s()});return d.setItem(o.name,{state:e,version:o.version})},g=a.setState;a.setState=(e,t)=>{g(e,t),h()};let p=e((...e)=>{r(...e),h()},s,a);a.getInitialState=()=>p;let f=()=>{var e,t;if(!d)return;l=!1,c.forEach(e=>{var t;return e(null!=(t=s())?t:p)});let a=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=s())?e:p))||void 0;return n(d.getItem.bind(d))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,a]=e;if(r(i=o.merge(a,null!=(t=s())?t:p),!0),n)return h()}).then(()=>{null==a||a(i,void 0),i=s(),l=!0,u.forEach(e=>e(i))}).catch(e=>{null==a||a(void 0,e)})};return a.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>f(),hasHydrated:()=>l,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},o.skipHydration||f(),i||p}},99720:(e,t,r)=>{r.d(t,{n:()=>i});var n=r(26787),s=r(59350),a=r(12234);let i=(0,n.v)()((0,s.Zr)((e,t)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async t=>{try{e({isLoading:!0,error:null});let r=await a.u.post("/auth/login",t);if(r.success&&r.data){let{user:t,tokens:n}=r.data;e({user:t,tokens:n,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Login failed")}catch(t){throw e({error:t.response?.data?.message||t.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},register:async t=>{try{e({isLoading:!0,error:null});let r=await a.u.post("/auth/register",t);if(r.success&&r.data){let{user:t,tokens:n}=r.data;e({user:t,tokens:n,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Registration failed")}catch(t){throw e({error:t.response?.data?.message||t.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},logout:()=>{e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{throw Error("Cannot refresh token on server-side")}catch(e){throw t().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let t=await a.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",t),t.success&&t.data)console.log("✅ Profile fetched successfully:",t.data.email),e({user:t.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(t){throw console.error("❌ Profile fetch error:",t),e({error:t.response?.data?.message||t.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),t}},clearError:()=>e({error:null}),setLoading:t=>e({isLoading:t}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store..."),console.log("\uD83D\uDEAB Server-side rendering, skipping token check"),e({isLoading:!1});return}catch(t){console.error("❌ Auth initialization error:",t),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))}};
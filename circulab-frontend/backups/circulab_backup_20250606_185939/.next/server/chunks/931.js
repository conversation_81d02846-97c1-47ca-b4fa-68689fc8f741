"use strict";exports.id=931,exports.ids=[931],exports.modules={4780:(e,r,t)=>{t.d(r,{cn:()=>i});var s=t(29270),a=t(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}},12234:(e,r,t)=>{t.d(r,{u:()=>i});var s=t(51060);class a{constructor(){this.instance=s.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{let r=e.config;return e.response?.status!==401||r._retry||(r._retry=!0),Promise.reject(e)})}async get(e,r){try{return(await this.instance.get(e,r)).data}catch(e){throw this.handleError(e)}}async post(e,r,t){try{return(await this.instance.post(e,r,t)).data}catch(e){throw this.handleError(e)}}async put(e,r,t){try{return(await this.instance.put(e,r,t)).data}catch(e){throw this.handleError(e)}}async patch(e,r,t){try{return(await this.instance.patch(e,r,t)).data}catch(e){throw this.handleError(e)}}async delete(e,r){try{return(await this.instance.delete(e,r)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){let r=Error(e.response.data?.message||e.response.data?.error||"An error occurred");return r.response=e.response,r}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}}let i=new a},29523:(e,r,t)=>{t.d(r,{$:()=>d});var s=t(60687);t(43210);var a=t(88480),i=t(95578),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...d})}},44493:(e,r,t)=>{t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>o});var s=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},70440:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,r,t)=>{t.d(r,{J:()=>l});var s=t(60687),a=t(43210),i=t(76186),n=t(95578),o=t(4780);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,o.cn)(d(),e),...r}));l.displayName=i.b.displayName},89667:(e,r,t)=>{t.d(r,{p:()=>n});var s=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Input"},99720:(e,r,t)=>{t.d(r,{n:()=>n});var s=t(26787),a=t(59350),i=t(12234);let n=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async r=>{try{e({isLoading:!0,error:null});let t=await i.u.post("/auth/login",r);if(t.success&&t.data){let{user:r,tokens:s}=t.data;e({user:r,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Login failed")}catch(r){throw e({error:r.response?.data?.message||r.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},register:async r=>{try{e({isLoading:!0,error:null});let t=await i.u.post("/auth/register",r);if(t.success&&t.data){let{user:r,tokens:s}=t.data;e({user:r,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(t.message||"Registration failed")}catch(r){throw e({error:r.response?.data?.message||r.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},logout:()=>{e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{throw Error("Cannot refresh token on server-side")}catch(e){throw r().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let r=await i.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",r),r.success&&r.data)console.log("✅ Profile fetched successfully:",r.data.email),e({user:r.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(r){throw console.error("❌ Profile fetch error:",r),e({error:r.response?.data?.message||r.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),r}},clearError:()=>e({error:null}),setLoading:r=>e({isLoading:r}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store..."),console.log("\uD83D\uDEAB Server-side rendering, skipping token check"),e({isLoading:!1});return}catch(r){console.error("❌ Auth initialization error:",r),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))}};
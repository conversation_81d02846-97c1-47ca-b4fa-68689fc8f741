exports.id=414,exports.ids=[414],exports.modules={2583:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(60687),a=s(43210),n=s(16189),i=s(99720),o=s(85814),l=s.n(o),c=s(4780);let d=({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),h=({className:e="w-6 h-6"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})}),u=({className:e="w-6 h-6"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),m=[{name:"Cockpit",href:"/dashboard",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})})},{name:"Collectes",href:"/materials",icon:d},{name:"Traitements",href:"/treatment-methods",icon:({className:e="w-5 h-5"})=>(0,t.jsxs)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})},{name:"Historique",href:"/processing-history",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})},{name:"Entreprises",href:"/companies",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})},{name:"Utilisateurs",href:"/users",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})},{name:"Notifications",href:"/notifications",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5zM4.868 19.462A17.173 17.173 0 0012 20c2.485 0 4.809-.503 6.868-1.462M6 10a6 6 0 1112 0v3.586l.707.707A1 1 0 0118 16H6a1 1 0 01-.707-1.707L6 13.586V10z"})})},{name:"Rapports",href:"/analytics",icon:({className:e="w-5 h-5"})=>(0,t.jsx)("svg",{className:e,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}];function x(){let e=(0,n.usePathname)(),{user:r}=(0,i.n)(),[s,o]=(0,a.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsxs)("button",{type:"button",className:"fixed top-4 left-4 z-50 inline-flex items-center justify-center rounded-lg p-2 text-gray-600 bg-white shadow-lg border hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary transition-colors",onClick:()=>o(!s),children:[(0,t.jsx)("span",{className:"sr-only",children:s?"Close menu":"Open menu"}),s?(0,t.jsx)(u,{}):(0,t.jsx)(h,{})]})}),(0,t.jsx)("div",{className:(0,c.cn)("fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-xl border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0",s?"translate-x-0":"-translate-x-full lg:translate-x-0"),children:(0,t.jsxs)("div",{className:"flex h-full flex-col",children:[(0,t.jsx)("div",{className:"flex h-16 items-center justify-center border-b border-gray-200 bg-gradient-to-r from-primary/5 to-primary/10",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary rounded-lg flex items-center justify-center",children:(0,t.jsx)(d,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("h1",{className:"text-xl font-bold text-primary",children:"CircuLab"})]})}),(0,t.jsx)("nav",{className:"flex-1 space-y-2 px-3 py-6",children:m.map(r=>{let s=e===r.href||e.startsWith(r.href+"/");return(0,t.jsxs)(l(),{href:r.href,className:(0,c.cn)("group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 relative",s?"bg-primary text-white shadow-lg shadow-primary/25":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"),onClick:()=>o(!1),children:[(0,t.jsx)(r.icon,{className:(0,c.cn)("mr-3 h-5 w-5 flex-shrink-0 transition-colors",s?"text-white":"text-gray-400 group-hover:text-gray-600")}),(0,t.jsx)("span",{className:"truncate",children:r.name}),s&&(0,t.jsx)("div",{className:"absolute right-3 w-2 h-2 bg-white rounded-full opacity-75"})]},r.name)})}),(0,t.jsx)("div",{className:"border-t border-gray-200 p-4 bg-gray-50/50",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-sm",children:(0,t.jsx)("span",{className:"text-sm font-semibold text-white",children:r?.username?.[0]?.toUpperCase()||r?.email?.[0]?.toUpperCase()||"U"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:r?.username||r?.email?.split("@")[0]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 truncate",children:r?.company?.name||"No company"})]})]})})]})}),s&&(0,t.jsx)("div",{className:"fixed inset-0 z-30 bg-gray-600 bg-opacity-75 lg:hidden",onClick:()=>o(!1)})]})}var p=s(29523);let g=()=>(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),f=()=>(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})});function v(){let e=(0,n.useRouter)(),{user:r,logout:s}=(0,i.n)();return(0,t.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30",children:(0,t.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex h-16 justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"hidden lg:block",children:[(0,t.jsxs)("h1",{className:"text-lg font-semibold text-gray-900",children:["Welcome back, ",r?.username||r?.email?.split("@")[0]]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"CircuLab"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,t.jsx)(f,{}),(0,t.jsx)("span",{className:"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white animate-pulse"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"hidden sm:block text-right",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 truncate max-w-32",children:r?.username||r?.email}),(0,t.jsx)("p",{className:"text-xs text-gray-500 truncate max-w-32",children:r?.company?.name||"No company"})]}),(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-sm",children:(0,t.jsx)("span",{className:"text-xs font-semibold text-white",children:r?.username?.[0]?.toUpperCase()||r?.email?.[0]?.toUpperCase()||"U"})}),(0,t.jsxs)(p.$,{variant:"outline",size:"sm",onClick:()=>{s(),e.push("/login")},className:"flex items-center space-x-2 hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors",children:[(0,t.jsx)(g,{}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})]})})})}function j({children:e}){(0,n.useRouter)();let{isAuthenticated:r,user:s,isLoading:a}=(0,i.n)();return a?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})}):r&&s?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50/50",children:[(0,t.jsx)(x,{}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)(v,{}),(0,t.jsx)("main",{className:"py-6 lg:py-8",children:(0,t.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"animate-fade-in",children:e})})})]})]}):null}},4780:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(29270),a=s(82348);function n(...e){return(0,a.QP)((0,t.$)(e))}},12234:(e,r,s)=>{"use strict";s.d(r,{u:()=>n});var t=s(51060);class a{constructor(){this.instance=t.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{let r=e.config;return e.response?.status!==401||r._retry||(r._retry=!0),Promise.reject(e)})}async get(e,r){try{return(await this.instance.get(e,r)).data}catch(e){throw this.handleError(e)}}async post(e,r,s){try{return(await this.instance.post(e,r,s)).data}catch(e){throw this.handleError(e)}}async put(e,r,s){try{return(await this.instance.put(e,r,s)).data}catch(e){throw this.handleError(e)}}async patch(e,r,s){try{return(await this.instance.patch(e,r,s)).data}catch(e){throw this.handleError(e)}}async delete(e,r){try{return(await this.instance.delete(e,r)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){let r=Error(e.response.data?.message||e.response.data?.error||"An error occurred");return r.response=e.response,r}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}}let n=new a},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>l});var t=s(60687);s(43210);var a=s(88480),n=s(95578),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:s,asChild:n=!1,...l}){let c=n?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:s,className:e})),...l})}},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o});var t=s(60687),a=s(43210),n=s(4780);let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},56312:(e,r,s)=>{Promise.resolve().then(s.bind(s,2583))},57675:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx","default")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71992:(e,r,s)=>{Promise.resolve().then(s.bind(s,57675))},99720:(e,r,s)=>{"use strict";s.d(r,{n:()=>i});var t=s(26787),a=s(59350),n=s(12234);let i=(0,t.v)()((0,a.Zr)((e,r)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async r=>{try{e({isLoading:!0,error:null});let s=await n.u.post("/auth/login",r);if(s.success&&s.data){let{user:r,tokens:t}=s.data;e({user:r,tokens:t,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(s.message||"Login failed")}catch(r){throw e({error:r.response?.data?.message||r.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},register:async r=>{try{e({isLoading:!0,error:null});let s=await n.u.post("/auth/register",r);if(s.success&&s.data){let{user:r,tokens:t}=s.data;e({user:r,tokens:t,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(s.message||"Registration failed")}catch(r){throw e({error:r.response?.data?.message||r.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),r}},logout:()=>{e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{throw Error("Cannot refresh token on server-side")}catch(e){throw r().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let r=await n.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",r),r.success&&r.data)console.log("✅ Profile fetched successfully:",r.data.email),e({user:r.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(r){throw console.error("❌ Profile fetch error:",r),e({error:r.response?.data?.message||r.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),r}},clearError:()=>e({error:null}),setLoading:r=>e({isLoading:r}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store..."),console.log("\uD83D\uDEAB Server-side rendering, skipping token check"),e({isLoading:!1});return}catch(r){console.error("❌ Auth initialization error:",r),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))}};
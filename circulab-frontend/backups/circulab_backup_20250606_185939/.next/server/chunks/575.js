"use strict";exports.id=575,exports.ids=[575],exports.modules={4780:(e,t,s)=>{s.d(t,{cn:()=>n});var r=s(29270),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},12234:(e,t,s)=>{s.d(t,{u:()=>n});var r=s(51060);class a{constructor(){this.instance=r.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{let t=e.config;return e.response?.status!==401||t._retry||(t._retry=!0),Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,s){try{return(await this.instance.post(e,t,s)).data}catch(e){throw this.handleError(e)}}async put(e,t,s){try{return(await this.instance.put(e,t,s)).data}catch(e){throw this.handleError(e)}}async patch(e,t,s){try{return(await this.instance.patch(e,t,s)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){let t=Error(e.response.data?.message||e.response.data?.error||"An error occurred");return t.response=e.response,t}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}}let n=new a},19080:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},26540:(e,t,s)=>{s.r(t),s.d(t,{DashboardContent:()=>g,default:()=>v});var r=s(60687),a=s(43210),n=s(44493),i=s(29523),l=s(96834),d=s(29617),c=s(41862),o=s(96474),u=s(19080),h=s(62688);let x=(0,h.A)("recycle",[["path",{d:"M7 19H4.815a1.83 1.83 0 0 1-1.57-.881 1.785 1.785 0 0 1-.004-1.784L7.196 9.5",key:"x6z5xu"}],["path",{d:"M11 19h8.203a1.83 1.83 0 0 0 1.556-.89 1.784 1.784 0 0 0 0-1.775l-1.226-2.12",key:"1x4zh5"}],["path",{d:"m14 16-3 3 3 3",key:"f6jyew"}],["path",{d:"M8.293 13.596 7.196 9.5 3.1 10.598",key:"wf1obh"}],["path",{d:"m9.344 5.811 1.093-1.892A1.83 1.83 0 0 1 11.985 3a1.784 1.784 0 0 1 1.546.888l3.943 6.843",key:"9tzpgr"}],["path",{d:"m13.378 9.633 4.096 1.098 1.097-4.096",key:"1oe83g"}]]),m=(0,h.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var p=s(53411);let f=(0,h.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);function g(){let[e,t]=(0,a.useState)(null),[s,h]=(0,a.useState)(!0),[g,v]=(0,a.useState)(null),y=async()=>{try{h(!0),v(null);let e=await d.uE.get("/waste-materials/statistics");if(e.success&&e.data)t(e.data);else throw Error("Failed to fetch statistics")}catch(e){console.error("Error fetching statistics:",e),v(e.message||"Failed to load dashboard data")}finally{h(!1)}};if(s)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 animate-spin"}),(0,r.jsx)("span",{children:"Loading dashboard..."})]})});if(g)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-destructive mb-4",children:g}),(0,r.jsx)(i.$,{onClick:y,variant:"outline",children:"Try Again"})]})});let j=e?.overview||{totalMaterials:0,availableMaterials:0,processingMaterials:0,valorizedMaterials:0,totalQuantity:0},b=j.totalMaterials>0?Math.round(j.valorizedMaterials/j.totalMaterials*100):0;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Vue d'ensemble de la valorisation des d\xe9chets industriels"})]}),(0,r.jsxs)(i.$,{className:"gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),"Nouveau d\xe9chet"]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Production Totale"}),(0,r.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[j.totalQuantity,"T"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[j.totalMaterials," mat\xe9riaux"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Valoris\xe9"}),(0,r.jsx)(x,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[e?.materialsByStatus?.find(e=>"VALORIZED"===e.currentStatus)?._sum?.quantity||0,"T"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[j.valorizedMaterials," mat\xe9riaux"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Taux de Valorisation"}),(0,r.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[b,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2.1% ce mois"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"En Traitement"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-orange-600",children:[e?.materialsByStatus?.find(e=>"PROCESSING"===e.currentStatus)?._sum?.quantity||0,"T"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[j.processingMaterials," mat\xe9riaux"]})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-7",children:[(0,r.jsxs)(n.Zp,{className:"col-span-4",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Types de D\xe9chets"}),(0,r.jsx)(n.BT,{children:"R\xe9partition par type de mat\xe9riau"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:e?.materialsByType?.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ${0===t?"bg-blue-500":1===t?"bg-green-500":"bg-orange-500"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.type})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm font-medium",children:[e._sum?.quantity||0,"T"]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e._count?.type||0," mat\xe9riaux"]})]})]},e.type))})})]}),(0,r.jsxs)(n.Zp,{className:"col-span-3",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Actions Rapides"}),(0,r.jsx)(n.BT,{children:"Acc\xe8s direct aux fonctionnalit\xe9s"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-3",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"w-full justify-start gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"G\xe9rer les d\xe9chets",(0,r.jsx)(f,{className:"h-4 w-4 ml-auto"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"w-full justify-start gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Analytics",(0,r.jsx)(f,{className:"h-4 w-4 ml-auto"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"w-full justify-start gap-2",children:[(0,r.jsx)(x,{className:"h-4 w-4"}),"Traitements",(0,r.jsx)(f,{className:"h-4 w-4 ml-auto"})]})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"\xc9tat des Mat\xe9riaux"}),(0,r.jsx)(n.BT,{children:"R\xe9partition par statut de traitement"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"flex flex-wrap gap-4",children:e?.materialsByStatus?.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.E,{variant:"AVAILABLE"===e.currentStatus?"default":"PROCESSING"===e.currentStatus?"secondary":"VALORIZED"===e.currentStatus?"outline":"destructive",children:"AVAILABLE"===e.currentStatus?"Disponible":"PROCESSING"===e.currentStatus?"En traitement":"VALORIZED"===e.currentStatus?"Valoris\xe9":e.currentStatus}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e._sum?.quantity||0,"T (",e._count?.currentStatus||0,")"]})]},e.currentStatus))})})]})]})}let v=g},29523:(e,t,s)=>{s.d(t,{$:()=>d});var r=s(60687);s(43210);var a=s(88480),n=s(95578),i=s(4780);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:s,asChild:n=!1,...d}){let c=n?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:s,className:e})),...d})}},41862:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>l});var r=s(60687),a=s(43210),n=s(4780);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));o.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},53411:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62688:(e,t,s)=>{s.d(t,{A:()=>u});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:o,...u},h)=>(0,r.createElement)("svg",{ref:h,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",n),...!i&&!d(u)&&{"aria-hidden":"true"},...u},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...n},d)=>(0,r.createElement)(o,{ref:d,iconNode:t,className:l(`lucide-${a(i(e))}`,`lucide-${e}`,s),...n}));return s.displayName=i(e),s}},96474:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,s)=>{s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(95578),n=s(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...s})}}};
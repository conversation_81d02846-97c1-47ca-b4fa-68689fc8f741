"use strict";exports.id=519,exports.ids=[519],exports.modules={22246:(e,t,a)=>{a.d(t,{OR:()=>T,IT:()=>E});var s=a(60687),r=a(43210),o=a(41862),n=a(31158),l=a(10022),d=a(37911),i=a(29523),c=a(3984),m=a(14952),p=a(13964),u=a(65822),f=a(4780);let x=c.bL,b=c.l9;c.YJ,c.ZL,c.Pb,c.z6,r.forwardRef(({className:e,inset:t,children:a,...r},o)=>(0,s.jsxs)(c.ZP,{ref:o,className:(0,f.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[a,(0,s.jsx)(m.A,{className:"ml-auto h-4 w-4"})]})).displayName=c.ZP.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(c.G5,{ref:a,className:(0,f.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=c.G5.displayName;let y=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>(0,s.jsx)(c.ZL,{children:(0,s.jsx)(c.UC,{ref:r,sideOffset:t,className:(0,f.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));y.displayName=c.UC.displayName;let h=r.forwardRef(({className:e,inset:t,...a},r)=>(0,s.jsx)(c.q7,{ref:r,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...a}));h.displayName=c.q7.displayName,r.forwardRef(({className:e,children:t,checked:a,...r},o)=>(0,s.jsxs)(c.H_,{ref:o,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(c.VF,{children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})}),t]})).displayName=c.H_.displayName,r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(c.hN,{ref:r,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(c.VF,{children:(0,s.jsx)(u.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=c.hN.displayName;let g=r.forwardRef(({className:e,inset:t,...a},r)=>(0,s.jsx)(c.JU,{ref:r,className:(0,f.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a}));g.displayName=c.JU.displayName;let v=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(c.wv,{ref:a,className:(0,f.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));v.displayName=c.wv.displayName;var N=a(29867),w=a(10345);function j(e,t){return t.split(".").reduce((e,t)=>e?.[t],e)}let k={date:e=>e?new Date(e).toLocaleDateString():"",number:(e,t=2)=>null==e?"":Number(e).toFixed(t),boolean:e=>e?"Yes":"No"};function S({data:e,columns:t,filename:a="export",disabled:c=!1,variant:m="outline",size:p="sm",className:u}){let[f,k]=(0,r.useState)(!1),{toast:S}=(0,N.d)(),T=async s=>{if(0===e.length)return void S({title:"No data to export",description:"There is no data available to export.",variant:"destructive"});k(!0);try{let r={filename:a,includeTimestamp:!0};"csv"===s?(!function(e,t,a={}){let{filename:s="export",includeTimestamp:r=!0}=a,o=[t.map(e=>e.label),...e.map(e=>t.map(t=>{var a;let s=j(e,t.key);return t.formatter?t.formatter(s):(a=s,null==a?"":a instanceof Date?a.toISOString().split("T")[0]:"object"==typeof a?JSON.stringify(a):String(a))}))].map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(",")).join("\n"),n=r?`_${new Date().toISOString().split("T")[0]}`:"";!function(e,t,a){let s=new Blob([e],{type:a}),r=window.URL.createObjectURL(s),o=document.createElement("a");o.href=r,o.download=t,o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)}(o,`${s}${n}.csv`,"text/csv;charset=utf-8;")}(e,t,r),S({title:"Export successful",description:`Data exported to CSV format (${e.length} records)`})):(!function(e,t,a={}){let{filename:s="export",sheetName:r="Sheet1",includeTimestamp:o=!0}=a,n=w.Wp.book_new(),l=e.map(e=>{let a={};return t.forEach(t=>{let s=j(e,t.key);a[t.label]=t.formatter?t.formatter(s):s}),a}),d=w.Wp.json_to_sheet(l),i=t.map(e=>({wch:Math.max(e.label.length,15)}));d["!cols"]=i,w.Wp.book_append_sheet(n,d,r);let c=o?`_${new Date().toISOString().split("T")[0]}`:"",m=`${s}${c}.xlsx`;w._h(n,m)}(e,t,r),S({title:"Export successful",description:`Data exported to Excel format (${e.length} records)`}))}catch(e){console.error("Export error:",e),S({title:"Export failed",description:"An error occurred while exporting the data.",variant:"destructive"})}finally{k(!1)}};return(0,s.jsxs)(x,{children:[(0,s.jsx)(b,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:m,size:p,disabled:c||f,className:u,children:[f?(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Export"]})}),(0,s.jsxs)(y,{align:"end",className:"w-48",children:[(0,s.jsx)(g,{children:"Export Format"}),(0,s.jsx)(v,{}),(0,s.jsxs)(h,{onClick:()=>T("csv"),disabled:f,className:"cursor-pointer",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{children:"CSV File"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Comma-separated values"})]})]}),(0,s.jsxs)(h,{onClick:()=>T("excel"),disabled:f,className:"cursor-pointer",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{children:"Excel File"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Microsoft Excel format"})]})]}),(0,s.jsx)(v,{}),(0,s.jsxs)(h,{disabled:!0,className:"text-xs text-muted-foreground",children:[e.length," records available"]})]})]})}function T({companies:e,...t}){return(0,s.jsx)(S,{data:e,columns:[{key:"name",label:"Company Name"},{key:"siret",label:"SIRET"},{key:"industryType",label:"Industry Type"},{key:"address",label:"Address"},{key:"contactPerson",label:"Contact Person"},{key:"contactEmail",label:"Contact Email"},{key:"createdAt",label:"Created Date",formatter:e=>e?new Date(e).toLocaleDateString():""}],filename:"companies",...t})}function E({history:e,...t}){return(0,s.jsx)(S,{data:e,columns:[{key:"wasteMaterial.type",label:"Waste Type"},{key:"treatmentMethod.name",label:"Treatment Method"},{key:"inputQuantity",label:"Input Quantity",formatter:e=>e?Number(e).toFixed(2):""},{key:"outputQuantity",label:"Output Quantity",formatter:e=>e?Number(e).toFixed(2):""},{key:"processorCompany.name",label:"Processor"},{key:"dateProcessed",label:"Date Processed",formatter:e=>e?new Date(e).toLocaleDateString():""},{key:"createdAt",label:"Created Date",formatter:e=>e?new Date(e).toLocaleDateString():""}],filename:"processing-history",...t})}k.date,k.date,k.number,k.number,k.date,k.date,k.boolean,k.date},29867:(e,t,a)=>{a.d(t,{d:()=>d});var s=a(43210);let r={toasts:[]},o=[];function n(e){switch(e.type){case"ADD_TOAST":r={...r,toasts:[...r.toasts,e.payload]};break;case"REMOVE_TOAST":r={...r,toasts:r.toasts.filter(t=>t.id!==e.payload)};break;case"CLEAR_TOASTS":r={...r,toasts:[]}}o.forEach(e=>e(r))}function l({title:e,description:t,variant:a="default",duration:s=5e3}){let r=Math.random().toString(36).substr(2,9);return n({type:"ADD_TOAST",payload:{id:r,title:e,description:t,variant:a,duration:s}}),s>0&&setTimeout(()=>{n({type:"REMOVE_TOAST",payload:r})},s),r}function d(){let[e,t]=(0,s.useState)(r),a=(0,s.useCallback)(e=>(o.push(e),()=>{o=o.filter(t=>t!==e)}),[]);return(0,s.useCallback)(()=>{o=[]},[]),s.useEffect(()=>a(t),[a]),{toast:l,toasts:e.toasts,dismiss:e=>{n({type:"REMOVE_TOAST",payload:e})},clear:()=>{n({type:"CLEAR_TOASTS"})}}}},89667:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(60687),r=a(43210),o=a(4780);let n=r.forwardRef(({className:e,type:t,...a},r)=>(0,s.jsx)("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));n.displayName="Input"},96834:(e,t,a)=>{a.d(t,{E:()=>l});var s=a(60687);a(43210);var r=a(95578),o=a(4780);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...a}){return(0,s.jsx)("div",{className:(0,o.cn)(n({variant:t}),e),...a})}}};
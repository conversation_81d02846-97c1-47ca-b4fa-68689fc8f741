exports.id=73,exports.ids=[73],exports.modules={3152:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/components/ErrorBoundary.tsx","default")},5241:(e,r,t)=>{Promise.resolve().then(t.bind(t,95758)),Promise.resolve().then(t.bind(t,30316))},30316:(e,r,t)=>{"use strict";t.d(r,{SafeAuthProvider:()=>d,W$:()=>c});var n=t(60687),s=t(30036);function o({height:e="h-20",width:r="w-full",className:t=""}){return(0,n.jsx)("div",{className:`animate-pulse bg-gray-200 rounded ${e} ${r} ${t}`,children:(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsx)("div",{className:"text-gray-400 text-sm",children:"Loading..."})})})}function i({error:e,componentName:r,retry:t}){return(0,n.jsxs)("div",{className:"border border-red-200 bg-red-50 rounded-lg p-4 m-2",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)("svg",{className:"h-5 w-5 text-red-500 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,n.jsxs)("h3",{className:"text-sm font-medium text-red-800",children:["Component Failed: ",r]})]}),(0,n.jsx)("p",{className:"text-sm text-red-700 mb-3",children:e.message||"Unknown error occurred"}),t&&(0,n.jsx)("button",{onClick:t,className:"text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded",children:"Retry Loading"}),!1]})}t(43210);class a{static getInstance(){return a.instance||(a.instance=new a),a.instance}log(e,r){let t={timestamp:new Date,error:e,context:r};this.errors.push(t),this.errors.length>100&&(this.errors=this.errors.slice(-100))}getErrors(){return[...this.errors]}clearErrors(){this.errors=[]}constructor(){this.errors=[]}}function l(e,r,t={}){let{loading:c=()=>(0,n.jsx)(o,{}),ssr:d=!1,onError:m,fallback:h,retryCount:u=3,timeout:p=1e4}=t,f=0,b=a.getInstance();return(0,s.default)(async()=>{try{let t=e(),n=new Promise((e,t)=>{setTimeout(()=>t(Error(`Import timeout for ${r}`)),p)}),s=await Promise.race([t,n]);return f=0,s}catch(t){if(b.log(t,`Safe import failed for ${r}`),m&&m(t),f<u)return f++,console.warn(`Retrying import for ${r} (attempt ${f}/${u})`),await new Promise(e=>setTimeout(e,1e3*Math.pow(2,f))),e();if(h)return{default:h};return{default:e=>(0,n.jsx)(i,{error:t,componentName:r,retry:()=>window.location.reload()})}}},{loading:c,ssr:d})}function c(){return a.getInstance()}let d=l(()=>Promise.all([t.e(60),t.e(52)]).then(t.bind(t,9052)),"AuthProvider",{loading:()=>(0,n.jsx)(o,{height:"h-screen"}),ssr:!1});l(()=>Promise.all([t.e(60),t.e(226),t.e(617),t.e(575)]).then(t.bind(t,26540)),"DashboardContent",{loading:()=>(0,n.jsx)(o,{height:"h-96"})})},34617:(e,r,t)=>{Promise.resolve().then(t.bind(t,3152)),Promise.resolve().then(t.bind(t,70346))},61135:()=>{},70346:(e,r,t)=>{"use strict";t.d(r,{SafeAuthProvider:()=>s});var n=t(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call ComponentSkeleton() from the server but ComponentSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","ComponentSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call ErrorFallback() from the server but ErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","ErrorFallback"),(0,n.registerClientReference)(function(){throw Error("Attempted to call createSafeImport() from the server but createSafeImport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","createSafeImport"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useErrorLogger() from the server but useErrorLogger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","useErrorLogger");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call SafeAuthProvider() from the server but SafeAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","SafeAuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call SafeDashboardContent() from the server but SafeDashboardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/lib/safeImports.tsx","SafeDashboardContent")},78244:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},85860:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>c});var n=t(37413),s=t(31001),o=t.n(s);t(61135);var i=t(61120),a=t(3152),l=t(70346);let c={title:"CircuLab - Industrial Waste Valorization Platform",description:"Comprehensive platform for managing and valorizing industrial waste materials through intelligent matching and processing tracking."};function d(){return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})})}function m({children:e}){return(0,n.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,n.jsx)("body",{className:`${o().variable} font-sans antialiased`,children:(0,n.jsx)(a.default,{children:(0,n.jsx)(i.Suspense,{fallback:(0,n.jsx)(d,{}),children:(0,n.jsx)(l.SafeAuthProvider,{children:e})})})})})}},95758:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var n=t(60687),s=t(43210);class o extends s.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r),this.setState({error:e,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("svg",{className:"h-8 w-8 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Application Error"})})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Something went wrong while loading the application. This might be due to:"}),(0,n.jsxs)("ul",{className:"mt-2 text-sm text-gray-600 list-disc list-inside",children:[(0,n.jsx)("li",{children:"A temporary network issue"}),(0,n.jsx)("li",{children:"A module loading problem"}),(0,n.jsx)("li",{children:"An extension conflict"})]})]}),!1,(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Reload Page"}),(0,n.jsx)("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorInfo:void 0}),className:"flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Try Again"})]}),(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsx)("button",{onClick:()=>window.location.href="/",className:"text-sm text-blue-600 hover:text-blue-800 underline",children:"Return to Home"})})]})}):this.props.children}}let i=o}};
"use strict";exports.id=33,exports.ids=[33],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},15195:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43210);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},32061:(e,t,r)=>{r.d(t,{UC:()=>eA,YJ:()=>eB,In:()=>eM,q7:()=>eV,VF:()=>eO,p4:()=>eG,JU:()=>e_,ZL:()=>eL,bL:()=>eP,wn:()=>eK,PP:()=>eF,wv:()=>eW,l9:()=>eE,WT:()=>eD,LM:()=>eH});var n=r(43210),l=r(51215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(31499),i=r(54e3),s=r(98081),d=r(62076),u=r(71381),c=r(52464),p=r(25233),f=r(35393),h=r(57441),v=r(34255),m=r(28210),w=r(50837),g=r(88480),x=r(15521),y=r(66293),S=r(3562),b=r(15195),C=r(60687),j=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...j,...e.style}})).displayName="VisuallyHidden";var T=r(47342),k=r(35599),R=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],I="Select",[P,E,D]=(0,i.N)(I),[M,L]=(0,d.A)(I,[D,v.Bk]),A=(0,v.Bk)(),[H,B]=M(I),[_,V]=M(I),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=A(t),[S,b]=n.useState(null),[j,T]=n.useState(null),[k,R]=n.useState(!1),N=(0,u.jH)(c),[E,D]=(0,y.i)({prop:l,defaultProp:o??!1,onChange:a,caller:I}),[M,L]=(0,y.i)({prop:i,defaultProp:s,onChange:d,caller:I}),B=n.useRef(null),V=!S||g||!!S.closest("form"),[G,O]=n.useState(new Set),F=Array.from(G).map(e=>e.props.value).join(";");return(0,C.jsx)(v.bL,{...x,children:(0,C.jsxs)(H,{required:w,scope:t,trigger:S,onTriggerChange:b,valueNode:j,onValueNodeChange:T,valueNodeHasChildren:k,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:M,onValueChange:L,open:E,onOpenChange:D,dir:N,triggerPointerDownPosRef:B,disabled:m,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,C.jsxs)(ek,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===M?(0,C.jsx)("option",{value:""}):null,Array.from(G)]},F):null]})})};G.displayName=I;var O="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),d=B(O,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=E(r),f=n.useRef("touch"),[h,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eI(t,e,r);void 0!==n&&d.onValueChange(n.value)}),x=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(v.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eR(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(x(),e.preventDefault())})})})});F.displayName=O;var K="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=B(K,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,S.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eR(d.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});W.displayName=K;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var z=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=B(q,e.__scopeSelect),[o,a]=n.useState();return((0,S.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,C.jsx)(Y,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});Z.displayName=q;var[Y,J]=M(q),X=(0,g.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:b,...j}=e,R=B(q,r),[N,I]=n.useState(null),[P,D]=n.useState(null),M=(0,s.s)(t,e=>I(e)),[L,A]=n.useState(null),[H,_]=n.useState(null),V=E(r),[G,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,T.Eq)(N)},[N]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),r?.focus(),document.activeElement!==l))return},[V,P]),W=n.useCallback(()=>K([L,N]),[K,L,N]);n.useEffect(()=>{G&&W()},[G,W]);let{onOpenChange:U,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,J]=eN(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(A(e),n&&(F.current=!0))},[R.value]),et=n.useCallback(()=>N?.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&_(e)},[R.value]),en="popper"===l?ee:$,el=en===ee?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:b}:{};return(0,C.jsx)(Y,{scope:r,content:N,viewport:P,onViewportChange:D,itemRefCallback:Q,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:H,position:l,isPositioned:G,searchRef:Z,children:(0,C.jsx)(k.A,{as:X,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>O(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=B(q,r),d=J(q,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=E(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:b,focusSelectedItem:j}=d,T=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&y&&b){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+h+d+parseInt(c.paddingBottom,10)+w,S=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),j=parseInt(C.paddingTop,10),T=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,R=y.offsetHeight/2,N=f+h+(y.offsetTop+R);if(N<=k){let e=a.length>0&&y===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-k,R+(e?T:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;u.style.top="0px";let t=Math.max(k,f+x.offsetTop+(e?j:0)+R);u.style.height=t+(g-N)+"px",x.scrollTop=N-k+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,x,y,b,i.dir,l]);(0,S.N)(()=>T(),[T]);let[k,R]=n.useState();(0,S.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(T(),j?.(),g.current=!1)},[T,j]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,C.jsx)(w.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,C.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=M(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=J(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(P.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=M(eo),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=eo;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=M(ec),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=B(ec,r),c=J(ec,r),p=u.value===l,[f,v]=n.useState(i??""),[m,g]=n.useState(!1),x=(0,s.s)(t,e=>c.itemRefCallback?.(e,l,o)),y=(0,h.B)(),S=n.useRef("touch"),b=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:x,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==S.current&&b()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===S.current&&b()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{S.current=e.pointerType,o?c.onItemLeave?.():"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(N.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ec;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=B(ev,r),u=J(ev,r),c=ef(ev,r),p=V(ev,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,S.N)(()=>(x(g),()=>y(g)),[x,y,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=ev;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=J(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eS="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=J(eS,e.__scopeSelect),l=er(eS,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=eS;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=J("SelectScrollButton",r),s=n.useRef(null),d=E(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,S.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});ej.displayName="SelectSeparator";var eT="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=B(eT,r),a=J(eT,r);return o.open&&"popper"===a.position?(0,C.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eT;var ek=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.s)(l,o),i=(0,b.Z)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,C.jsx)(w.sG.select,{...r,style:{...j,...r.style},ref:a,defaultValue:t})});function eR(e){return""===e||void 0===e}function eN(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eI(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}ek.displayName="SelectBubbleInput";var eP=G,eE=F,eD=W,eM=U,eL=z,eA=Z,eH=el,eB=es,e_=eu,eV=eh,eG=em,eO=eg,eF=ey,eK=eb,eW=ej},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}};
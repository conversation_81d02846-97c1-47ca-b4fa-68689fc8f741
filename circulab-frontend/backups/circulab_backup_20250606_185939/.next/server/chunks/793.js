"use strict";exports.id=793,exports.ids=[793],exports.modules={3562:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},15521:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},31499:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},50837:(e,t,r)=>{r.d(t,{hO:()=>c,sG:()=>i});var n=r(43210),o=r(51215),l=r(88480),u=r(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},54e3:(e,t,r)=>{r.d(t,{N:()=>c});var n=r(43210),o=r(62076),l=r(98081),u=r(88480),i=r(60687);function c(e){let t=e+"CollectionProvider",[r,c]=(0,o.A)(t),[a,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,i.jsx)(a,{scope:t,itemMap:l,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",m=(0,u.TL)(d),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(d,r),u=(0,l.s)(t,o.collectionRef);return(0,i.jsx)(m,{ref:u,children:n})});p.displayName=d;let v=e+"CollectionItemSlot",h="data-radix-collection-item",w=(0,u.TL)(v),x=n.forwardRef((e,t)=>{let{scope:r,children:o,...u}=e,c=n.useRef(null),a=(0,l.s)(t,c),f=s(v,r);return n.useEffect(()=>(f.itemMap.set(c,{ref:c,...u}),()=>void f.itemMap.delete(c))),(0,i.jsx)(w,{...{[h]:""},ref:a,children:o})});return x.displayName=v,[{Provider:f,Slot:p,ItemSlot:x},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},c]}var a=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=f(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},57441:(e,t,r)=>{r.d(t,{B:()=>c});var n,o=r(43210),l=r(3562),u=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,r]=o.useState(u());return(0,l.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},62076:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(43210),o=r(60687);function l(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,l){let u=n.createContext(l),i=r.length;r=[...r,l];let c=t=>{let{scope:r,children:l,...c}=t,a=r?.[e]?.[i]||u,s=n.useMemo(()=>c,Object.values(c));return(0,o.jsx)(a.Provider,{value:s,children:l})};return c.displayName=t+"Provider",[c,function(r,o){let c=o?.[e]?.[i]||u,a=n.useContext(c);if(a)return a;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},62688:(e,t,r)=>{r.d(t,{A:()=>f});var n=r(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),u=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:l="",children:u,iconNode:s,...f},d)=>(0,n.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",l),...!u&&!c(f)&&{"aria-hidden":"true"},...f},[...s.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(u)?u:[u]])),f=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...l},c)=>(0,n.createElement)(s,{ref:c,iconNode:t,className:i(`lucide-${o(u(e))}`,`lucide-${e}`,r),...l}));return r.displayName=u(e),r}},66293:(e,t,r)=>{r.d(t,{i:()=>i});var n,o=r(43210),l=r(3562),u=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,i,c]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),i=o.useRef(t);return u(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==r&&(i.current?.(r),l.current=r)},[r,l]),[r,n,i]}({defaultProp:t,onChange:r}),a=void 0!==e,s=a?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[s,o.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&c.current?.(r)}else i(t)},[a,e,i,c])]}Symbol("RADIX:SYNC_STATE")},71381:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(43210);r(60687);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}}};
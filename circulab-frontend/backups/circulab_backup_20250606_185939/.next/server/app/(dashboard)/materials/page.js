(()=>{var e={};e.id=143,e.ids=[143],e.modules={2440:(e,r,t)=>{Promise.resolve().then(t.bind(t,57291))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38888:(e,r,t)=>{Promise.resolve().then(t.bind(t,70641))},49014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=t(65239),i=t(48088),n=t(88170),o=t.n(n),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let c={children:["",{children:["(dashboard)",{children:["materials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70641)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/materials/page",pathname:"/materials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57291:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),i=t(44493),n=t(29523);function o(){return(0,s.jsx)("div",{className:"animate-fade-in",children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Gestion des D\xe9chets"}),(0,s.jsx)(i.BT,{children:"G\xe9rez vos mat\xe9riaux de d\xe9chets et suivez leur traitement"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"mx-auto w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Module de Gestion des D\xe9chets"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Cette section permet de g\xe9rer vos mat\xe9riaux de d\xe9chets, suivre leur statut et organiser leur traitement."}),(0,s.jsx)(n.$,{children:"Ajouter un D\xe9chet"})]})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70641:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/materials/page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,178,60,226,658,607,73,414],()=>t(49014));module.exports=s})();
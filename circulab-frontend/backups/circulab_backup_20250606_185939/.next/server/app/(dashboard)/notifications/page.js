(()=>{var e={};e.id=317,e.ids=[317],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5626:(e,t,r)=>{Promise.resolve().then(r.bind(r,48350))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>p,eb:()=>m,gC:()=>h,l6:()=>c,yv:()=>u});var s=r(60687),a=r(43210),i=r(32061),n=r(78272),o=r(3589),d=r(13964),l=r(4780);let c=i.bL;i.YJ;let u=i.WT,p=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(i.l9,{ref:a,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let x=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.PP,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})}));x.displayName=i.PP.displayName;let f=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.wn,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));f.displayName=i.wn.displayName;let h=a.forwardRef(({className:e,children:t,position:r="popper",...a},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:n,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,s.jsx)(x,{}),(0,s.jsx)(i.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(f,{})]})}));h.displayName=i.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.JU,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let m=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(i.q7,{ref:a,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:t})]}));m.displayName=i.q7.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29867:(e,t,r)=>{"use strict";r.d(t,{d:()=>d});var s=r(43210);let a={toasts:[]},i=[];function n(e){switch(e.type){case"ADD_TOAST":a={...a,toasts:[...a.toasts,e.payload]};break;case"REMOVE_TOAST":a={...a,toasts:a.toasts.filter(t=>t.id!==e.payload)};break;case"CLEAR_TOASTS":a={...a,toasts:[]}}i.forEach(e=>e(a))}function o({title:e,description:t,variant:r="default",duration:s=5e3}){let a=Math.random().toString(36).substr(2,9);return n({type:"ADD_TOAST",payload:{id:a,title:e,description:t,variant:r,duration:s}}),s>0&&setTimeout(()=>{n({type:"REMOVE_TOAST",payload:a})},s),a}function d(){let[e,t]=(0,s.useState)(a),r=(0,s.useCallback)(e=>(i.push(e),()=>{i=i.filter(t=>t!==e)}),[]);return(0,s.useCallback)(()=>{i=[]},[]),s.useEffect(()=>r(t),[r]),{toast:o,toasts:e.toasts,dismiss:e=>{n({type:"REMOVE_TOAST",payload:e})},clear:()=>{n({type:"CLEAR_TOASTS"})}}}},33873:e=>{"use strict";e.exports=require("path")},46946:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["(dashboard)",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94383)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},48350:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(13964),n=r(62688);let o=(0,n.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),d=(0,n.A)("bell-ring",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M22 8c0-2.3-.8-4.3-2-6",key:"5bb3ad"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}],["path",{d:"M4 2C2.8 3.7 2 5.7 2 8",key:"tap9e0"}]]);var l=r(13861),c=r(88233),u=r(29523),p=r(44493),x=r(96834),f=r(15079),h=r(29617),m=r(29867);function b(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[b,v]=(0,a.useState)(""),[g,y]=(0,a.useState)(""),[j,A]=(0,a.useState)(1),[N,E]=(0,a.useState)(1),[w,S]=(0,a.useState)(0),[C,T]=(0,a.useState)(0),{toast:_}=(0,m.d)(),P=async(e=1,r="",s)=>{try{n(!0);let a=await h.vx.getMy({page:e,limit:10,type:r||void 0,isRead:s,sortBy:"createdAt",sortOrder:"desc"});a.success&&a.data&&(t(a.data.notifications),S(a.data.total),E(a.data.totalPages),A(a.data.page),T(a.data.unreadCount))}catch(e){console.error("Error fetching notifications:",e),_({title:"Error",description:"Failed to fetch notifications",variant:"destructive"})}finally{n(!1)}},k=e=>{P(e,b,"read"===g||"unread"!==g&&void 0)},R=async e=>{try{await h.vx.markAsRead(e),_({title:"Success",description:"Notification marked as read"}),P(j,b,"read"===g||"unread"!==g&&void 0)}catch(e){console.error("Error marking notification as read:",e),_({title:"Error",description:"Failed to mark notification as read",variant:"destructive"})}},L=async()=>{try{_({title:"Info",description:"Mark all as read functionality needs user context"})}catch(e){console.error("Error marking all notifications as read:",e),_({title:"Error",description:"Failed to mark all notifications as read",variant:"destructive"})}},M=async e=>{if(confirm("Are you sure you want to delete this notification?"))try{await h.vx.delete(e),_({title:"Success",description:"Notification deleted successfully"}),P(j,b,"read"===g||"unread"!==g&&void 0)}catch(e){console.error("Error deleting notification:",e),_({title:"Error",description:"Failed to delete notification",variant:"destructive"})}},O=e=>{switch(e){case"NEW_WASTE_AVAILABLE":return"\uD83D\uDCE6";case"WASTE_STATUS_UPDATED":return"\uD83D\uDD04";case"PROCESSING_COMPLETE":return"✅";case"CONTRACT_PROPOSAL":return"\uD83D\uDCCB";case"MESSAGE_RECEIVED":return"\uD83D\uDCAC";case"SYSTEM_ALERT":return"⚠️";default:return"ℹ️"}},D=e=>{switch(e){case"NEW_WASTE_AVAILABLE":return"bg-blue-100 text-blue-800";case"WASTE_STATUS_UPDATED":return"bg-yellow-100 text-yellow-800";case"PROCESSING_COMPLETE":return"bg-green-100 text-green-800";case"CONTRACT_PROPOSAL":return"bg-purple-100 text-purple-800";case"MESSAGE_RECEIVED":return"bg-indigo-100 text-indigo-800";case"SYSTEM_ALERT":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Notifications"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Stay updated with your CircuLab activities"})]}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:C>0&&(0,s.jsxs)(u.$,{variant:"outline",onClick:L,children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Mark All Read (",C,")"]})})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Filter Notifications"}),(0,s.jsx)(p.BT,{children:"Filter by type and read status"})]}),(0,s.jsx)(p.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)(f.l6,{value:b,onValueChange:v,children:[(0,s.jsx)(f.bq,{className:"w-48",children:(0,s.jsx)(f.yv,{placeholder:"Filter by type"})}),(0,s.jsxs)(f.gC,{children:[(0,s.jsx)(f.eb,{value:"",children:"All types"}),(0,s.jsx)(f.eb,{value:"NEW_WASTE_AVAILABLE",children:"New Waste Available"}),(0,s.jsx)(f.eb,{value:"WASTE_STATUS_UPDATED",children:"Status Updated"}),(0,s.jsx)(f.eb,{value:"PROCESSING_COMPLETE",children:"Processing Complete"}),(0,s.jsx)(f.eb,{value:"CONTRACT_PROPOSAL",children:"Contract Proposal"}),(0,s.jsx)(f.eb,{value:"MESSAGE_RECEIVED",children:"Message Received"}),(0,s.jsx)(f.eb,{value:"SYSTEM_ALERT",children:"System Alert"})]})]}),(0,s.jsxs)(f.l6,{value:g,onValueChange:y,children:[(0,s.jsx)(f.bq,{className:"w-48",children:(0,s.jsx)(f.yv,{placeholder:"Filter by status"})}),(0,s.jsxs)(f.gC,{children:[(0,s.jsx)(f.eb,{value:"",children:"All notifications"}),(0,s.jsx)(f.eb,{value:"unread",children:"Unread"}),(0,s.jsx)(f.eb,{value:"read",children:"Read"})]})]})]})})]}),(0,s.jsx)("div",{className:"grid gap-4",children:r?(0,s.jsx)("div",{className:"text-center py-8",children:"Loading notifications..."}):0===e.length?(0,s.jsx)(p.Zp,{children:(0,s.jsxs)(p.Wu,{className:"text-center py-8",children:[(0,s.jsx)(o,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No notifications found"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:b||g?"No notifications match your filters.":"You're all caught up!"})]})}):e.map(e=>(0,s.jsx)(p.Zp,{className:e.isRead?"":"border-l-4 border-l-blue-500",children:(0,s.jsx)(p.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,s.jsx)("span",{className:"text-lg",children:O(e.type)}),(0,s.jsx)(x.E,{className:D(e.type),children:e.type.replace(/_/g," ")}),!e.isRead&&(0,s.jsxs)(x.E,{variant:"default",className:"bg-blue-100 text-blue-800",children:[(0,s.jsx)(d,{className:"h-3 w-3 mr-1"}),"New"]})]}),(0,s.jsx)("p",{className:"text-sm mb-3",children:e.message}),(0,s.jsx)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:(0,s.jsx)("span",{children:new Date(e.createdAt).toLocaleString()})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[e.linkTo&&(0,s.jsx)(u.$,{variant:"outline",size:"sm",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),!e.isRead&&(0,s.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>R(e.id),children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}),(0,s.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>M(e.id),children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})]})]})})},e.id))}),N>1&&(0,s.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,s.jsx)(u.$,{variant:"outline",onClick:()=>k(j-1),disabled:1===j,children:"Previous"}),(0,s.jsxs)("span",{className:"flex items-center px-4",children:["Page ",j," of ",N]}),(0,s.jsx)(u.$,{variant:"outline",onClick:()=>k(j+1),disabled:j===N,children:"Next"})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82066:(e,t,r)=>{Promise.resolve().then(r.bind(r,94383))},83997:e=>{"use strict";e.exports=require("tty")},94383:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/notifications/page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(60687);r(43210);var a=r(95578),i=r(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...r})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,178,60,226,658,607,793,737,33,73,414,617],()=>r(46946));module.exports=s})();
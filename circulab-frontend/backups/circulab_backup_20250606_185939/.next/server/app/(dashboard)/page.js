(()=>{var e={};e.id=966,e.ids=[966],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},15195:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(43210);function r(e){let s=a.useRef({value:e,previous:e});return a.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28464:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30695:(e,s,t)=>{Promise.resolve().then(t.bind(t,28464))},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32201:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Y});var a=t(60687),r=t(43210),l=t(44493),i=t(29523),c=t(85763),n=t(31158),d=t(62688);let o=(0,d.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var x=t(40228);let h=(0,d.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),m=(0,d.A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);var u=t(53411),p=t(26787),f=t(29617);let g=(0,p.v)((e,s)=>({materials:[],currentMaterial:null,statistics:null,isLoading:!1,error:null,pagination:null,fetchMaterials:async s=>{try{e({isLoading:!0,error:null});let t=new URLSearchParams;s&&Object.entries(s).forEach(([e,s])=>{null!=s&&t.append(e,s.toString())});let a=await f.nv.get(`/waste-materials?${t.toString()}`);if(a.success&&a.data)e({materials:a.data,pagination:a.pagination||null,isLoading:!1,error:null});else throw Error(a.message||"Failed to fetch waste materials")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to fetch waste materials",isLoading:!1}),s}},fetchMaterialById:async s=>{try{e({isLoading:!0,error:null});let t=await f.nv.get(`/waste-materials/${s}`);if(t.success&&t.data)e({currentMaterial:t.data,isLoading:!1,error:null});else throw Error(t.message||"Failed to fetch waste material")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to fetch waste material",isLoading:!1}),s}},createMaterial:async s=>{try{e({isLoading:!0,error:null});let t=await f.nv.post("/waste-materials",s);if(t.success&&t.data)return e(e=>({materials:[t.data,...e.materials],isLoading:!1,error:null})),t.data;throw Error(t.message||"Failed to create waste material")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to create waste material",isLoading:!1}),s}},updateMaterial:async(s,t)=>{try{e({isLoading:!0,error:null});let a=await f.nv.put(`/waste-materials/${s}`,t);if(a.success&&a.data)return e(e=>({materials:e.materials.map(e=>e.id===s?a.data:e),currentMaterial:e.currentMaterial?.id===s?a.data:e.currentMaterial,isLoading:!1,error:null})),a.data;throw Error(a.message||"Failed to update waste material")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to update waste material",isLoading:!1}),s}},deleteMaterial:async s=>{try{e({isLoading:!0,error:null});let t=await f.nv.delete(`/waste-materials/${s}`);if(t.success)e(e=>({materials:e.materials.filter(e=>e.id!==s),currentMaterial:e.currentMaterial?.id===s?null:e.currentMaterial,isLoading:!1,error:null}));else throw Error(t.message||"Failed to delete waste material")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to delete waste material",isLoading:!1}),s}},fetchStatistics:async()=>{try{e({isLoading:!0,error:null});let s=await f.nv.get("/waste-materials/statistics");if(s.success&&s.data)e({statistics:s.data,isLoading:!1,error:null});else throw Error(s.message||"Failed to fetch statistics")}catch(s){throw e({error:s.response?.data?.message||s.message||"Failed to fetch statistics",isLoading:!1}),s}},clearError:()=>e({error:null}),setLoading:s=>e({isLoading:s}),clearCurrentMaterial:()=>e({currentMaterial:null})}));var j=t(98081),v=t(62076),b=t(31499),y=t(66293),N=t(15195),w=t(92415),k=t(19697),M=t(50837),P="Checkbox",[E,C]=(0,v.A)(P),[L,A]=E(P);function R(e){let{__scopeCheckbox:s,checked:t,children:l,defaultChecked:i,disabled:c,form:n,name:d,onCheckedChange:o,required:x,value:h="on",internal_do_not_use_render:m}=e,[u,p]=(0,y.i)({prop:t,defaultProp:i??!1,onChange:o,caller:P}),[f,g]=r.useState(null),[j,v]=r.useState(null),b=r.useRef(!1),N=!f||!!n||!!f.closest("form"),w={checked:u,disabled:c,setChecked:p,control:f,setControl:g,name:d,form:n,value:h,hasConsumerStoppedPropagationRef:b,required:x,defaultChecked:!D(i)&&i,isFormControl:N,bubbleInput:j,setBubbleInput:v};return(0,a.jsx)(L,{scope:s,...w,children:"function"==typeof m?m(w):l})}var B="CheckboxTrigger",T=r.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...l},i)=>{let{control:c,value:n,disabled:d,checked:o,required:x,setControl:h,setChecked:m,hasConsumerStoppedPropagationRef:u,isFormControl:p,bubbleInput:f}=A(B,e),g=(0,j.s)(i,h),v=r.useRef(o);return r.useEffect(()=>{let e=c?.form;if(e){let s=()=>m(v.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[c,m]),(0,a.jsx)(M.sG.button,{type:"button",role:"checkbox","aria-checked":D(o)?"mixed":o,"aria-required":x,"data-state":F(o),"data-disabled":d?"":void 0,disabled:d,value:n,...l,ref:g,onKeyDown:(0,b.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,b.m)(t,e=>{m(e=>!!D(e)||!e),f&&p&&(u.current=e.isPropagationStopped(),u.current||e.stopPropagation())})})});T.displayName=B;var Z=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:r,checked:l,defaultChecked:i,required:c,disabled:n,value:d,onCheckedChange:o,form:x,...h}=e;return(0,a.jsx)(R,{__scopeCheckbox:t,checked:l,defaultChecked:i,disabled:n,required:c,onCheckedChange:o,name:r,form:x,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T,{...h,ref:s,__scopeCheckbox:t}),e&&(0,a.jsx)(q,{__scopeCheckbox:t})]})})});Z.displayName=P;var $="CheckboxIndicator",W=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...l}=e,i=A($,t);return(0,a.jsx)(k.C,{present:r||D(i.checked)||!0===i.checked,children:(0,a.jsx)(M.sG.span,{"data-state":F(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});W.displayName=$;var S="CheckboxBubbleInput",q=r.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:l,hasConsumerStoppedPropagationRef:i,checked:c,defaultChecked:n,required:d,disabled:o,name:x,value:h,form:m,bubbleInput:u,setBubbleInput:p}=A(S,e),f=(0,j.s)(t,p),g=(0,N.Z)(c),v=(0,w.X)(l);r.useEffect(()=>{if(!u)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!i.current;if(g!==c&&e){let t=new Event("click",{bubbles:s});u.indeterminate=D(c),e.call(u,!D(c)&&c),u.dispatchEvent(t)}},[u,g,c,i]);let b=r.useRef(!D(c)&&c);return(0,a.jsx)(M.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:n??b.current,required:d,disabled:o,name:x,value:h,form:m,...s,tabIndex:-1,ref:f,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function D(e){return"indeterminate"===e}function F(e){return D(e)?"indeterminate":e?"checked":"unchecked"}q.displayName=S;var z=t(13964),_=t(4780);let O=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(Z,{ref:t,className:(0,_.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,a.jsx)(W,{className:(0,_.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(z.A,{className:"h-4 w-4"})})}));function U(){let[e,s]=(0,r.useState)([{title:"Organization",options:[{id:"org1",label:"CircuLab HQ",checked:!1},{id:"org2",label:"Regional Office",checked:!1},{id:"org3",label:"Processing Center",checked:!1}]},{title:"Hub",options:[{id:"hub1",label:"North Hub",checked:!1},{id:"hub2",label:"South Hub",checked:!1},{id:"hub3",label:"Central Hub",checked:!1}]},{title:"Facility",options:[{id:"fac1",label:"Processing Plant A",checked:!1},{id:"fac2",label:"Processing Plant B",checked:!1},{id:"fac3",label:"Sorting Facility",checked:!1}]},{title:"Site",options:[{id:"site1",label:"Collection Point 1",checked:!1},{id:"site2",label:"Collection Point 2",checked:!1},{id:"site3",label:"Storage Site",checked:!1}]},{title:"Activity",options:[{id:"act1",label:"Collection",checked:!1},{id:"act2",label:"Processing",checked:!1},{id:"act3",label:"Recycling",checked:!1},{id:"act4",label:"Disposal",checked:!1}]},{title:"Scope",options:[{id:"scope1",label:"Organic Waste",checked:!1},{id:"scope2",label:"Plastic Waste",checked:!1},{id:"scope3",label:"Metal Waste",checked:!1}]},{title:"Location",options:[{id:"loc1",label:"Urban Areas",checked:!1},{id:"loc2",label:"Industrial Zones",checked:!1},{id:"loc3",label:"Rural Areas",checked:!1}]},{title:"Tags",options:[{id:"tag1",label:"High Priority",checked:!1},{id:"tag2",label:"Recyclable",checked:!1},{id:"tag3",label:"Hazardous",checked:!1}]},{title:"Time period",options:[{id:"time1",label:"Last 7 days",checked:!1},{id:"time2",label:"Last 30 days",checked:!0},{id:"time3",label:"Last 90 days",checked:!1},{id:"time4",label:"Last year",checked:!1}]}]),t=(t,a)=>{let r=[...e];r[t].options[a].checked=!r[t].options[a].checked,s(r)};return(0,a.jsx)("div",{className:"w-80 bg-gray-800 text-white h-full overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-6",children:"Filter options"}),(0,a.jsx)("div",{className:"space-y-6",children:e.map((e,s)=>(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:e.title}),(0,a.jsx)("div",{className:"space-y-2",children:e.options.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(O,{id:e.id,checked:e.checked,onCheckedChange:()=>t(s,r),className:"border-gray-500 data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500"}),(0,a.jsx)("label",{htmlFor:e.id,className:"text-sm text-gray-300 cursor-pointer hover:text-white transition-colors",children:e.label})]},e.id))})]},e.title))}),(0,a.jsx)(i.$,{onClick:()=>{console.log("Applying filters:",e)},className:"w-full mt-8 bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded",children:"APPLY FILTER"})]})})}function G({title:e,value:s,unit:t,trend:r,chartData:i}){let{areaPath:c,linePath:n}=(e=>{if(0===e.length)return{areaPath:"",linePath:""};let s=Math.max(...e.map(e=>e.value)),t=Math.min(...e.map(e=>e.value)),a=s-t,r=e.map((s,r)=>{let l=10+r/(e.length-1)*280,i=90-(s.value-t)/a*80;return`${l},${i}`});return{areaPath:`M 10,90 L ${r.join(" L ")} L 290,90 Z`,linePath:`M ${r.join(" L ")}`}})(i);return(0,a.jsxs)(l.Zp,{className:"border-0 shadow-sm",children:[(0,a.jsx)(l.aR,{className:"pb-2",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:e})}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:s}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:t}),r&&(0,a.jsxs)("span",{className:`text-sm font-medium ${r>0?"text-green-600":"text-red-600"}`,children:[r>0?"+":"",r,"%"]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("svg",{width:"100%",height:"100",viewBox:"0 0 300 100",className:"overflow-visible",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:`gradient-${e}`,x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#E91E63",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#E91E63",stopOpacity:"0.05"})]})}),(0,a.jsx)("path",{d:c,fill:`url(#gradient-${e})`,className:"transition-all duration-300"}),(0,a.jsx)("path",{d:n,fill:"none",stroke:"#E91E63",strokeWidth:"2",className:"transition-all duration-300"}),i.map((e,s)=>{let t=Math.max(...i.map(e=>e.value)),r=Math.min(...i.map(e=>e.value)),l=10+s/(i.length-1)*280,c=90-(e.value-r)/(t-r)*70;return(0,a.jsx)("circle",{cx:l,cy:c,r:"3",fill:"#E91E63",className:"hover:r-4 transition-all duration-200 cursor-pointer"},s)})]})})]})]})}function I({data:e}){let s=e||[{month:"Jan",processed:85,collected:100},{month:"Feb",processed:92,collected:110},{month:"Mar",processed:78,collected:95},{month:"Apr",processed:88,collected:105},{month:"May",processed:95,collected:115},{month:"Jun",processed:82,collected:98}],t=Math.max(...s.flatMap(e=>[e.processed,e.collected]));return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"relative h-48",children:(0,a.jsxs)("svg",{width:"100%",height:"100%",viewBox:"0 0 400 200",className:"overflow-visible",children:[[0,25,50,75,100].map(e=>(0,a.jsxs)("g",{children:[(0,a.jsx)("line",{x1:"40",y1:160-e/t*120,x2:"360",y2:160-e/t*120,stroke:"#E5E7EB",strokeWidth:"1"}),(0,a.jsx)("text",{x:"35",y:165-e/t*120,textAnchor:"end",className:"text-xs fill-gray-500",children:e})]},e)),s.map((e,s)=>{let r=60+50*s,l=e.processed/t*120,i=e.collected/t*120;return(0,a.jsxs)("g",{children:[(0,a.jsx)("rect",{x:r,y:160-i,width:20,height:i,fill:"#E5E7EB",rx:"2"}),(0,a.jsx)("rect",{x:r,y:160-l,width:20,height:l,fill:"#E91E63",rx:"2",className:"hover:opacity-80 transition-opacity cursor-pointer"}),(0,a.jsx)("text",{x:r+10,y:"180",textAnchor:"middle",className:"text-xs fill-gray-600",children:e.month}),(0,a.jsx)("text",{x:r+10,y:155-l,textAnchor:"middle",className:"text-xs fill-gray-700 font-medium",children:e.processed})]},e.month)})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-pink-500 rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Processed"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-300 rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Collected"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:[s.reduce((e,s)=>e+s.processed,0),"T"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Total Processed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:[Math.round(s.reduce((e,s)=>e+s.processed,0)/s.reduce((e,s)=>e+s.collected,0)*100),"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Efficiency Rate"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:s.length}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Months Tracked"})]})]})]})}function H({title:e,percentage:s,categories:t}){let r=2*Math.PI*45;return(0,a.jsxs)(l.Zp,{className:"border-0 shadow-sm",children:[(0,a.jsx)(l.aR,{className:"pb-4",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:e})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("svg",{width:"120",height:"120",className:"transform -rotate-90",children:[(0,a.jsx)("circle",{cx:"60",cy:"60",r:45,stroke:"#E5E7EB",strokeWidth:"8",fill:"none"}),(0,a.jsx)("circle",{cx:"60",cy:"60",r:45,stroke:"#E91E63",strokeWidth:"8",fill:"none",strokeDasharray:r,strokeDashoffset:r-s/100*r,strokeLinecap:"round",className:"transition-all duration-1000 ease-out"})]}),(0,a.jsx)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:(0,a.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[s,"%"]})})]}),(0,a.jsx)("div",{className:"space-y-3 ml-6",children:t.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},s))})]})})]})}function X({viewMode:e}){let[s,t]=(0,r.useState)(null),l=[{id:"1",name:"Collection Point A",x:20,y:30,value:45,type:"collection"},{id:"2",name:"Processing Plant B",x:60,y:25,value:78,type:"processing"},{id:"3",name:"Collection Point C",x:35,y:60,value:32,type:"collection"},{id:"4",name:"Disposal Site D",x:80,y:70,value:25,type:"disposal"},{id:"5",name:"Processing Plant E",x:15,y:75,value:56,type:"processing"},{id:"6",name:"Collection Point F",x:70,y:40,value:38,type:"collection"}],i=e=>{switch(e){case"collection":return"#22C55E";case"processing":return"#3B82F6";case"disposal":return"#EF4444";default:return"#6B7280"}},c=e=>Math.max(8,Math.min(20,e/4));return(0,a.jsxs)("div",{className:"w-full",children:["map"===e&&(0,a.jsxs)("div",{className:"relative w-full h-96 bg-gray-100 rounded-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50",children:(0,a.jsxs)("svg",{className:"w-full h-full opacity-20",children:[(0,a.jsx)("defs",{children:(0,a.jsx)("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:(0,a.jsx)("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"#94A3B8",strokeWidth:"1"})})}),(0,a.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)"})]})}),l.map(e=>(0,a.jsxs)("div",{className:"absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110",style:{left:`${e.x}%`,top:`${e.y}%`},onClick:()=>t(e.id),children:[(0,a.jsx)("div",{className:"rounded-full shadow-lg border-2 border-white flex items-center justify-center",style:{backgroundColor:i(e.type),width:`${c(e.value)}px`,height:`${c(e.value)}px`},children:(0,a.jsx)("span",{className:"text-white text-xs font-bold",children:e.value})}),s===e.id&&(0,a.jsxs)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg shadow-lg p-2 min-w-max z-10",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.value,"T waste"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.type})]})]},e.id)),(0,a.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Legend"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,a.jsx)("span",{className:"text-xs",children:"Collection"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,a.jsx)("span",{className:"text-xs",children:"Processing"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,a.jsx)("span",{className:"text-xs",children:"Disposal"})]})]})]})]}),"table"===e&&(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Location"}),(0,a.jsx)("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Type"}),(0,a.jsx)("th",{className:"text-right py-2 px-3 font-medium text-gray-600",children:"Waste (T)"})]})}),(0,a.jsx)("tbody",{children:l.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-2 px-3",children:e.name}),(0,a.jsx)("td",{className:"py-2 px-3",children:(0,a.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize ${"collection"===e.type?"bg-green-100 text-green-800":"processing"===e.type?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"}`,children:e.type})}),(0,a.jsx)("td",{className:"py-2 px-3 text-right font-medium",children:e.value})]},e.id))})]})}),"chart"===e&&(0,a.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.name}),(0,a.jsxs)("span",{className:"text-sm font-bold text-gray-900",children:[e.value,"T"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-out",style:{width:`${e.value/Math.max(...l.map(e=>e.value))*100}%`,backgroundColor:i(e.type)}})})]},e.id))}),"targets"===e&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:"85%"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Collection Target"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"85%"}})})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:"92%"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Processing Target"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"92%"}})})]})]}),"yearly"===e&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:"2,847T"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"Total waste processed this year"}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Q1"}),(0,a.jsx)("div",{className:"text-gray-500",children:"642T"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Q2"}),(0,a.jsx)("div",{className:"text-gray-500",children:"718T"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Q3"}),(0,a.jsx)("div",{className:"text-gray-500",children:"756T"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Q4"}),(0,a.jsx)("div",{className:"text-gray-500",children:"731T"})]})]})]})]})}O.displayName=Z.displayName;var V=t(85814),Q=t.n(V);let J=({title:e,data:s,centerValue:t,centerLabel:r})=>{let i=s.reduce((e,s)=>e+s.value,0),c=0,n=(e,s)=>{let t=Math.PI/180*(3.6*s-90),a=Math.PI/180*((s+e)*3.6-90),r=50+35*Math.cos(t),l=50+35*Math.sin(t),i=50+35*Math.cos(a),c=50+35*Math.sin(a);return`M 50 50 L ${r} ${l} A 35 35 0 ${+(e>50)} 1 ${i} ${c} Z`};return(0,a.jsxs)(l.Zp,{className:"border-0 shadow-sm",children:[(0,a.jsx)(l.aR,{className:"pb-4",children:(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:e})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("svg",{width:"200",height:"200",viewBox:"0 0 100 100",className:"transform -rotate-90",children:[s.map((e,s)=>{let t=e.value/i*100,r=n(t,c);return c+=t,(0,a.jsx)("path",{d:r,fill:e.color,className:"hover:opacity-80 transition-opacity cursor-pointer"},s)}),(0,a.jsx)("circle",{cx:"50",cy:"50",r:"20",fill:"white"})]}),t&&(0,a.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:t}),r&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:r})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.label}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.value,"%"]})]},s))})]})})]})},K=({title:e,data:s,subtitle:t})=>{let r=Math.max(...s.map(e=>e.value));return(0,a.jsxs)(l.Zp,{className:"border-0 shadow-sm",children:[(0,a.jsxs)(l.aR,{className:"pb-4",children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold",children:e}),t&&(0,a.jsx)(l.BT,{children:t})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,a.jsxs)("span",{className:"text-sm font-bold text-gray-900",children:[e.value,"T"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out",style:{width:`${e.value/r*100}%`,backgroundColor:e.color}})})]},s))})})]})};function Y(){let{statistics:e,fetchStatistics:s}=g(),[t,d]=(0,r.useState)("overview"),[p,f]=(0,r.useState)("targets"),j=e?.overview;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,a.jsx)(U,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboards"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Export"]})})]}),(0,a.jsx)(c.tU,{value:t,onValueChange:d,className:"mt-4",children:(0,a.jsxs)(c.j7,{className:"grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6",children:[(0,a.jsx)(c.Xi,{value:"overview",children:"KPIs"}),(0,a.jsx)(c.Xi,{value:"waste",children:"Waste dashboard"}),(0,a.jsx)(c.Xi,{value:"processing",children:"Processing dashboard"}),(0,a.jsx)(c.Xi,{value:"efficiency",children:"Efficiency"}),(0,a.jsx)(c.Xi,{value:"materials",children:"Materials"}),(0,a.jsx)(c.Xi,{value:"analytics",children:"Analytics"})]})})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto p-6",children:(0,a.jsxs)(c.tU,{value:t,onValueChange:d,children:[(0,a.jsxs)(c.av,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsx)(G,{title:"Waste processed",value:"476,117.93",unit:"tonnes",trend:12.5,chartData:[{month:"Jan",value:4e5},{month:"Feb",value:42e4},{month:"Mar",value:45e4},{month:"Apr",value:476117}]}),(0,a.jsx)(H,{title:"Processing by category",percentage:75,categories:[{name:"Organic",color:"#E91E63",percentage:45},{name:"Plastic",color:"#9E9E9E",percentage:30},{name:"Metal",color:"#757575",percentage:25}]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsx)(l.ZB,{children:"Waste generated"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.$,{variant:"targets"===p?"default":"outline",size:"sm",onClick:()=>f("targets"),children:[(0,a.jsx)(o,{className:"h-4 w-4 mr-1"}),"view targets"]}),(0,a.jsxs)(i.$,{variant:"yearly"===p?"default":"outline",size:"sm",onClick:()=>f("yearly"),children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"yearly"]}),(0,a.jsxs)(i.$,{variant:"map"===p?"default":"outline",size:"sm",onClick:()=>f("map"),children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-1"}),"map"]}),(0,a.jsxs)(i.$,{variant:"table"===p?"default":"outline",size:"sm",onClick:()=>f("table"),children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-1"}),"table"]}),(0,a.jsxs)(i.$,{variant:"chart"===p?"default":"outline",size:"sm",onClick:()=>f("chart"),children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"chart"]})]})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)(X,{viewMode:p})})]})]}),(0,a.jsxs)(c.av,{value:"waste",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Waste"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:j?.totalMaterials||0}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"Processing Rate"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+5% from last month"})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"Active Facilities"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:j?.totalMaterials||0}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+3 new this month"})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"Efficiency Score"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2% from last week"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Processing Efficiency"}),(0,a.jsx)(l.BT,{children:"Monthly processing trends"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)(I,{})})]}),(0,a.jsx)(J,{title:"Types de d\xe9chets",data:[{label:"Plastiques",value:35,color:"#22c55e"},{label:"M\xe9taux",value:25,color:"#3b82f6"},{label:"Organiques",value:20,color:"#f59e0b"},{label:"Carton",value:20,color:"#ef4444"}],centerValue:"100%",centerLabel:"Total"})]})]}),(0,a.jsx)(c.av,{value:"processing",className:"space-y-4",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Processing Overview"}),(0,a.jsx)(l.BT,{children:"Track processing activities and efficiency"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,a.jsx)(K,{title:"Masses Collect\xe9es",data:[{label:"Plastiques",value:45,color:"#22c55e"},{label:"Carton",value:38,color:"#f59e0b"},{label:"Organiques",value:32,color:"#3b82f6"},{label:"M\xe9taux",value:28,color:"#ef4444"},{label:"Verre",value:25,color:"#8b5cf6"},{label:"Autres",value:20,color:"#6b7280"}],subtitle:"R\xe9partition par type de d\xe9chet"}),(0,a.jsx)(J,{title:"Traitements",data:[{label:"Recyclage",value:45,color:"#22c55e"},{label:"Valorisation",value:30,color:"#3b82f6"},{label:"Enfouissement",value:25,color:"#f59e0b"}],centerValue:"100%",centerLabel:"Total"})]})})]})}),(0,a.jsx)(c.av,{value:"efficiency",className:"space-y-4",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Efficiency Metrics"}),(0,a.jsx)(l.BT,{children:"Monitor operational efficiency and optimization opportunities"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)(J,{title:"Prestataires",data:[{label:"ETS Recyclage",value:40,color:"#22c55e"},{label:"Green Solutions",value:35,color:"#3b82f6"},{label:"EcoTech",value:25,color:"#f59e0b"}],centerValue:"3",centerLabel:"Actifs"})})]})}),(0,a.jsx)(c.av,{value:"materials",className:"space-y-4",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Material Management"}),(0,a.jsx)(l.BT,{children:"Manage waste materials and inventory"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsx)(Q(),{href:"/materials",children:(0,a.jsx)(i.$,{className:"w-full h-16 flex-col space-y-2 hover:bg-primary/90 transition-colors",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"G\xe9rer les D\xe9chets"})})}),(0,a.jsx)(Q(),{href:"/analytics",children:(0,a.jsx)(i.$,{variant:"outline",className:"w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Voir Analytics"})})}),(0,a.jsx)(Q(),{href:"/treatment-methods",children:(0,a.jsx)(i.$,{variant:"outline",className:"w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Traitements"})})})]})})]})}),(0,a.jsx)(c.av,{value:"analytics",className:"space-y-4",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Analytics & Reports"}),(0,a.jsx)(l.BT,{children:"Detailed analytics and reporting tools"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"Analytics dashboard will be implemented here."})})]})})]})})]})]})}},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44906:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),c=t(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d={children:["",{children:["(dashboard)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28464)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>o,j7:()=>n,tU:()=>c});var a=t(60687),r=t(43210),l=t(10424),i=t(4780);let c=l.bL,n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));n.displayName=l.B8.displayName;let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=l.l9.displayName;let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=l.UC.displayName},92415:(e,s,t)=>{"use strict";t.d(s,{X:()=>l});var a=t(43210),r=t(3562);function l(e){let[s,t]=a.useState(void 0);return(0,r.N)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let s=new ResizeObserver(s=>{let a,r;if(!Array.isArray(s)||!s.length)return;let l=s[0];if("borderBoxSize"in l){let e=l.borderBoxSize,s=Array.isArray(e)?e[0]:e;a=s.inlineSize,r=s.blockSize}else a=e.offsetWidth,r=e.offsetHeight;t({width:a,height:r})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}t(void 0)},[e]),s}},94735:e=>{"use strict";e.exports=require("events")},96191:(e,s,t)=>{Promise.resolve().then(t.bind(t,32201))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,178,60,226,658,607,793,662,73,414,617],()=>t(44906));module.exports=a})();
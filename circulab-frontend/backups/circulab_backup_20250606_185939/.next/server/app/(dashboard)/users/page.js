(()=>{var e={};e.id=809,e.ids=[809],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11926:(e,s,t)=>{Promise.resolve().then(t.bind(t,47147))},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>p,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>u});var r=t(60687),a=t(43210),i=t(32061),n=t(78272),d=t(3589),l=t(13964),o=t(4780);let c=i.bL;i.YJ;let u=i.WT,p=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.l9,{ref:a,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let m=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(d.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let h=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=i.wn.displayName;let x=a.forwardRef(({className:e,children:s,position:t="popper",...a},n)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[(0,r.jsx)(m,{}),(0,r.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(h,{})]})}));x.displayName=i.UC.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.JU.displayName;let f=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.q7,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:s})]}));f.displayName=i.q7.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.wv.displayName},17313:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23077:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(43210),i=t(96474),n=t(99270),d=t(62688);let l=(0,d.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var o=t(99891);let c=(0,d.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var u=t(17313),p=t(13861),m=t(63143),h=t(88233),x=t(29523),f=t(89667),b=t(44493),v=t(96834),g=t(15079),y=t(29617),j=t(29867);function N(){let[e,s]=(0,a.useState)([]),[t,d]=(0,a.useState)(!0),[N,w]=(0,a.useState)(""),[A,k]=(0,a.useState)(""),[C,q]=(0,a.useState)(""),[P,_]=(0,a.useState)(1),[E,S]=(0,a.useState)(1),[M,D]=(0,a.useState)(0),{toast:L}=(0,j.d)(),T=async(e=1,t="",r="",a)=>{try{d(!0);let i=await y.dG.getAll({page:e,limit:10,search:t||void 0,role:r||void 0,isEmailVerified:a,sortBy:"createdAt",sortOrder:"desc"});i.success&&i.data&&(s(i.data.users),D(i.data.total),S(i.data.totalPages),_(i.data.page))}catch(e){console.error("Error fetching users:",e),L({title:"Error",description:"Failed to fetch users",variant:"destructive"})}finally{d(!1)}},R=e=>{w(e),_(1)},U=e=>{T(e,N,A,"verified"===C||"unverified"!==C&&void 0)},z=async(e,s)=>{if(confirm(`Are you sure you want to delete user ${s}?`))try{await y.dG.delete(e),L({title:"Success",description:"User deleted successfully"}),T(P,N,A,"verified"===C||"unverified"!==C&&void 0)}catch(e){console.error("Error deleting user:",e),L({title:"Error",description:"Failed to delete user",variant:"destructive"})}},V=e=>e.isEmailVerified?(0,r.jsx)(v.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Verified"}):(0,r.jsx)(v.E,{variant:"secondary",children:"Unverified"});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Users"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage users in the CircuLab platform"})]}),(0,r.jsxs)(x.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add User"]})]}),(0,r.jsxs)(b.Zp,{children:[(0,r.jsxs)(b.aR,{children:[(0,r.jsx)(b.ZB,{children:"Search Users"}),(0,r.jsx)(b.BT,{children:"Find users by email, username, or filter by role and verification status"})]}),(0,r.jsx)(b.Wu,{children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(f.p,{placeholder:"Search users...",value:N,onChange:e=>R(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(g.l6,{value:A,onValueChange:k,children:[(0,r.jsx)(g.bq,{className:"w-48",children:(0,r.jsx)(g.yv,{placeholder:"Filter by role"})}),(0,r.jsxs)(g.gC,{children:[(0,r.jsx)(g.eb,{value:"",children:"All roles"}),(0,r.jsx)(g.eb,{value:"super_admin",children:"Super Admin"}),(0,r.jsx)(g.eb,{value:"company_admin",children:"Company Admin"}),(0,r.jsx)(g.eb,{value:"waste_producer",children:"Waste Producer"}),(0,r.jsx)(g.eb,{value:"waste_processor",children:"Waste Processor"}),(0,r.jsx)(g.eb,{value:"viewer",children:"Viewer"})]})]}),(0,r.jsxs)(g.l6,{value:C,onValueChange:q,children:[(0,r.jsx)(g.bq,{className:"w-48",children:(0,r.jsx)(g.yv,{placeholder:"Filter by status"})}),(0,r.jsxs)(g.gC,{children:[(0,r.jsx)(g.eb,{value:"",children:"All statuses"}),(0,r.jsx)(g.eb,{value:"verified",children:"Verified"}),(0,r.jsx)(g.eb,{value:"unverified",children:"Unverified"})]})]})]})})]}),(0,r.jsx)("div",{className:"grid gap-4",children:t?(0,r.jsx)("div",{className:"text-center py-8",children:"Loading users..."}):0===e.length?(0,r.jsx)(b.Zp,{children:(0,r.jsxs)(b.Wu,{className:"text-center py-8",children:[(0,r.jsx)(l,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No users found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:N||A||C?"No users match your search criteria.":"Get started by adding your first user."}),(0,r.jsxs)(x.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add User"]})]})}):e.map(e=>(0,r.jsx)(b.Zp,{children:(0,r.jsx)(b.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)(l,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.username||e.email}),V(e),e.roles.map(e=>(0,r.jsxs)(v.E,{variant:"outline",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1"}),e.role.name]},e.role.id))]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,r.jsx)(c,{className:"h-3 w-3"}),e.email]})]}),e.company&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Company"}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),e.company.name]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Created"}),(0,r.jsx)("p",{className:"text-sm",children:new Date(e.createdAt).toLocaleDateString()})]}),e.lastLoginAt&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Last Login"}),(0,r.jsx)("p",{className:"text-sm",children:new Date(e.lastLoginAt).toLocaleDateString()})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,r.jsx)(x.$,{variant:"outline",size:"sm",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>z(e.id,e.email),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})]})})},e.id))}),E>1&&(0,r.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,r.jsx)(x.$,{variant:"outline",onClick:()=>U(P-1),disabled:1===P,children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center px-4",children:["Page ",P," of ",E]}),(0,r.jsx)(x.$,{variant:"outline",onClick:()=>U(P+1),disabled:P===E,children:"Next"})]})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29867:(e,s,t)=>{"use strict";t.d(s,{d:()=>l});var r=t(43210);let a={toasts:[]},i=[];function n(e){switch(e.type){case"ADD_TOAST":a={...a,toasts:[...a.toasts,e.payload]};break;case"REMOVE_TOAST":a={...a,toasts:a.toasts.filter(s=>s.id!==e.payload)};break;case"CLEAR_TOASTS":a={...a,toasts:[]}}i.forEach(e=>e(a))}function d({title:e,description:s,variant:t="default",duration:r=5e3}){let a=Math.random().toString(36).substr(2,9);return n({type:"ADD_TOAST",payload:{id:a,title:e,description:s,variant:t,duration:r}}),r>0&&setTimeout(()=>{n({type:"REMOVE_TOAST",payload:a})},r),a}function l(){let[e,s]=(0,r.useState)(a),t=(0,r.useCallback)(e=>(i.push(e),()=>{i=i.filter(s=>s!==e)}),[]);return(0,r.useCallback)(()=>{i=[]},[]),r.useEffect(()=>t(s),[t]),{toast:d,toasts:e.toasts,dismiss:e=>{n({type:"REMOVE_TOAST",payload:e})},clear:()=>{n({type:"CLEAR_TOASTS"})}}}},33873:e=>{"use strict";e.exports=require("path")},47147:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58782:(e,s,t)=>{Promise.resolve().then(t.bind(t,23077))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75450:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),d=t(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let o={children:["",{children:["(dashboard)",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47147)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/users/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,type:s,...t},a)=>(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var r=t(60687);t(43210);var a=t(95578),i=t(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,178,60,226,658,607,793,737,33,73,414,617],()=>t(75450));module.exports=r})();
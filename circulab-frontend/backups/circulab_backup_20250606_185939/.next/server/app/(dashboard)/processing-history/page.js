(()=>{var e={};e.id=473,e.ids=[473],e.modules={447:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),i=s(96474);let n=(0,s(62688).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var o=s(19080),d=s(17313),c=s(40228),l=s(13861),p=s(63143),u=s(88233),m=s(29523),x=s(89667),h=s(44493),g=s(96834),v=s(29617),f=s(29867),j=s(22246);function y(){let[e,t]=(0,a.useState)([]),[s,y]=(0,a.useState)(!0),[b,N]=(0,a.useState)(""),[w,P]=(0,a.useState)(""),[A,k]=(0,a.useState)(""),[q,M]=(0,a.useState)(""),[C,D]=(0,a.useState)(1),[_,E]=(0,a.useState)(1),[S,T]=(0,a.useState)(0),{toast:L}=(0,f.d)(),Q=async(e=1)=>{try{y(!0);let s=await v.gK.getAll({page:e,limit:10,wasteType:b||void 0,treatmentMethod:w||void 0,dateFrom:A||void 0,dateTo:q||void 0,sortBy:"dateProcessed",sortOrder:"desc"});s.success&&s.data&&(t(s.data.processingHistory),T(s.data.total),E(s.data.totalPages),D(s.data.page))}catch(e){console.error("Error fetching processing history:",e),L({title:"Error",description:"Failed to fetch processing history",variant:"destructive"})}finally{y(!1)}},z=e=>{Q(e)},U=async e=>{if(confirm("Are you sure you want to delete this processing record?"))try{await v.gK.delete(e),L({title:"Success",description:"Processing record deleted successfully"}),Q(C)}catch(e){console.error("Error deleting processing record:",e),L({title:"Error",description:"Failed to delete processing record",variant:"destructive"})}},$=(e,t)=>null==e?"N/A":`${e} ${t}`,R=(e,t)=>e&&t&&0!==e?Math.round(t/e*100):null;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Processing History"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Track waste material processing activities and outcomes"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.IT,{history:e}),(0,r.jsxs)(m.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Record Processing"]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Filter Processing Records"}),(0,r.jsx)(h.BT,{children:"Filter by waste type, treatment method, and date range"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsx)(x.p,{placeholder:"Waste type...",value:b,onChange:e=>N(e.target.value)}),(0,r.jsx)(x.p,{placeholder:"Treatment method...",value:w,onChange:e=>P(e.target.value)}),(0,r.jsx)(x.p,{type:"date",placeholder:"From date",value:A,onChange:e=>k(e.target.value)}),(0,r.jsx)(x.p,{type:"date",placeholder:"To date",value:q,onChange:e=>M(e.target.value)})]})})]}),(0,r.jsx)("div",{className:"grid gap-4",children:s?(0,r.jsx)("div",{className:"text-center py-8",children:"Loading processing history..."}):0===e.length?(0,r.jsx)(h.Zp,{children:(0,r.jsxs)(h.Wu,{className:"text-center py-8",children:[(0,r.jsx)(n,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No processing records found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:b||w||A||q?"No records match your filter criteria.":"Get started by recording your first processing activity."}),(0,r.jsxs)(m.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Record Processing"]})]})}):e.map(e=>(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)(n,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("h3",{className:"text-lg font-semibold",children:[e.wasteMaterial.type," Processing"]}),(0,r.jsx)(g.E,{variant:"outline",children:e.treatmentMethod.name})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Waste Material"}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3"}),$(e.wasteMaterial.quantity,e.wasteMaterial.unit)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Processor"}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,r.jsx)(d.A,{className:"h-3 w-3"}),e.processorCompany.name]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Date Processed"}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-1",children:[(0,r.jsx)(c.A,{className:"h-3 w-3"}),new Date(e.dateProcessed).toLocaleDateString()]})]}),e.inputQuantity&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Input Quantity"}),(0,r.jsx)("p",{className:"text-sm",children:$(e.inputQuantity,e.wasteMaterial.unit)})]}),e.outputQuantity&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Output Quantity"}),(0,r.jsx)("p",{className:"text-sm",children:$(e.outputQuantity,e.wasteMaterial.unit)})]}),e.inputQuantity&&e.outputQuantity&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Efficiency"}),(0,r.jsxs)("p",{className:"text-sm",children:[R(e.inputQuantity,e.outputQuantity),"%"]})]})]}),e.outputMaterialType&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Output Material Type"}),(0,r.jsx)("p",{className:"text-sm",children:e.outputMaterialType})]}),e.notes&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Notes"}),(0,r.jsx)("p",{className:"text-sm",children:e.notes})]}),e.environmentalImpactAchieved&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Environmental Impact"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:Object.entries(e.environmentalImpactAchieved).map(([e,t])=>(0,r.jsxs)(g.E,{variant:"secondary",className:"text-xs",children:[e,": ",t]},e))})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:["Recorded ",new Date(e.createdAt).toLocaleDateString()]}),e.processedByUser&&(0,r.jsxs)("span",{children:["by ",e.processedByUser.username||e.processedByUser.email]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,r.jsx)(m.$,{variant:"outline",size:"sm",children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}),(0,r.jsx)(m.$,{variant:"outline",size:"sm",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(m.$,{variant:"outline",size:"sm",onClick:()=>U(e.id),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]})})},e.id))}),_>1&&(0,r.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,r.jsx)(m.$,{variant:"outline",onClick:()=>z(C-1),disabled:1===C,children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center px-4",children:["Page ",C," of ",_]}),(0,r.jsx)(m.$,{variant:"outline",onClick:()=>z(C+1),disabled:C===_,children:"Next"})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14424:(e,t,s)=>{Promise.resolve().then(s.bind(s,74625))},19080:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24152:(e,t,s)=>{Promise.resolve().then(s.bind(s,447))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73044:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let c={children:["",{children:["(dashboard)",{children:["processing-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74625)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/processing-history/page",pathname:"/processing-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},74625:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/processing-history/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,178,60,226,658,607,793,737,440,73,414,617,519],()=>s(73044));module.exports=r})();
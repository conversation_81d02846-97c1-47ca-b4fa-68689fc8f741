(()=>{var e={};e.id=558,e.ids=[558],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7761:(e,s,t)=>{Promise.resolve().then(t.bind(t,44070))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42609:(e,s,t)=>{Promise.resolve().then(t.bind(t,91804))},44070:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(60687),a=t(43210),i=t(96474),n=t(99270),o=t(17313),c=t(13861),d=t(63143),l=t(88233),p=t(29523),m=t(89667),u=t(44493),x=t(96834),h=t(29617),f=t(29867),j=t(22246);function v(){let[e,s]=(0,a.useState)([]),[t,v]=(0,a.useState)(!0),[b,g]=(0,a.useState)(""),[y,N]=(0,a.useState)(1),[A,P]=(0,a.useState)(1),[C,w]=(0,a.useState)(0),{toast:q}=(0,f.d)(),k=async(e=1,t="")=>{try{v(!0);let r=await h.yq.getAll({page:e,limit:10,search:t||void 0,sortBy:"createdAt",sortOrder:"desc"});r.success&&r.data&&(s(r.data.companies),w(r.data.total),P(r.data.totalPages),N(r.data.page))}catch(e){console.error("Error fetching companies:",e),q({title:"Error",description:"Failed to fetch companies",variant:"destructive"})}finally{v(!1)}},_=e=>{g(e),N(1)},D=e=>{k(e,b)},E=async(e,s)=>{if(confirm(`Are you sure you want to delete ${s}?`))try{await h.yq.delete(e),q({title:"Success",description:"Company deleted successfully"}),k(y,b)}catch(e){console.error("Error deleting company:",e),q({title:"Error",description:"Failed to delete company",variant:"destructive"})}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Companies"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage companies in the CircuLab platform"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.OR,{companies:e}),(0,r.jsxs)(p.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add Company"]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsx)(u.ZB,{children:"Search Companies"}),(0,r.jsx)(u.BT,{children:"Find companies by name, contact person, or email"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"flex gap-4",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(m.p,{placeholder:"Search companies...",value:b,onChange:e=>_(e.target.value),className:"pl-10"})]})})})]}),(0,r.jsx)("div",{className:"grid gap-4",children:t?(0,r.jsx)("div",{className:"text-center py-8",children:"Loading companies..."}):0===e.length?(0,r.jsx)(u.Zp,{children:(0,r.jsxs)(u.Wu,{className:"text-center py-8",children:[(0,r.jsx)(o.A,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No companies found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:b?"No companies match your search criteria.":"Get started by adding your first company."}),(0,r.jsxs)(p.$,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Add Company"]})]})}):e.map(e=>(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),e.industryType&&(0,r.jsx)(x.E,{variant:"secondary",children:e.industryType})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.siret&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"SIRET"}),(0,r.jsx)("p",{className:"text-sm",children:e.siret})]}),e.address&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Address"}),(0,r.jsx)("p",{className:"text-sm",children:e.address})]}),e.contactPerson&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Contact Person"}),(0,r.jsx)("p",{className:"text-sm",children:e.contactPerson})]}),e.contactEmail&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Contact Email"}),(0,r.jsx)("p",{className:"text-sm",children:e.contactEmail})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-4 mt-4 text-sm text-muted-foreground",children:(0,r.jsxs)("span",{children:["Created ",new Date(e.createdAt).toLocaleDateString()]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,r.jsx)(p.$,{variant:"outline",size:"sm",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}),(0,r.jsx)(p.$,{variant:"outline",size:"sm",children:(0,r.jsx)(d.A,{className:"h-4 w-4"})}),(0,r.jsx)(p.$,{variant:"outline",size:"sm",onClick:()=>E(e.id,e.name),children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})]})]})})},e.id))}),A>1&&(0,r.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,r.jsx)(p.$,{variant:"outline",onClick:()=>D(y-1),disabled:1===y,children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center px-4",children:["Page ",y," of ",A]}),(0,r.jsx)(p.$,{variant:"outline",onClick:()=>D(y+1),disabled:y===A,children:"Next"})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58454:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(s,c);let d={children:["",{children:["(dashboard)",{children:["companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91804)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/companies/page",pathname:"/companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91804:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(dashboard)/companies/page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,178,60,226,658,607,793,737,440,73,414,617,519],()=>t(58454));module.exports=r})();
(()=>{var e={};e.id=302,e.ids=[302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12234:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var s=r(51060);class n{constructor(){this.instance=s.A.create({baseURL:"http://localhost:4000/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.instance.interceptors.response.use(e=>e,async e=>{let t=e.config;return e.response?.status!==401||t._retry||(t._retry=!0),Promise.reject(e)})}async get(e,t){try{return(await this.instance.get(e,t)).data}catch(e){throw this.handleError(e)}}async post(e,t,r){try{return(await this.instance.post(e,t,r)).data}catch(e){throw this.handleError(e)}}async put(e,t,r){try{return(await this.instance.put(e,t,r)).data}catch(e){throw this.handleError(e)}}async patch(e,t,r){try{return(await this.instance.patch(e,t,r)).data}catch(e){throw this.handleError(e)}}async delete(e,t){try{return(await this.instance.delete(e,t)).data}catch(e){throw this.handleError(e)}}handleError(e){if(e.response){let t=Error(e.response.data?.message||e.response.data?.error||"An error occurred");return t.response=e.response,t}return e.request?Error("Network error - please check your connection"):Error(e.message||"An unexpected error occurred")}}let i=new n},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26787:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var s=r(43210);let n=e=>{let t,r=new Set,s=(e,s)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=s?s:"object"!=typeof n||null===n)?n:Object.assign({},t,n),r.forEach(r=>r(t,e))}},n=()=>t,i={setState:s,getState:n,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(s,n,i);return i},i=e=>e?n(e):n,a=e=>e,o=e=>{let t=i(e),r=e=>(function(e,t=a){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48024:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx","default")},48871:(e,t,r)=>{Promise.resolve().then(r.bind(r,48024))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59350:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>n});let s=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>s(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},n=(e,t)=>(r,n,i)=>{let a,o={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let s=e=>null===e?null:JSON.parse(e,void 0),n=null!=(t=r.getItem(e))?t:null;return n instanceof Promise?n.then(s):s(n)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,c=new Set,d=new Set,u=o.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},n,i);let h=()=>{let e=o.partialize({...n()});return u.setItem(o.name,{state:e,version:o.version})},p=i.setState;i.setState=(e,t)=>{p(e,t),h()};let g=e((...e)=>{r(...e),h()},n,i);i.getInitialState=()=>g;let m=()=>{var e,t;if(!u)return;l=!1,c.forEach(e=>{var t;return e(null!=(t=n())?t:g)});let i=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=n())?e:g))||void 0;return s(u.getItem.bind(u))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,i]=e;if(r(a=o.merge(i,null!=(t=n())?t:g),!0),s)return h()}).then(()=>{null==i||i(a,void 0),a=n(),l=!0,d.forEach(e=>e(a))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{o={...o,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>m(),hasHydrated:()=>l,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},o.skipHydration||m(),a||g}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68286:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),n=r(43210),i=r(99720);function a(){let e=(0,i.n)(),[t,r]=(0,n.useState)(null),[a,o]=(0,n.useState)(null);return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83D\uDD0D CircuLab Debug Page"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDD10 Auth Store State"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Is Loading:"})," ",e.isLoading?"✅ True":"❌ False"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Is Authenticated:"})," ",e.isAuthenticated?"✅ True":"❌ False"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"User:"})," ",e.user?"✅ Present":"❌ Null"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tokens:"})," ",e.tokens?"✅ Present":"❌ Null"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",e.error||"❌ None"]})]}),e.user&&(0,s.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,s.jsx)("strong",{children:"User Details:"}),(0,s.jsx)("pre",{className:"text-sm mt-2",children:JSON.stringify(e.user,null,2)})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83C\uDF10 API Connectivity Test"}),t?(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:`p-4 rounded ${t.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[(0,s.jsx)("strong",{children:"Status:"})," ",t.success?"✅ Success":"❌ Failed",t.success&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("strong",{children:"Response:"})," ",t.data.status," (Status: ",t.status,")"]}),!t.success&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("strong",{children:"Error:"})," ",t.error]})]})}):(0,s.jsx)("div",{children:"Testing API..."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDCBE LocalStorage Data"}),a?(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Access Token:"})," ",a.accessToken?"✅ Present":"❌ None",a.accessToken&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[a.accessToken.substring(0,50),"..."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Refresh Token:"})," ",a.refreshToken?"✅ Present":"❌ None",a.refreshToken&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[a.refreshToken.substring(0,50),"..."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Auth Storage:"})," ",a.authStorage?"✅ Present":"❌ None",a.authStorage&&(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1 break-all",children:[a.authStorage.substring(0,100),"..."]})]})]})}):(0,s.jsx)("div",{children:"Loading localStorage data..."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83D\uDEE0️ Debug Actions"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:()=>e.initialize(),className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-4",children:"\uD83D\uDD04 Re-initialize Auth"}),(0,s.jsx)("button",{onClick:()=>{},className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 mr-4",children:"\uD83D\uDDD1️ Clear Storage & Reload"}),(0,s.jsx)("button",{onClick:()=>window.location.href="/login",className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-4",children:"\uD83D\uDD10 Go to Login"}),(0,s.jsx)("button",{onClick:()=>window.location.href="/dashboard",className:"bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600",children:"\uD83D\uDCCA Go to Dashboard"})]})]})]})})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},77314:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48024)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/debug/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79135:(e,t,r)=>{Promise.resolve().then(r.bind(r,68286))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99720:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var s=r(26787),n=r(59350),i=r(12234);let a=(0,s.v)()((0,n.Zr)((e,t)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!0,error:null,login:async t=>{try{e({isLoading:!0,error:null});let r=await i.u.post("/auth/login",t);if(r.success&&r.data){let{user:t,tokens:s}=r.data;e({user:t,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Login failed")}catch(t){throw e({error:t.response?.data?.message||t.message||"Login failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},register:async t=>{try{e({isLoading:!0,error:null});let r=await i.u.post("/auth/register",t);if(r.success&&r.data){let{user:t,tokens:s}=r.data;e({user:t,tokens:s,isAuthenticated:!0,isLoading:!1,error:null})}else throw Error(r.message||"Registration failed")}catch(t){throw e({error:t.response?.data?.message||t.message||"Registration failed",isLoading:!1,isAuthenticated:!1,user:null,tokens:null}),t}},logout:()=>{e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshToken:async()=>{try{throw Error("Cannot refresh token on server-side")}catch(e){throw t().logout(),e}},fetchProfile:async()=>{try{console.log("\uD83D\uDD04 Fetching user profile..."),e({isLoading:!0,error:null});let t=await i.u.get("/auth/profile");if(console.log("\uD83D\uDCCB Profile response:",t),t.success&&t.data)console.log("✅ Profile fetched successfully:",t.data.email),e({user:t.data,isAuthenticated:!0,isLoading:!1});else throw console.log("❌ Profile fetch failed: Invalid response"),Error("Failed to fetch profile")}catch(t){throw console.error("❌ Profile fetch error:",t),e({error:t.response?.data?.message||t.message||"Failed to fetch profile",isLoading:!1,user:null,isAuthenticated:!1}),t}},clearError:()=>e({error:null}),setLoading:t=>e({isLoading:t}),initialize:async()=>{try{console.log("\uD83D\uDD04 Initializing auth store..."),console.log("\uD83D\uDEAB Server-side rendering, skipping token check"),e({isLoading:!1});return}catch(t){console.error("❌ Auth initialization error:",t),e({isLoading:!1,isAuthenticated:!1,user:null,tokens:null,error:"Initialization failed"})}}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,178,60,658,73],()=>r(77314));module.exports=s})();
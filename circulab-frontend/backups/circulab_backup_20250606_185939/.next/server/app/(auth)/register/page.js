(()=>{var e={};e.id=678,e.ids=[678],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11657:(e,s,r)=>{Promise.resolve().then(r.bind(r,96366))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21385:(e,s,r)=>{Promise.resolve().then(r.bind(r,99308))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43650:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let l={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99308)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96366:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(60687),a=r(43210),i=r(16189),n=r(27605),o=r(63442),d=r(9275),l=r(85814),c=r.n(l),m=r(29523),u=r(89667),p=r(80013),x=r(44493),h=r(99720);let f=d.z.object({email:d.z.string().email("Please enter a valid email address"),username:d.z.string().min(3,"Username must be at least 3 characters"),password:d.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:d.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function b(){let e=(0,i.useRouter)(),{register:s,isLoading:r,error:d,clearError:l}=(0,h.n)(),[b,v]=(0,a.useState)(!1),{register:j,handleSubmit:w,formState:{errors:g}}=(0,n.mN)({resolver:(0,o.u)(f)}),N=async r=>{try{l(),await s({email:r.email,username:r.username,password:r.password}),e.push("/dashboard")}catch(e){}};return(0,t.jsxs)("div",{className:"min-h-screen flex",children:[(0,t.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary to-primary/80 relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/10"}),(0,t.jsx)("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:(0,t.jsxs)("div",{className:"max-w-md",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"CircuLab"}),(0,t.jsx)("p",{className:"text-xl mb-6 text-white/90",children:"Join the Circular Economy"}),(0,t.jsx)("p",{className:"text-white/80 leading-relaxed",children:"Create your account to start transforming waste into valuable resources. Connect with treatment facilities and contribute to sustainable practices."})]})}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full translate-y-24 -translate-x-24"})]}),(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 animate-fade-in",children:[(0,t.jsxs)("div",{className:"text-center lg:hidden",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"CircuLab"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Industrial Waste Valorization Platform"})]}),(0,t.jsxs)(x.Zp,{className:"shadow-xl border-0",children:[(0,t.jsxs)(x.aR,{className:"space-y-1 pb-6",children:[(0,t.jsx)(x.ZB,{className:"text-2xl font-bold text-center",children:"Create Account"}),(0,t.jsx)(x.BT,{className:"text-center",children:"Enter your information to get started"})]}),(0,t.jsxs)(x.Wu,{children:[(0,t.jsxs)("form",{onSubmit:w(N),className:"space-y-6",children:[d&&(0,t.jsx)("div",{className:"bg-destructive/15 text-destructive text-sm p-4 rounded-lg border border-destructive/20 animate-slide-up",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:d})]})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"Email address"}),(0,t.jsx)(u.p,{id:"email",type:"email",placeholder:"Enter your email",...j("email"),className:g.email?"border-destructive":""}),g.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:g.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"username",children:"Username"}),(0,t.jsx)(u.p,{id:"username",type:"text",placeholder:"Choose a username",...j("username"),className:g.username?"border-destructive":""}),g.username&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:g.username.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.p,{id:"password",type:b?"text":"password",placeholder:"Create a password",...j("password"),className:g.password?"border-destructive":""}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5",onClick:()=>v(!b),children:b?"Hide":"Show"})]}),g.password&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:g.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,t.jsx)(u.p,{id:"confirmPassword",type:"password",placeholder:"Confirm your password",...j("confirmPassword"),className:g.confirmPassword?"border-destructive":""}),g.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:g.confirmPassword.message})]}),(0,t.jsx)(m.$,{type:"submit",className:"w-full h-11 text-base font-medium",disabled:r,children:r?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Creating account..."})]}):"Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,t.jsx)(c(),{href:"/login",className:"text-primary hover:underline font-medium transition-colors",children:"Sign in"})]})})]})]})]})})]})}},99308:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/(auth)/register/page.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,178,60,226,658,607,469,73,931],()=>r(43650));module.exports=t})();
(()=>{var e={};e.id=13,e.ids=[13],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{DELETE:()=>h,GET:()=>u,POST:()=>c,PUT:()=>l});var s=r(96559),a=r(48088),n=r(37719),i=r(32190);let p=process.env.BACKEND_URL||"http://localhost:4000";async function u(e,t){let r=await t.params;try{let t=r.path.join("/"),o=new URL(e.url).searchParams.toString(),s=`${p}/${t}${o?`?${o}`:""}`;console.log(`🔄 Proxying GET request to backend: ${s}`);let a=await fetch(s,{method:"GET",headers:{"Content-Type":"application/json",Authorization:e.headers.get("Authorization")||""}}),n=await a.json();return i.NextResponse.json(n,{status:a.status})}catch(e){return console.error("❌ Proxy error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,t){let r=await t.params;try{let t=r.path.join("/"),o=await e.json(),s=`${p}/${t}`;console.log(`🔄 Proxying POST request to backend: ${s}`);let a=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json",Authorization:e.headers.get("Authorization")||""},body:JSON.stringify(o)}),n=await a.json();return i.NextResponse.json(n,{status:a.status})}catch(e){return console.error("❌ Proxy error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e,t){let r=await t.params;try{let t=r.path.join("/"),o=await e.json(),s=`${p}/${t}`;console.log(`🔄 Proxying PUT request to backend: ${s}`);let a=await fetch(s,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:e.headers.get("Authorization")||""},body:JSON.stringify(o)}),n=await a.json();return i.NextResponse.json(n,{status:a.status})}catch(e){return console.error("❌ Proxy error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,t){let r=await t.params;try{let t=r.path.join("/"),o=`${p}/${t}`;console.log(`🔄 Proxying DELETE request to backend: ${o}`);let s=await fetch(o,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:e.headers.get("Authorization")||""}}),a=await s.json();return i.NextResponse.json(a,{status:s.status})}catch(e){return console.error("❌ Proxy error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/[...path]/route",pathname:"/api/[...path]",filename:"route",bundlePath:"app/api/[...path]/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/[...path]/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:j}=d;function g(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580],()=>r(10886));module.exports=o})();
(()=>{var e={};e.id=244,e.ids=[244],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18635:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(96559),o=t(48088),n=t(37719),i=t(32190);async function p(e){try{console.log("\uD83D\uDD04 Proxying refresh token request to backend...");let r=await e.text(),t=await fetch("http://localhost:4000/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:r}),s=await t.text();return new i.NextResponse(s,{status:t.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Refresh token proxy error:",e),i.NextResponse.json({success:!1,error:{message:"Token refresh service unavailable"},timestamp:new Date().toISOString()},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/refresh/route",pathname:"/api/auth/refresh",filename:"route",bundlePath:"app/api/auth/refresh/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/refresh/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:h}=u;function l(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(18635));module.exports=s})();
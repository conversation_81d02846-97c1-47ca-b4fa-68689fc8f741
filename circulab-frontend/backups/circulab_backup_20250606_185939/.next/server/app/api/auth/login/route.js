(()=>{var e={};e.id=758,e.ids=[758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},62424:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function p(e){try{console.log("\uD83D\uDD04 Proxying login request to backend...");let t=await e.text(),r=await fetch("http://localhost:4000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:t}),s=await r.text();return new i.NextResponse(s,{status:r.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Login proxy error:",e),i.NextResponse.json({success:!1,error:{message:"Authentication service unavailable"},timestamp:new Date().toISOString()},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/login/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=u;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(62424));module.exports=s})();
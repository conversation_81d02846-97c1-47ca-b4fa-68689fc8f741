(()=>{var e={};e.id=810,e.ids=[810],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66353:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var o=r(96559),a=r(48088),n=r(37719),i=r(32190);async function p(e){try{console.log("\uD83D\uDD04 Proxying profile request to backend...");let t=e.headers.get("authorization"),r={"Content-Type":"application/json"};t&&(r.Authorization=t);let s=await fetch("http://localhost:4000/api/auth/profile",{method:"GET",headers:r}),o=await s.text();return new i.NextResponse(o,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Profile proxy error:",e),i.NextResponse.json({success:!1,error:{message:"Profile service unavailable"},timestamp:new Date().toISOString()},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/profile/route",pathname:"/api/auth/profile",filename:"route",bundlePath:"app/api/auth/profile/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/auth/profile/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:d}=u;function h(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(66353));module.exports=s})();
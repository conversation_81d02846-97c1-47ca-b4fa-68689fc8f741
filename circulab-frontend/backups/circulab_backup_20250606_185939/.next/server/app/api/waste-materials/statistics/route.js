(()=>{var e={};e.id=481,e.ids=[481],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94242:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>p});var a=s(96559),i=s(48088),o=s(37719),n=s(32190);async function p(e){try{console.log("\uD83D\uDD04 Proxying statistics request to backend...");let t=e.nextUrl.searchParams.toString(),s=`http://localhost:4000/api/waste-materials/statistics${t?`?${t}`:""}`,r={"Content-Type":"application/json"},a=e.headers.get("authorization");a&&(r.Authorization=a);let i=await fetch(s,{method:"GET",headers:r}),o=await i.text();return new n.NextResponse(o,{status:i.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Statistics proxy error:",e),n.NextResponse.json({success:!1,error:{message:"Statistics service unavailable"},timestamp:new Date().toISOString()},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/waste-materials/statistics/route",pathname:"/api/waste-materials/statistics",filename:"route",bundlePath:"app/api/waste-materials/statistics/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/statistics/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:l}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(94242));module.exports=r})();
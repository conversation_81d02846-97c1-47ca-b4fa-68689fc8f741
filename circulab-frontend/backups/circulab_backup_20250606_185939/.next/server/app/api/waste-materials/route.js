(()=>{var e={};e.id=85,e.ids=[85],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},82295:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function p(e){return c(e,"GET")}async function u(e){return c(e,"POST")}async function c(e,t){try{console.log(`🔄 Proxying ${t} waste-materials request to backend...`);let r=e.nextUrl.searchParams.toString(),s=`http://localhost:4000/api/waste-materials${r?`?${r}`:""}`,a={"Content-Type":"application/json"},n=e.headers.get("authorization");n&&(a.Authorization=n);let o={method:t,headers:a};if("POST"===t){let t=await e.text();t&&(o.body=t)}let p=await fetch(s,o),u=await p.text();return new i.NextResponse(u,{status:p.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error(`Waste materials ${t} proxy error:`,e),i.NextResponse.json({success:!1,error:{message:"Waste materials service unavailable"},timestamp:new Date().toISOString()},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/waste-materials/route",pathname:"/api/waste-materials",filename:"route",bundlePath:"app/api/waste-materials/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/waste-materials/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:m}=l;function h(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(82295));module.exports=s})();
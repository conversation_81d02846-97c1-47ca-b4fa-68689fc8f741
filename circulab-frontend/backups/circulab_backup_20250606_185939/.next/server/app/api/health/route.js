(()=>{var e={};e.id=772,e.ids=[772],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96996:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function p(e){try{console.log("\uD83D\uDD04 Proxying health check to backend...");let e=await fetch("http://localhost:4000/health",{method:"GET",headers:{"Content-Type":"application/json"}}),t=await e.text();return new i.NextResponse(t,{status:e.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Health check proxy error:",e),i.NextResponse.json({success:!1,error:{message:"Backend connection failed"},timestamp:new Date().toISOString()},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/api/health/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:h}=c;function l(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(96996));module.exports=s})();
(()=>{var e={};e.id=653,e.ids=[653],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(29270),a=t(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10864:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var r=t(60687),a=t(43210);class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}validateModuleResolution(){try{let e=[];if(["react","next","@/components/providers/AuthProvider","@/store/authStore","@/lib/apiClient"].forEach(e=>{}),0===e.length)return{status:"healthy",message:"All critical modules resolved successfully",timestamp:new Date};return{status:"error",message:`Failed to resolve ${e.length} modules`,details:{failedModules:e,errors:this.moduleErrors},timestamp:new Date}}catch(e){return{status:"error",message:"Module resolution check failed",details:{error:e.message},timestamp:new Date}}}checkModuleAvailability(e){return!0}monitorHotReload(){return{status:"healthy",message:"Server-side rendering - hot reload not applicable",timestamp:new Date}}reportHotReloadIssue(){this.hotReloadIssues++}reset(){this.moduleErrors=[],this.hotReloadIssues=0,this.lastCheck=new Date}constructor(){this.moduleErrors=[],this.hotReloadIssues=0,this.lastCheck=new Date}}class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}checkExtensionCompatibility(){return{status:"healthy",message:"Server-side - extension check not applicable",timestamp:new Date}}detectAugmentExtension(){return!1}checkAugmentConflicts(){return!1}}class l{static checkDependencies(){try{let e=[{name:"React",check:()=>!1},{name:"Next.js",check:()=>!0},{name:"Zustand",check:()=>!0},{name:"Axios",check:()=>!0}].filter(e=>{try{return!e.check()}catch{return!0}});if(0===e.length)return{status:"healthy",message:"All critical dependencies available",timestamp:new Date};return{status:"error",message:`Missing dependencies: ${e.map(e=>e.name).join(", ")}`,details:{failedDeps:e.map(e=>e.name)},timestamp:new Date}}catch(e){return{status:"error",message:"Dependency check failed",details:{error:e.message},timestamp:new Date}}}}class c{static async checkAPIHealth(){try{let e=await fetch("/api/health",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)return{status:"error",message:`API returned ${e.status}: ${e.statusText}`,timestamp:new Date};{let s=await e.json();return{status:"healthy",message:"API is responding correctly",details:s,timestamp:new Date}}}catch(e){return{status:"error",message:"API health check failed",details:{error:e.message},timestamp:new Date}}}}class d{static checkLocalStorage(){return{status:"healthy",message:"Server-side - localStorage not applicable",timestamp:new Date}}}class o{constructor(){this.webpackMonitor=i.getInstance(),this.extensionChecker=n.getInstance()}static getInstance(){return o.instance||(o.instance=new o),o.instance}async performFullHealthCheck(){let e=this.webpackMonitor.validateModuleResolution(),s=this.extensionChecker.checkExtensionCompatibility(),t=l.checkDependencies(),r=await c.checkAPIHealth(),a=d.checkLocalStorage(),i=[e,s,t,r,a],n=i.filter(e=>"healthy"===e.status).length,o=i.filter(e=>"warning"===e.status).length,m=i.filter(e=>"error"===e.status).length,h=Math.round((100*n+50*o+0*m)/i.length),u="healthy",x="All systems operational";return m>0?(u="error",x=`${m} critical issues detected`):o>0&&(u="warning",x=`${o} warnings detected`),{webpack:e,extensions:s,dependencies:t,api:r,localStorage:a,overall:{status:u,message:x,details:{healthyCount:n,warningCount:o,errorCount:m},timestamp:new Date},confidence:h}}reportHotReloadIssue(){this.webpackMonitor.reportHotReloadIssue()}reset(){this.webpackMonitor.reset()}}var m=t(44493),h=t(96834),u=t(29523),x=t(62688);let p=(0,x.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),f=(0,x.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),g=(0,x.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),v=(0,x.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var j=t(99891);let b=(0,x.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),y=(0,x.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),N=(0,x.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),w=(0,x.A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),k=(0,x.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);function C({isMinimized:e=!1,onToggle:s}){var t;let[i,n]=(0,a.useState)(null),[l,c]=(0,a.useState)(!1),[d,x]=(0,a.useState)(null),C=o.getInstance(),A=async()=>{c(!0);try{let e=await C.performFullHealthCheck();n(e),x(new Date)}catch(e){console.error("Health check failed:",e)}finally{c(!1)}},D=e=>{switch(e){case"healthy":return(0,r.jsx)(p,{className:"h-4 w-4 text-green-500"});case"warning":return(0,r.jsx)(f,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,r.jsx)(g,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(v,{className:"h-4 w-4 text-gray-500"})}},R=e=>{switch(e){case"healthy":return"bg-green-100 text-green-800 border-green-200";case"warning":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"error":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return e?(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,r.jsxs)(u.$,{onClick:s,variant:"outline",size:"sm",className:`shadow-lg ${i?R(i.overall.status):"bg-gray-100"}`,children:[i&&D(i.overall.status),(0,r.jsxs)("span",{className:"ml-2",children:["Health: ",i?`${i.confidence}%`:"..."]})]})}):(0,r.jsxs)(m.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,r.jsxs)(m.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5"}),"System Health Dashboard"]}),(0,r.jsx)(m.BT,{children:"Real-time monitoring of application health and stability"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[d&&(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Last check: ",d.toLocaleTimeString()]}),(0,r.jsxs)(u.$,{onClick:A,disabled:l,size:"sm",variant:"outline",children:[(0,r.jsx)(b,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"Refresh"]}),s&&(0,r.jsx)(u.$,{onClick:s,size:"sm",variant:"ghost",children:"Minimize"})]})]}),(0,r.jsx)(m.Wu,{children:i?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[D(i.overall.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"Overall System Health"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:i.overall.message})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:`text-2xl font-bold ${(t=i.confidence)>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600"}`,children:[i.confidence,"%"]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Confidence"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(y,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Webpack"}),D(i.webpack.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.webpack.message}),(0,r.jsx)(h.E,{variant:"outline",className:R(i.webpack.status),children:i.webpack.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(v,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Extensions"}),D(i.extensions.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.extensions.message}),(0,r.jsx)(h.E,{variant:"outline",className:R(i.extensions.status),children:i.extensions.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(N,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Dependencies"}),D(i.dependencies.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.dependencies.message}),(0,r.jsx)(h.E,{variant:"outline",className:R(i.dependencies.status),children:i.dependencies.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(w,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"API"}),D(i.api.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.api.message}),(0,r.jsx)(h.E,{variant:"outline",className:R(i.api.status),children:i.api.status})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(k,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Storage"}),D(i.localStorage.status)]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:i.localStorage.message}),(0,r.jsx)(h.E,{variant:"outline",className:R(i.localStorage.status),children:i.localStorage.status})]})]}),!1,"healthy"!==i.overall.status&&(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Recommendations"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:["error"===i.webpack.status&&(0,r.jsx)("li",{children:"• Clear webpack cache and restart development server"}),"error"===i.extensions.status&&(0,r.jsx)("li",{children:"• Disable problematic VS Code extensions temporarily"}),"error"===i.dependencies.status&&(0,r.jsx)("li",{children:"• Run npm install to resolve missing dependencies"}),"error"===i.api.status&&(0,r.jsx)("li",{children:"• Check if the backend server is running on port 4000"}),"error"===i.localStorage.status&&(0,r.jsx)("li",{children:"• Clear browser cache and cookies"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(b,{className:"h-6 w-6 animate-spin mr-2"}),(0,r.jsx)("span",{children:"Performing initial health check..."})]})})]})}var A=t(53411);let D=(0,x.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function R(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{children:[(0,r.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(A.A,{className:"h-5 w-5"}),"Performance Metrics Dashboard"]}),(0,r.jsx)(m.BT,{children:"Monitor and validate the defensive architecture performance"})]}),(0,r.jsx)(m.Wu,{children:(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Metrics collection and analysis coming soon..."})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(m.ZB,{className:"text-sm font-medium",children:"System Uptime"}),(0,r.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(m.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[98,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 24 hours"}),(0,r.jsx)(h.E,{variant:"default",className:"mt-2",children:"Excellent"})]})]}),(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(m.ZB,{className:"text-sm font-medium",children:"Avg Confidence"}),(0,r.jsx)(v,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(m.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[87,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"System health score"}),(0,r.jsx)(h.E,{variant:"default",className:"mt-2",children:"Healthy"})]})]}),(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(m.ZB,{className:"text-sm font-medium",children:"Avg Recovery"}),(0,r.jsx)(D,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(m.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[4.2,"min"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Time to recover from errors"}),(0,r.jsx)(h.E,{variant:"default",className:"mt-2",children:"Fast"})]})]}),(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(m.ZB,{className:"text-sm font-medium",children:"Error Reduction"}),(0,r.jsx)(p,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(m.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[73,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Compared to baseline"}),(0,r.jsx)(h.E,{variant:"default",className:"mt-2",children:"Improved"})]})]})]}),(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{children:[(0,r.jsx)(m.ZB,{children:"Validation Criteria"}),(0,r.jsx)(m.BT,{children:"Target metrics for defensive architecture validation"})]}),(0,r.jsx)(m.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"✅ Success Criteria"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Confidence score maintained > 80% for 24h"}),(0,r.jsx)("li",{children:"• Recovery time < 5 minutes for common errors"}),(0,r.jsx)("li",{children:"• Zero development interruptions from extensions"}),(0,r.jsx)("li",{children:"• 70% reduction in debugging time"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"\uD83D\uDCCA Current Status"}),(0,r.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(p,{className:"h-4 w-4 text-green-500"}),"Confidence: ",87,"%"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(p,{className:"h-4 w-4 text-green-500"}),"Recovery: ",4.2,"min"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(p,{className:"h-4 w-4 text-green-500"}),"Error Reduction: ",73,"%"]})]})]})]})})]})]})}var M=t(30316),S=t(85763);function E(){let[e,s]=(0,a.useState)(!0),t=(0,M.W$)(),i=t.getErrors();return(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"System Health & Monitoring"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Real-time system health monitoring, error tracking, and performance metrics"})]}),(0,r.jsxs)(S.tU,{defaultValue:"health",className:"w-full",children:[(0,r.jsxs)(S.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(S.Xi,{value:"health",children:"Health Dashboard"}),(0,r.jsx)(S.Xi,{value:"metrics",children:"Performance Metrics"})]}),(0,r.jsxs)(S.av,{value:"health",className:"space-y-6",children:[(0,r.jsx)(C,{isMinimized:!e,onToggle:()=>s(!e)}),i.length>0&&(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{children:[(0,r.jsxs)(m.ZB,{className:"flex items-center justify-between",children:["Error Log (",i.length,")",(0,r.jsx)(u.$,{onClick:()=>{t.clearErrors()},variant:"outline",size:"sm",children:"Clear Errors"})]}),(0,r.jsx)(m.BT,{children:"Recent errors captured by the safe import system"})]}),(0,r.jsx)(m.Wu,{children:(0,r.jsx)("div",{className:"space-y-2 max-h-64 overflow-auto",children:i.map((e,s)=>(0,r.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-red-800",children:e.context}),(0,r.jsx)("span",{className:"text-xs text-red-600",children:e.timestamp.toLocaleTimeString()})]}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:e.error.message}),!1]},s))})})]}),(0,r.jsxs)(m.Zp,{children:[(0,r.jsxs)(m.aR,{children:[(0,r.jsx)(m.ZB,{children:"Health Monitoring Guide"}),(0,r.jsx)(m.BT,{children:"How to use the health monitoring system"})]}),(0,r.jsx)(m.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDFE2 Healthy Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"All systems are operating normally. Confidence score above 80%."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDFE1 Warning Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Some issues detected but system is functional. Confidence score 50-80%."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDD34 Error Status"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Critical issues detected. Confidence score below 50%. Immediate attention required."})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Monitored Components"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Webpack:"})," Module resolution and hot reload health"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Extensions:"})," VS Code extension compatibility"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Dependencies:"})," Critical package availability"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"API:"})," Backend connectivity and response"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Storage:"})," localStorage functionality"]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Automatic Actions"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Health checks run every 30 seconds"}),(0,r.jsx)("li",{children:"• Errors are automatically logged and categorized"}),(0,r.jsx)("li",{children:"• Safe imports provide fallbacks for failed components"}),(0,r.jsx)("li",{children:"• Recommendations are generated based on detected issues"})]})]})]})})]})]}),(0,r.jsx)(S.av,{value:"metrics",className:"space-y-6",children:(0,r.jsx)(R,{})})]})]})}},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23766:(e,s,t)=>{Promise.resolve().then(t.bind(t,91097))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var r=t(60687);t(43210);var a=t(88480),i=t(95578),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:s,size:t,asChild:i=!1,...c}){let d=i?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:t,className:e})),...c})}},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>l});var r=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},47326:(e,s,t)=>{Promise.resolve().then(t.bind(t,10864))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57912:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["health",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91097)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/health/page",pathname:"/health",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>l});var r=t(60687),a=t(43210),i=t(10424),n=t(4780);let l=i.bL,c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.B8.displayName;let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.l9.displayName;let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=i.UC.displayName},91097:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-frontend/src/app/health/page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(60687);t(43210);var a=t(95578),i=t(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,178,226,658,793,662,73],()=>t(57912));module.exports=r})();
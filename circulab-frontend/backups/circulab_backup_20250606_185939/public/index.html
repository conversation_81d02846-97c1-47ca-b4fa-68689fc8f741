<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="CircuLab - Plateforme de Valorisation des Déchets Industriels" />
    <title>CircuLab - Waste Valorization Platform</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        .loading-card {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            margin: 1rem;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 6px;
            margin: 0.5rem;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .error-message {
            color: #e53e3e;
            margin-top: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <div id="__next">
        <div class="loading-container">
            <div class="loading-card">
                <div class="logo">🌱 CircuLab</div>
                <h2>Plateforme de Valorisation des Déchets Industriels</h2>
                <p>Chargement de l'application...</p>
                <div class="spinner"></div>
                <div id="loading-status">Initialisation en cours...</div>
                <div class="error-message" id="error-message">
                    <p>Erreur de chargement de l'application.</p>
                    <a href="/api-docs" class="button">📚 Documentation API</a>
                    <a href="/health" class="button">🔍 État du Serveur</a>
                    <button onclick="window.location.reload()" class="button">🔄 Réessayer</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate loading progress
        const statusElement = document.getElementById('loading-status');
        const errorElement = document.getElementById('error-message');
        let loadingStep = 0;
        
        const loadingSteps = [
            'Initialisation en cours...',
            'Chargement des composants...',
            'Configuration de l\'interface...',
            'Connexion au serveur...',
            'Finalisation...'
        ];
        
        function updateLoadingStatus() {
            if (loadingStep < loadingSteps.length) {
                statusElement.textContent = loadingSteps[loadingStep];
                loadingStep++;
                setTimeout(updateLoadingStatus, 800);
            } else {
                // Try to redirect to the actual Next.js app
                checkAppAvailability();
            }
        }
        
        function checkAppAvailability() {
            // Check if the Next.js app is available
            fetch('/dashboard')
                .then(response => {
                    if (response.ok) {
                        // App is available, redirect
                        window.location.href = '/dashboard';
                    } else {
                        // App not available, show error
                        showError();
                    }
                })
                .catch(() => {
                    // Network error or app not available
                    showError();
                });
        }
        
        function showError() {
            document.querySelector('.spinner').style.display = 'none';
            statusElement.style.display = 'none';
            errorElement.style.display = 'block';
        }
        
        // Start loading simulation
        setTimeout(updateLoadingStatus, 500);
        
        // Auto-retry every 10 seconds
        setInterval(() => {
            if (errorElement.style.display === 'block') {
                window.location.reload();
            }
        }, 10000);
    </script>
</body>
</html>

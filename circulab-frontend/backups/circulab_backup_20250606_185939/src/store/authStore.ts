import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthTokens, LoginCredentials, RegisterData } from '@/types';
import { apiClient } from '@/lib/apiClient';

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  fetchProfile: () => Promise<void>;
  initialize: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true to check stored auth
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post('/auth/login', credentials);

          if (response.success && response.data) {
            const { user, tokens } = response.data;

            // Store tokens in localStorage (client-side only)
            if (typeof window !== 'undefined') {
              localStorage.setItem('accessToken', tokens.accessToken);
              localStorage.setItem('refreshToken', tokens.refreshToken);
            }

            set({
              user,
              tokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            throw new Error(response.message || 'Login failed');
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || error.message || 'Login failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            tokens: null,
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post('/auth/register', data);

          if (response.success && response.data) {
            const { user, tokens } = response.data;

            // Store tokens in localStorage (client-side only)
            if (typeof window !== 'undefined') {
              localStorage.setItem('accessToken', tokens.accessToken);
              localStorage.setItem('refreshToken', tokens.refreshToken);
            }

            set({
              user,
              tokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            throw new Error(response.message || 'Registration failed');
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            tokens: null,
          });
          throw error;
        }
      },

      logout: () => {
        // Clear localStorage (client-side only)
        if (typeof window !== 'undefined') {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
        }

        // Reset state
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      refreshToken: async () => {
        try {
          if (typeof window === 'undefined') {
            throw new Error('Cannot refresh token on server-side');
          }

          const refreshToken = localStorage.getItem('refreshToken');
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          const response = await apiClient.post('/auth/refresh', { refreshToken });

          if (response.success && response.data) {
            const { tokens } = response.data;

            // Update tokens in localStorage (client-side only)
            if (typeof window !== 'undefined') {
              localStorage.setItem('accessToken', tokens.accessToken);
              localStorage.setItem('refreshToken', tokens.refreshToken);
            }

            set({ tokens });
          } else {
            throw new Error('Token refresh failed');
          }
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      fetchProfile: async () => {
        try {
          console.log('🔄 Fetching user profile...');
          set({ isLoading: true, error: null });

          const response = await apiClient.get('/auth/profile');
          console.log('📋 Profile response:', response);

          if (response.success && response.data) {
            console.log('✅ Profile fetched successfully:', response.data.email);
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            console.log('❌ Profile fetch failed: Invalid response');
            throw new Error('Failed to fetch profile');
          }
        } catch (error: any) {
          console.error('❌ Profile fetch error:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch profile';
          set({
            error: errorMessage,
            isLoading: false,
            user: null,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      initialize: async () => {
        try {
          console.log('🔄 Initializing auth store...');

          // Check if we're in the browser environment
          if (typeof window === 'undefined') {
            console.log('🚫 Server-side rendering, skipping token check');
            set({ isLoading: false });
            return;
          }

          const accessToken = localStorage.getItem('accessToken');
          const refreshToken = localStorage.getItem('refreshToken');

          if (accessToken && refreshToken) {
            console.log('🔑 Found stored tokens, validating...');
            // Set tokens in state first
            set({
              tokens: { accessToken, refreshToken },
              isLoading: true
            });

            // Try to fetch profile to validate token
            try {
              await get().fetchProfile();
              console.log('✅ Profile fetched successfully');
            } catch (error) {
              console.log('❌ Profile fetch failed, trying to refresh token...');
              // If profile fetch fails, try to refresh token
              try {
                await get().refreshToken();
                await get().fetchProfile();
                console.log('✅ Token refreshed and profile fetched');
              } catch (refreshError) {
                console.log('❌ Token refresh failed, logging out');
                // If refresh also fails, logout
                get().logout();
              }
            }
          } else {
            console.log('🚫 No tokens found, setting loading to false');
            // No tokens found, set loading to false
            set({
              isLoading: false,
              isAuthenticated: false,
              user: null,
              tokens: null
            });
          }
        } catch (error) {
          console.error('❌ Auth initialization error:', error);
          set({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            tokens: null,
            error: 'Initialization failed'
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Clock,
  Shield,
  CheckCircle,
  Activity,
  BarChart3
} from 'lucide-react';

export function MetricsDashboard() {
  // Mock validation metrics
  const validationMetrics = {
    uptime: 98,
    averageConfidence: 87,
    averageRecoveryTime: 4.2,
    errorReduction: 73,
    totalIncidents: 2
  };

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Metrics Dashboard
          </CardTitle>
          <CardDescription>
            Monitor and validate the defensive architecture performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-muted-foreground">
              Metrics collection and analysis coming soon...
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Validation Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Uptime */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{validationMetrics.uptime}%</div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
            <Badge
              variant={validationMetrics.uptime > 95 ? "default" : "destructive"}
              className="mt-2"
            >
              {validationMetrics.uptime > 95 ? "Excellent" : "Needs Attention"}
            </Badge>
          </CardContent>
        </Card>

        {/* Average Confidence */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{validationMetrics.averageConfidence}%</div>
            <p className="text-xs text-muted-foreground">
              System health score
            </p>
            <Badge
              variant={validationMetrics.averageConfidence > 80 ? "default" : "secondary"}
              className="mt-2"
            >
              {validationMetrics.averageConfidence > 80 ? "Healthy" : "Warning"}
            </Badge>
          </CardContent>
        </Card>

        {/* Recovery Time */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Recovery</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{validationMetrics.averageRecoveryTime}min</div>
            <p className="text-xs text-muted-foreground">
              Time to recover from errors
            </p>
            <Badge
              variant={validationMetrics.averageRecoveryTime < 5 ? "default" : "secondary"}
              className="mt-2"
            >
              {validationMetrics.averageRecoveryTime < 5 ? "Fast" : "Acceptable"}
            </Badge>
          </CardContent>
        </Card>

        {/* Error Reduction */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Reduction</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{validationMetrics.errorReduction}%</div>
            <p className="text-xs text-muted-foreground">
              Compared to baseline
            </p>
            <Badge variant="default" className="mt-2">
              Improved
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Success Criteria */}
      <Card>
        <CardHeader>
          <CardTitle>Validation Criteria</CardTitle>
          <CardDescription>
            Target metrics for defensive architecture validation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">✅ Success Criteria</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Confidence score maintained &gt; 80% for 24h</li>
                <li>• Recovery time &lt; 5 minutes for common errors</li>
                <li>• Zero development interruptions from extensions</li>
                <li>• 70% reduction in debugging time</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">📊 Current Status</h4>
              <ul className="text-sm space-y-1">
                <li className="flex items-center gap-2">
                  {validationMetrics.averageConfidence > 80 ?
                    <CheckCircle className="h-4 w-4 text-green-500" /> :
                    <Activity className="h-4 w-4 text-yellow-500" />
                  }
                  Confidence: {validationMetrics.averageConfidence}%
                </li>
                <li className="flex items-center gap-2">
                  {validationMetrics.averageRecoveryTime < 5 ?
                    <CheckCircle className="h-4 w-4 text-green-500" /> :
                    <Activity className="h-4 w-4 text-yellow-500" />
                  }
                  Recovery: {validationMetrics.averageRecoveryTime}min
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Error Reduction: {validationMetrics.errorReduction}%
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

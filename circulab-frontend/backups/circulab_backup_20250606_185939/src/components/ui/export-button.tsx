'use client';

import { useState } from 'react';
import { Download, FileText, FileSpreadsheet, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { exportToCSV, exportToExcel, ExportColumn, ExportOptions } from '@/lib/exportUtils';

interface ExportButtonProps {
  data: any[];
  columns: ExportColumn[];
  filename?: string;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export function ExportButton({
  data,
  columns,
  filename = 'export',
  disabled = false,
  variant = 'outline',
  size = 'sm',
  className
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const handleExport = async (format: 'csv' | 'excel') => {
    if (data.length === 0) {
      toast({
        title: 'No data to export',
        description: 'There is no data available to export.',
        variant: 'destructive',
      });
      return;
    }

    setIsExporting(true);

    try {
      const options: ExportOptions = {
        filename,
        includeTimestamp: true,
      };

      if (format === 'csv') {
        exportToCSV(data, columns, options);
        toast({
          title: 'Export successful',
          description: `Data exported to CSV format (${data.length} records)`,
        });
      } else {
        exportToExcel(data, columns, options);
        toast({
          title: 'Export successful',
          description: `Data exported to Excel format (${data.length} records)`,
        });
      }
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export failed',
        description: 'An error occurred while exporting the data.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={disabled || isExporting}
          className={className}
        >
          {isExporting ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Download className="mr-2 h-4 w-4" />
          )}
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Export Format</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => handleExport('csv')}
          disabled={isExporting}
          className="cursor-pointer"
        >
          <FileText className="mr-2 h-4 w-4" />
          <div className="flex flex-col">
            <span>CSV File</span>
            <span className="text-xs text-muted-foreground">
              Comma-separated values
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleExport('excel')}
          disabled={isExporting}
          className="cursor-pointer"
        >
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          <div className="flex flex-col">
            <span>Excel File</span>
            <span className="text-xs text-muted-foreground">
              Microsoft Excel format
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem disabled className="text-xs text-muted-foreground">
          {data.length} records available
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Specialized export buttons for different data types
export function CompaniesExportButton({ 
  companies, 
  ...props 
}: Omit<ExportButtonProps, 'data' | 'columns'> & { companies: any[] }) {
  const columns: ExportColumn[] = [
    { key: 'name', label: 'Company Name' },
    { key: 'siret', label: 'SIRET' },
    { key: 'industryType', label: 'Industry Type' },
    { key: 'address', label: 'Address' },
    { key: 'contactPerson', label: 'Contact Person' },
    { key: 'contactEmail', label: 'Contact Email' },
    { 
      key: 'createdAt', 
      label: 'Created Date', 
      formatter: (value) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  return (
    <ExportButton
      data={companies}
      columns={columns}
      filename="companies"
      {...props}
    />
  );
}

export function WasteMaterialsExportButton({ 
  materials, 
  ...props 
}: Omit<ExportButtonProps, 'data' | 'columns'> & { materials: any[] }) {
  const columns: ExportColumn[] = [
    { key: 'type', label: 'Material Type' },
    { key: 'quantity', label: 'Quantity' },
    { key: 'unit', label: 'Unit' },
    { key: 'status', label: 'Status' },
    { key: 'company.name', label: 'Company' },
    { key: 'location', label: 'Location' },
    { 
      key: 'createdAt', 
      label: 'Created Date', 
      formatter: (value) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  return (
    <ExportButton
      data={materials}
      columns={columns}
      filename="waste-materials"
      {...props}
    />
  );
}

export function ProcessingHistoryExportButton({ 
  history, 
  ...props 
}: Omit<ExportButtonProps, 'data' | 'columns'> & { history: any[] }) {
  const columns: ExportColumn[] = [
    { key: 'wasteMaterial.type', label: 'Waste Type' },
    { key: 'treatmentMethod.name', label: 'Treatment Method' },
    { 
      key: 'inputQuantity', 
      label: 'Input Quantity', 
      formatter: (value) => value ? Number(value).toFixed(2) : ''
    },
    { 
      key: 'outputQuantity', 
      label: 'Output Quantity', 
      formatter: (value) => value ? Number(value).toFixed(2) : ''
    },
    { key: 'processorCompany.name', label: 'Processor' },
    { 
      key: 'dateProcessed', 
      label: 'Date Processed', 
      formatter: (value) => value ? new Date(value).toLocaleDateString() : ''
    },
    { 
      key: 'createdAt', 
      label: 'Created Date', 
      formatter: (value) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  return (
    <ExportButton
      data={history}
      columns={columns}
      filename="processing-history"
      {...props}
    />
  );
}

export function UsersExportButton({ 
  users, 
  ...props 
}: Omit<ExportButtonProps, 'data' | 'columns'> & { users: any[] }) {
  const columns: ExportColumn[] = [
    { key: 'username', label: 'Username' },
    { key: 'email', label: 'Email' },
    { key: 'role', label: 'Role' },
    { key: 'company.name', label: 'Company' },
    { 
      key: 'isActive', 
      label: 'Active', 
      formatter: (value) => value ? 'Yes' : 'No'
    },
    { 
      key: 'createdAt', 
      label: 'Created Date', 
      formatter: (value) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  return (
    <ExportButton
      data={users}
      columns={columns}
      filename="users"
      {...props}
    />
  );
}

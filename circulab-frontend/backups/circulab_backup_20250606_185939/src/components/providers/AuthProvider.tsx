"use client";

import React, { useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const { initialize, isLoading } = useAuthStore();

  useEffect(() => {
    // Only execute on client-side after hydration
    if (typeof window !== 'undefined') {
      initialize();
    }
  }, [initialize]);

  return <>{children}</>;
}

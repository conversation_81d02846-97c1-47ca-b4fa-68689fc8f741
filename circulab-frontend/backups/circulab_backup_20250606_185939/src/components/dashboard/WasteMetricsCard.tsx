'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface ChartDataPoint {
  month: string;
  value: number;
}

interface WasteMetricsCardProps {
  title: string;
  value: string;
  unit: string;
  trend?: number;
  chartData: ChartDataPoint[];
}

export function WasteMetricsCard({ title, value, unit, trend, chartData }: WasteMetricsCardProps) {
  // Create SVG path for the area chart
  const createAreaPath = (data: ChartDataPoint[]): { areaPath: string; linePath: string } => {
    if (data.length === 0) return { areaPath: '', linePath: '' };

    const maxValue = Math.max(...data.map(d => d.value));
    const minValue = Math.min(...data.map(d => d.value));
    const range = maxValue - minValue;

    const width = 300;
    const height = 100;
    const padding = 10;

    const points = data.map((point, index) => {
      const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
      const y = height - padding - ((point.value - minValue) / range) * (height - 2 * padding);
      return `${x},${y}`;
    });

    // Create area path (filled)
    const areaPath = `M ${padding},${height - padding} L ${points.join(' L ')} L ${width - padding},${height - padding} Z`;

    // Create line path (stroke)
    const linePath = `M ${points.join(' L ')}`;

    return { areaPath, linePath };
  };

  const { areaPath, linePath } = createAreaPath(chartData);

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-baseline space-x-2">
          <span className="text-3xl font-bold text-gray-900">{value}</span>
          <span className="text-sm text-gray-500">{unit}</span>
          {trend && (
            <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </span>
          )}
        </div>
        
        {/* Mini Chart */}
        <div className="relative">
          <svg width="100%" height="100" viewBox="0 0 300 100" className="overflow-visible">
            {/* Gradient definition */}
            <defs>
              <linearGradient id={`gradient-${title}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#E91E63" stopOpacity="0.3" />
                <stop offset="100%" stopColor="#E91E63" stopOpacity="0.05" />
              </linearGradient>
            </defs>
            
            {/* Area fill */}
            <path
              d={areaPath}
              fill={`url(#gradient-${title})`}
              className="transition-all duration-300"
            />
            
            {/* Line stroke */}
            <path
              d={linePath}
              fill="none"
              stroke="#E91E63"
              strokeWidth="2"
              className="transition-all duration-300"
            />
            
            {/* Data points */}
            {chartData.map((point, index) => {
              const maxValue = Math.max(...chartData.map(d => d.value));
              const minValue = Math.min(...chartData.map(d => d.value));
              const range = maxValue - minValue;
              
              const x = 10 + (index / (chartData.length - 1)) * 280;
              const y = 90 - ((point.value - minValue) / range) * 70;
              
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="3"
                  fill="#E91E63"
                  className="hover:r-4 transition-all duration-200 cursor-pointer"
                />
              );
            })}
          </svg>
        </div>
      </CardContent>
    </Card>
  );
}

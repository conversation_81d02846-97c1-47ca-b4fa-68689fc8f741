import { User, WasteMaterial, WasteStatus, WasteMaterialStatistics, LoginCredentials, RegisterData } from '@/types';

// Mock data
const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  username: 'root',
  isEmailVerified: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  lastLoginAt: new Date().toISOString(),
  companyId: '1',
  company: {
    id: '1',
    name: 'CircuLab Demo Company',
    siret: '12345678901234',
    address: '123 Demo Street, Demo City',
    industryType: 'Manufacturing',
    contactPerson: '<PERSON>',
    contactEmail: '<EMAIL>',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  roles: [
    {
      role: {
        id: '1',
        name: 'admin',
        description: 'Administrator role',
      },
    },
  ],
};

const mockMaterials: WasteMaterial[] = [
  {
    id: '1',
    type: 'Plastic Waste',
    description: 'Mixed plastic containers from manufacturing process',
    quantity: 500,
    unit: 'kg',
    source: 'Production Line A',
    location_address: '123 Factory Street, Industrial Zone',
    currentStatus: WasteStatus.AVAILABLE,
    producerCompanyId: '1',
    producerCompany: {
      id: '1',
      name: 'CircuLab Demo Company',
      address: '123 Demo Street, Demo City',
    },
    isHazardous: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    type: 'Metal Scraps',
    description: 'Aluminum and steel scraps from machining',
    quantity: 200,
    unit: 'kg',
    source: 'Machining Department',
    location_address: '456 Industrial Ave, Manufacturing District',
    currentStatus: WasteStatus.PROCESSING,
    producerCompanyId: '1',
    producerCompany: {
      id: '1',
      name: 'CircuLab Demo Company',
      address: '123 Demo Street, Demo City',
    },
    isHazardous: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const mockStatistics: WasteMaterialStatistics = {
  overview: {
    totalMaterials: 15,
    availableMaterials: 8,
    processingMaterials: 4,
    valorizedMaterials: 3,
    totalQuantity: 2500,
  },
  materialsByType: [
    {
      type: 'Plastic Waste',
      _count: { type: 5 },
      _sum: { quantity: 1200 },
    },
    {
      type: 'Metal Scraps',
      _count: { type: 4 },
      _sum: { quantity: 800 },
    },
    {
      type: 'Organic Waste',
      _count: { type: 6 },
      _sum: { quantity: 500 },
    },
  ],
  materialsByStatus: [
    {
      currentStatus: WasteStatus.AVAILABLE,
      _count: { currentStatus: 8 },
      _sum: { quantity: 1500 },
    },
    {
      currentStatus: WasteStatus.PROCESSING,
      _count: { currentStatus: 4 },
      _sum: { quantity: 700 },
    },
    {
      currentStatus: WasteStatus.VALORIZED,
      _count: { currentStatus: 3 },
      _sum: { quantity: 300 },
    },
  ],
};

// Mock API functions
export const mockApi = {
  // Auth endpoints
  login: async (credentials: LoginCredentials) => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

    if (credentials.email === '<EMAIL>' && credentials.password === 'Password123') {
      return {
        success: true,
        data: {
          user: mockUser,
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
          },
        },
        message: 'Login successful',
        timestamp: new Date().toISOString(),
      };
    } else {
      throw new Error('Invalid credentials');
    }
  },

  register: async (data: RegisterData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      data: {
        user: { ...mockUser, email: data.email, username: data.username },
        tokens: {
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token',
        },
      },
      message: 'Registration successful',
      timestamp: new Date().toISOString(),
    };
  },

  getProfile: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      data: mockUser,
      message: 'Profile fetched successfully',
      timestamp: new Date().toISOString(),
    };
  },

  refreshToken: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      data: {
        tokens: {
          accessToken: 'new-mock-access-token',
          refreshToken: 'new-mock-refresh-token',
        },
      },
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString(),
    };
  },

  // Waste materials endpoints
  getMaterials: async () => {
    await new Promise(resolve => setTimeout(resolve, 800));

    return {
      success: true,
      data: mockMaterials,
      message: 'Materials fetched successfully',
      timestamp: new Date().toISOString(),
    };
  },

  getStatistics: async () => {
    await new Promise(resolve => setTimeout(resolve, 600));

    return {
      success: true,
      data: mockStatistics,
      message: 'Statistics fetched successfully',
      timestamp: new Date().toISOString(),
    };
  },
};

// Check if we should use mock API (when backend is not available)
export const shouldUseMockApi = () => {
  const useRealApi = process.env.NEXT_PUBLIC_USE_REAL_API;

  console.log('🔍 Mock API check:', {
    NEXT_PUBLIC_USE_REAL_API: useRealApi,
    NODE_ENV: process.env.NODE_ENV,
    shouldUseMock: useRealApi !== 'true'
  });

  // Force use of real API when explicitly set to 'true'
  if (useRealApi === 'true') {
    console.log('✅ Using REAL API (explicitly set)');
    return false;
  }

  // Use mock API when explicitly set to 'false' or not set
  console.log('🔄 Using MOCK API (default or explicitly set)');
  return true;
};

import * as XLSX from 'xlsx';

export interface ExportColumn {
  key: string;
  label: string;
  formatter?: (value: any) => string;
}

export interface ExportOptions {
  filename?: string;
  sheetName?: string;
  includeTimestamp?: boolean;
}

/**
 * Export data to CSV format
 */
export function exportToCSV<T extends Record<string, any>>(
  data: T[],
  columns: ExportColumn[],
  options: ExportOptions = {}
): void {
  const {
    filename = 'export',
    includeTimestamp = true
  } = options;

  // Create CSV headers
  const headers = columns.map(col => col.label);
  
  // Create CSV rows
  const rows = data.map(item => 
    columns.map(col => {
      const value = getNestedValue(item, col.key);
      return col.formatter ? col.formatter(value) : formatCellValue(value);
    })
  );

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    .join('\n');

  // Create and download file
  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.csv`;
  
  downloadFile(csvContent, finalFilename, 'text/csv;charset=utf-8;');
}

/**
 * Export data to Excel format
 */
export function exportToExcel<T extends Record<string, any>>(
  data: T[],
  columns: ExportColumn[],
  options: ExportOptions = {}
): void {
  const {
    filename = 'export',
    sheetName = 'Sheet1',
    includeTimestamp = true
  } = options;

  // Create workbook
  const workbook = XLSX.utils.book_new();

  // Prepare data for Excel
  const excelData = data.map(item => {
    const row: Record<string, any> = {};
    columns.forEach(col => {
      const value = getNestedValue(item, col.key);
      row[col.label] = col.formatter ? col.formatter(value) : value;
    });
    return row;
  });

  // Create worksheet
  const worksheet = XLSX.utils.json_to_sheet(excelData);

  // Auto-size columns
  const columnWidths = columns.map(col => ({
    wch: Math.max(col.label.length, 15) // Minimum width of 15 characters
  }));
  worksheet['!cols'] = columnWidths;

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // Generate and download file
  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.xlsx`;
  
  XLSX.writeFile(workbook, finalFilename);
}

/**
 * Export multiple sheets to Excel
 */
export function exportMultiSheetExcel(
  sheets: Array<{
    data: any[];
    columns: ExportColumn[];
    sheetName: string;
  }>,
  options: ExportOptions = {}
): void {
  const {
    filename = 'export',
    includeTimestamp = true
  } = options;

  const workbook = XLSX.utils.book_new();

  sheets.forEach(({ data, columns, sheetName }) => {
    const excelData = data.map(item => {
      const row: Record<string, any> = {};
      columns.forEach(col => {
        const value = getNestedValue(item, col.key);
        row[col.label] = col.formatter ? col.formatter(value) : value;
      });
      return row;
    });

    const worksheet = XLSX.utils.json_to_sheet(excelData);
    
    // Auto-size columns
    const columnWidths = columns.map(col => ({
      wch: Math.max(col.label.length, 15)
    }));
    worksheet['!cols'] = columnWidths;

    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  });

  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.xlsx`;
  
  XLSX.writeFile(workbook, finalFilename);
}

/**
 * Get nested object value using dot notation
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Format cell value for CSV export
 */
function formatCellValue(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (value instanceof Date) {
    return value.toISOString().split('T')[0]; // YYYY-MM-DD format
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
}

/**
 * Download file to user's device
 */
function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
}

/**
 * Common formatters for different data types
 */
export const formatters = {
  date: (value: any) => {
    if (!value) return '';
    const date = new Date(value);
    return date.toLocaleDateString();
  },
  
  datetime: (value: any) => {
    if (!value) return '';
    const date = new Date(value);
    return date.toLocaleString();
  },
  
  currency: (value: any, currency = 'EUR') => {
    if (value === null || value === undefined) return '';
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency
    }).format(Number(value));
  },
  
  number: (value: any, decimals = 2) => {
    if (value === null || value === undefined) return '';
    return Number(value).toFixed(decimals);
  },
  
  boolean: (value: any) => {
    return value ? 'Yes' : 'No';
  },
  
  array: (value: any[], separator = ', ') => {
    if (!Array.isArray(value)) return '';
    return value.join(separator);
  }
};

/**
 * Pre-defined column configurations for common entities
 */
export const columnConfigs = {
  companies: [
    { key: 'name', label: 'Company Name' },
    { key: 'siret', label: 'SIRET' },
    { key: 'industryType', label: 'Industry Type' },
    { key: 'address', label: 'Address' },
    { key: 'contactPerson', label: 'Contact Person' },
    { key: 'contactEmail', label: 'Contact Email' },
    { key: 'createdAt', label: 'Created Date', formatter: formatters.date }
  ] as ExportColumn[],
  
  wasteMaterials: [
    { key: 'type', label: 'Material Type' },
    { key: 'quantity', label: 'Quantity' },
    { key: 'unit', label: 'Unit' },
    { key: 'status', label: 'Status' },
    { key: 'company.name', label: 'Company' },
    { key: 'location', label: 'Location' },
    { key: 'createdAt', label: 'Created Date', formatter: formatters.date }
  ] as ExportColumn[],
  
  processingHistory: [
    { key: 'wasteMaterial.type', label: 'Waste Type' },
    { key: 'treatmentMethod.name', label: 'Treatment Method' },
    { key: 'inputQuantity', label: 'Input Quantity', formatter: formatters.number },
    { key: 'outputQuantity', label: 'Output Quantity', formatter: formatters.number },
    { key: 'processorCompany.name', label: 'Processor' },
    { key: 'dateProcessed', label: 'Date Processed', formatter: formatters.date },
    { key: 'createdAt', label: 'Created Date', formatter: formatters.date }
  ] as ExportColumn[],
  
  users: [
    { key: 'username', label: 'Username' },
    { key: 'email', label: 'Email' },
    { key: 'role', label: 'Role' },
    { key: 'company.name', label: 'Company' },
    { key: 'isActive', label: 'Active', formatter: formatters.boolean },
    { key: 'createdAt', label: 'Created Date', formatter: formatters.date }
  ] as ExportColumn[]
};

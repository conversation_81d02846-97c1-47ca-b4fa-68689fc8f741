import { apiClient, ApiResponse } from '../apiClient';
import { User, PaginationParams } from '../../types';

// Re-export types for convenience
export type { User } from '../../types';

export interface UserQuery extends PaginationParams {
  search?: string;
  companyId?: string;
  role?: string;
  isEmailVerified?: boolean;
}

export interface CreateUserData {
  email: string;
  password: string;
  username?: string;
  companyId?: string;
  roleNames?: string[];
  isEmailVerified?: boolean;
}

export interface UpdateUserData {
  email?: string;
  username?: string;
  companyId?: string;
  roleNames?: string[];
  isEmailVerified?: boolean;
}

export interface UpdateUserPasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface UserStatistics {
  totalUsers: number;
  usersByRole: Array<{ role: string; count: number }>;
  usersByCompany: Array<{ company: string; count: number }>;
  recentlyJoined: Array<{ email: string; username: string | null; createdAt: string }>;
  emailVerificationStats: { verified: number; unverified: number };
}

export interface UsersResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const usersApi = {
  // Get all users with filtering and pagination
  getAll: async (query?: UserQuery): Promise<ApiResponse<UsersResponse>> => {
    const params = new URLSearchParams();
    if (query?.search) params.append('search', query.search);
    if (query?.companyId) params.append('companyId', query.companyId);
    if (query?.role) params.append('role', query.role);
    if (query?.isEmailVerified !== undefined) params.append('isEmailVerified', query.isEmailVerified.toString());
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortOrder) params.append('sortOrder', query.sortOrder);

    const queryString = params.toString();
    return apiClient.get(`/users${queryString ? `?${queryString}` : ''}`);
  },

  // Get user by ID
  getById: async (id: string): Promise<ApiResponse<User>> => {
    return apiClient.get(`/users/${id}`);
  },

  // Create new user
  create: async (data: CreateUserData): Promise<ApiResponse<User>> => {
    return apiClient.post('/users', data);
  },

  // Update user
  update: async (id: string, data: UpdateUserData): Promise<ApiResponse<User>> => {
    return apiClient.put(`/users/${id}`, data);
  },

  // Update user password
  updatePassword: async (id: string, data: UpdateUserPasswordData): Promise<ApiResponse<void>> => {
    return apiClient.put(`/users/${id}/password`, data);
  },

  // Delete user
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/users/${id}`);
  },

  // Get user statistics
  getStatistics: async (): Promise<ApiResponse<UserStatistics>> => {
    return apiClient.get('/users/statistics');
  },
};

"use client";

// Types pour le système de santé
export interface HealthStatus {
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details?: any;
  timestamp: Date;
}

export interface SystemHealth {
  webpack: HealthStatus;
  extensions: HealthStatus;
  dependencies: HealthStatus;
  api: HealthStatus;
  localStorage: HealthStatus;
  overall: HealthStatus;
  confidence: number;
}

// Détecteur d'anomalies webpack
class WebpackHealthMonitor {
  private static instance: WebpackHealthMonitor;
  private moduleErrors: string[] = [];
  private hotReloadIssues: number = 0;
  private lastCheck: Date = new Date();

  static getInstance(): WebpackHealthMonitor {
    if (!WebpackHealthMonitor.instance) {
      WebpackHealthMonitor.instance = new WebpackHealthMonitor();
    }
    return WebpackHealthMonitor.instance;
  }

  validateModuleResolution(): HealthStatus {
    try {
      // Vérification des modules critiques
      const criticalModules = [
        'react',
        'next',
        '@/components/providers/AuthProvider',
        '@/store/authStore',
        '@/lib/apiClient'
      ];

      const failedModules: string[] = [];

      // Test de résolution des modules (simulation)
      criticalModules.forEach(module => {
        try {
          // En production, on ne peut pas vraiment tester require()
          // mais on peut vérifier d'autres indicateurs
          if (typeof window !== 'undefined') {
            // Vérifications côté client
            if (module.includes('@/') && !this.checkModuleAvailability(module)) {
              failedModules.push(module);
            }
          }
        } catch (error) {
          failedModules.push(module);
          this.moduleErrors.push(`${module}: ${(error as Error).message}`);
        }
      });

      if (failedModules.length === 0) {
        return {
          status: 'healthy',
          message: 'All critical modules resolved successfully',
          timestamp: new Date()
        };
      } else {
        return {
          status: 'error',
          message: `Failed to resolve ${failedModules.length} modules`,
          details: { failedModules, errors: this.moduleErrors },
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        status: 'error',
        message: 'Module resolution check failed',
        details: { error: (error as Error).message },
        timestamp: new Date()
      };
    }
  }

  private checkModuleAvailability(modulePath: string): boolean {
    // Vérification basique de disponibilité des modules
    // En réalité, on vérifierait si les composants sont montés correctement
    return true; // Placeholder
  }

  monitorHotReload(): HealthStatus {
    if (typeof window === 'undefined') {
      return {
        status: 'healthy',
        message: 'Server-side rendering - hot reload not applicable',
        timestamp: new Date()
      };
    }

    // Vérification des rechargements suspects
    const reloadCount = this.hotReloadIssues;
    
    if (reloadCount === 0) {
      return {
        status: 'healthy',
        message: 'No hot reload issues detected',
        timestamp: new Date()
      };
    } else if (reloadCount < 5) {
      return {
        status: 'warning',
        message: `${reloadCount} hot reload issues detected`,
        details: { reloadCount },
        timestamp: new Date()
      };
    } else {
      return {
        status: 'error',
        message: `Excessive hot reload issues: ${reloadCount}`,
        details: { reloadCount },
        timestamp: new Date()
      };
    }
  }

  reportHotReloadIssue() {
    this.hotReloadIssues++;
  }

  reset() {
    this.moduleErrors = [];
    this.hotReloadIssues = 0;
    this.lastCheck = new Date();
  }
}

// Détecteur de compatibilité des extensions
class ExtensionCompatibilityChecker {
  private static instance: ExtensionCompatibilityChecker;

  static getInstance(): ExtensionCompatibilityChecker {
    if (!ExtensionCompatibilityChecker.instance) {
      ExtensionCompatibilityChecker.instance = new ExtensionCompatibilityChecker();
    }
    return ExtensionCompatibilityChecker.instance;
  }

  checkExtensionCompatibility(): HealthStatus {
    if (typeof window === 'undefined') {
      return {
        status: 'healthy',
        message: 'Server-side - extension check not applicable',
        timestamp: new Date()
      };
    }

    const detectedExtensions: string[] = [];
    const problematicExtensions: string[] = [];

    // Détection d'extensions connues pour causer des problèmes
    try {
      // Vérification de l'extension IA AUGMENTE
      if (this.detectAugmentExtension()) {
        detectedExtensions.push('IA AUGMENTE');
        // Vérifier si elle cause des problèmes
        if (this.checkAugmentConflicts()) {
          problematicExtensions.push('IA AUGMENTE - Module resolution conflicts');
        }
      }

      // Autres extensions problématiques connues
      const knownProblematicExtensions = [
        'Auto Rename Tag',
        'Bracket Pair Colorizer',
        'GitLens (certain versions)'
      ];

      // Simulation de détection d'extensions
      // En réalité, on ne peut pas détecter les extensions VS Code depuis le navigateur
      // Mais on peut détecter leurs effets sur le DOM ou les erreurs qu'elles causent

      if (problematicExtensions.length === 0) {
        return {
          status: detectedExtensions.length > 0 ? 'warning' : 'healthy',
          message: detectedExtensions.length > 0 
            ? `Extensions detected but no conflicts: ${detectedExtensions.join(', ')}`
            : 'No problematic extensions detected',
          details: { detectedExtensions },
          timestamp: new Date()
        };
      } else {
        return {
          status: 'error',
          message: `Problematic extensions detected: ${problematicExtensions.length}`,
          details: { detectedExtensions, problematicExtensions },
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        status: 'warning',
        message: 'Extension compatibility check failed',
        details: { error: (error as Error).message },
        timestamp: new Date()
      };
    }
  }

  private detectAugmentExtension(): boolean {
    // Détection basée sur des modifications DOM ou des scripts injectés
    // Placeholder - en réalité on chercherait des signes spécifiques
    return typeof window !== 'undefined' && 
           (document.querySelector('[data-augment]') !== null ||
            window.location.search.includes('augment'));
  }

  private checkAugmentConflicts(): boolean {
    // Vérification des conflits spécifiques à l'extension Augment
    // Placeholder pour la logique de détection de conflits
    return false;
  }
}

// Vérificateur de dépendances
class DependencyChecker {
  static checkDependencies(): HealthStatus {
    try {
      const criticalDependencies = [
        { name: 'React', check: () => typeof window !== 'undefined' && (window as any).React !== undefined },
        { name: 'Next.js', check: () => typeof window === 'undefined' || (window as any).next !== undefined },
        { name: 'Zustand', check: () => true }, // Placeholder
        { name: 'Axios', check: () => true }, // Placeholder
      ];

      const failedDeps = criticalDependencies.filter(dep => {
        try {
          return !dep.check();
        } catch {
          return true;
        }
      });

      if (failedDeps.length === 0) {
        return {
          status: 'healthy',
          message: 'All critical dependencies available',
          timestamp: new Date()
        };
      } else {
        return {
          status: 'error',
          message: `Missing dependencies: ${failedDeps.map(d => d.name).join(', ')}`,
          details: { failedDeps: failedDeps.map(d => d.name) },
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        status: 'error',
        message: 'Dependency check failed',
        details: { error: (error as Error).message },
        timestamp: new Date()
      };
    }
  }
}

// Vérificateur API
class APIHealthChecker {
  static async checkAPIHealth(): Promise<HealthStatus> {
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        return {
          status: 'healthy',
          message: 'API is responding correctly',
          details: data,
          timestamp: new Date()
        };
      } else {
        return {
          status: 'error',
          message: `API returned ${response.status}: ${response.statusText}`,
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        status: 'error',
        message: 'API health check failed',
        details: { error: (error as Error).message },
        timestamp: new Date()
      };
    }
  }
}

// Vérificateur de localStorage
class StorageChecker {
  static checkLocalStorage(): HealthStatus {
    if (typeof window === 'undefined') {
      return {
        status: 'healthy',
        message: 'Server-side - localStorage not applicable',
        timestamp: new Date()
      };
    }

    try {
      const testKey = '__health_check_test__';
      const testValue = 'test';
      
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);

      if (retrieved === testValue) {
        return {
          status: 'healthy',
          message: 'localStorage is working correctly',
          timestamp: new Date()
        };
      } else {
        return {
          status: 'error',
          message: 'localStorage read/write test failed',
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        status: 'error',
        message: 'localStorage is not available',
        details: { error: (error as Error).message },
        timestamp: new Date()
      };
    }
  }
}

// Classe principale de monitoring de santé
export class HealthMonitor {
  private static instance: HealthMonitor;
  private webpackMonitor: WebpackHealthMonitor;
  private extensionChecker: ExtensionCompatibilityChecker;

  constructor() {
    this.webpackMonitor = WebpackHealthMonitor.getInstance();
    this.extensionChecker = ExtensionCompatibilityChecker.getInstance();
  }

  static getInstance(): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor();
    }
    return HealthMonitor.instance;
  }

  async performFullHealthCheck(): Promise<SystemHealth> {
    const webpack = this.webpackMonitor.validateModuleResolution();
    const extensions = this.extensionChecker.checkExtensionCompatibility();
    const dependencies = DependencyChecker.checkDependencies();
    const api = await APIHealthChecker.checkAPIHealth();
    const localStorage = StorageChecker.checkLocalStorage();

    // Calcul du score de confiance
    const healthScores = [webpack, extensions, dependencies, api, localStorage];
    const healthyCount = healthScores.filter(h => h.status === 'healthy').length;
    const warningCount = healthScores.filter(h => h.status === 'warning').length;
    const errorCount = healthScores.filter(h => h.status === 'error').length;

    const confidence = Math.round(
      (healthyCount * 100 + warningCount * 50 + errorCount * 0) / healthScores.length
    );

    // Statut global
    let overallStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    let overallMessage = 'All systems operational';

    if (errorCount > 0) {
      overallStatus = 'error';
      overallMessage = `${errorCount} critical issues detected`;
    } else if (warningCount > 0) {
      overallStatus = 'warning';
      overallMessage = `${warningCount} warnings detected`;
    }

    const overall: HealthStatus = {
      status: overallStatus,
      message: overallMessage,
      details: { healthyCount, warningCount, errorCount },
      timestamp: new Date()
    };

    return {
      webpack,
      extensions,
      dependencies,
      api,
      localStorage,
      overall,
      confidence
    };
  }

  reportHotReloadIssue() {
    this.webpackMonitor.reportHotReloadIssue();
  }

  reset() {
    this.webpackMonitor.reset();
  }
}

// User and Authentication Types
export interface User {
  id: string;
  email: string;
  username?: string;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  companyId?: string;
  company?: Company;
  roles: UserRole[];
}

export interface Company {
  id: string;
  name: string;
  siret?: string;
  address?: string;
  industryType?: string;
  contactPerson?: string;
  contactEmail?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
}

export interface UserRole {
  role: Role;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  username?: string;
  companyId?: string;
}

// Waste Material Types
export enum WasteStatus {
  AVAILABLE = 'AVAILABLE',
  PENDING_PICKUP = 'PENDING_PICKUP',
  IN_TRANSIT = 'IN_TRANSIT',
  PROCESSING = 'PROCESSING',
  VALORIZED = 'VALORIZED',
  DISPOSED = 'DISPOSED',
  AWAITING_ANALYSIS = 'AWAITING_ANALYSIS',
}

export interface WasteMaterial {
  id: string;
  type: string;
  description?: string;
  quantity: number;
  unit: string;
  source: string;
  location_address?: string;
  location_latitude?: number;
  location_longitude?: number;
  currentStatus: WasteStatus;
  producerUserId?: string;
  producerUser?: {
    id: string;
    email: string;
    username?: string;
  };
  producerCompanyId: string;
  producerCompany: {
    id: string;
    name: string;
    address?: string;
  };
  imageUrls?: Array<{ url: string; description?: string }>;
  analysisReportUrl?: string;
  suggestedTreatmentMethodId?: string;
  suggestedTreatmentMethod?: {
    id: string;
    name: string;
    description?: string;
  };
  valorizationPotentialScore?: number;
  estimatedEnvironmentalImpact?: Record<string, any>;
  desiredProcessingDate?: string;
  isHazardous: boolean;
  createdAt: string;
  updatedAt: string;
  processingHistory?: ProcessingHistory[];
  _count?: {
    processingHistory: number;
  };
}

export interface CreateWasteMaterialData {
  type: string;
  description?: string;
  quantity: number;
  unit: string;
  source: string;
  location_address?: string;
  location_latitude?: number;
  location_longitude?: number;
  currentStatus?: WasteStatus;
  producerCompanyId?: string;
  suggestedTreatmentMethodId?: string;
  valorizationPotentialScore?: number;
  desiredProcessingDate?: string;
  isHazardous?: boolean;
  imageUrls?: Array<{ url: string; description?: string }>;
  analysisReportUrl?: string;
}

// Treatment Method Types
export interface TreatmentMethod {
  id: string;
  name: string;
  description?: string;
  technologyType?: string;
  typicalInputs?: string[];
  typicalOutputs?: string[];
  environmentalBenefits?: string;
  costEstimate?: string;
  iconUrl?: string;
  // Additional properties for UI display
  efficiency?: number; // Efficiency percentage (0-100)
  estimatedCost?: number; // Estimated cost in euros
  estimatedDuration?: number; // Estimated duration in days
  environmentalImpact?: 'LOW' | 'MEDIUM' | 'HIGH'; // Environmental impact level
  processSteps?: string[]; // Array of process steps
  _count?: {
    wasteMaterials: number;
    processingHistories: number;
  };
}

export interface CreateTreatmentMethodData {
  name: string;
  description?: string;
  technologyType?: string;
  typicalInputs?: string[];
  typicalOutputs?: string[];
  environmentalBenefits?: string;
  costEstimate?: string;
  iconUrl?: string;
}

// Processing History Types
export interface ProcessingHistory {
  id: string;
  wasteMaterialId: string;
  wasteMaterial: WasteMaterial;
  processedByUserId?: string;
  processedByUser?: {
    id: string;
    email: string;
    username?: string;
  };
  processorCompanyId: string;
  processorCompany: {
    id: string;
    name: string;
  };
  treatmentMethodId: string;
  treatmentMethod: {
    id: string;
    name: string;
    description?: string;
  };
  dateProcessed: string;
  inputQuantity?: number;
  outputQuantity?: number;
  outputMaterialType?: string;
  environmentalImpactAchieved?: Record<string, any>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Notification Types
export enum NotificationType {
  GENERIC_INFO = 'GENERIC_INFO',
  NEW_WASTE_AVAILABLE = 'NEW_WASTE_AVAILABLE',
  WASTE_STATUS_UPDATED = 'WASTE_STATUS_UPDATED',
  PROCESSING_COMPLETE = 'PROCESSING_COMPLETE',
  CONTRACT_PROPOSAL = 'CONTRACT_PROPOSAL',
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  SYSTEM_ALERT = 'SYSTEM_ALERT',
}

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  message: string;
  isRead: boolean;
  linkTo?: string;
  createdAt: string;
}

// API Query Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface WasteMaterialQuery extends PaginationParams {
  search?: string;
  type?: string;
  status?: WasteStatus;
  companyId?: string;
  isHazardous?: boolean;
}

export interface TreatmentMethodQuery extends PaginationParams {
  search?: string;
  technologyType?: string;
}

// Statistics Types
export interface WasteMaterialStatistics {
  overview: {
    totalMaterials: number;
    availableMaterials: number;
    processingMaterials: number;
    valorizedMaterials: number;
    totalQuantity: number;
  };
  materialsByType: Array<{
    type: string;
    _count: { type: number };
    _sum: { quantity: number };
  }>;
  materialsByStatus: Array<{
    currentStatus: WasteStatus;
    _count: { currentStatus: number };
    _sum: { quantity: number };
  }>;
}

export interface TreatmentMethodStatistics {
  overview: {
    totalMethods: number;
  };
  methodsByTechnology: Array<{
    technologyType: string;
    _count: { technologyType: number };
  }>;
  mostUsedMethods: TreatmentMethod[];
}

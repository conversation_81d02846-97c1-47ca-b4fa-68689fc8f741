import * as React from 'react';
import { useState, useCallback } from 'react';

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

interface ToastState {
  toasts: Toast[];
}

const initialState: ToastState = {
  toasts: [],
};

let toastState = initialState;
let listeners: Array<(state: ToastState) => void> = [];

function dispatch(action: { type: string; payload?: any }) {
  switch (action.type) {
    case 'ADD_TOAST':
      toastState = {
        ...toastState,
        toasts: [...toastState.toasts, action.payload],
      };
      break;
    case 'REMOVE_TOAST':
      toastState = {
        ...toastState,
        toasts: toastState.toasts.filter((toast) => toast.id !== action.payload),
      };
      break;
    case 'CLEAR_TOASTS':
      toastState = {
        ...toastState,
        toasts: [],
      };
      break;
  }

  listeners.forEach((listener) => listener(toastState));
}

function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

export function toast({
  title,
  description,
  variant = 'default',
  duration = 5000,
}: Omit<Toast, 'id'>) {
  const id = generateId();
  
  dispatch({
    type: 'ADD_TOAST',
    payload: {
      id,
      title,
      description,
      variant,
      duration,
    },
  });

  // Auto-remove toast after duration
  if (duration > 0) {
    setTimeout(() => {
      dispatch({
        type: 'REMOVE_TOAST',
        payload: id,
      });
    }, duration);
  }

  return id;
}

export function useToast() {
  const [state, setState] = useState(toastState);

  const subscribe = useCallback((listener: (state: ToastState) => void) => {
    listeners.push(listener);
    return () => {
      listeners = listeners.filter((l) => l !== listener);
    };
  }, []);

  const unsubscribe = useCallback(() => {
    listeners = [];
  }, []);

  // Subscribe to state changes
  React.useEffect(() => {
    const unsubscribe = subscribe(setState);
    return unsubscribe;
  }, [subscribe]);

  return {
    toast,
    toasts: state.toasts,
    dismiss: (id: string) => {
      dispatch({
        type: 'REMOVE_TOAST',
        payload: id,
      });
    },
    clear: () => {
      dispatch({
        type: 'CLEAR_TOASTS',
      });
    },
  };
}



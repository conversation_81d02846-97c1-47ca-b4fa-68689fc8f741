import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = 'http://localhost:4000';

export async function GET(request: NextRequest) {
  return proxyRequest(request, 'GET');
}

export async function POST(request: NextRequest) {
  return proxyRequest(request, 'POST');
}

async function proxyRequest(request: NextRequest, method: string) {
  try {
    console.log(`🔄 Proxying ${method} waste-materials request to backend...`);
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams.toString();
    const url = `${BACKEND_URL}/api/waste-materials${searchParams ? `?${searchParams}` : ''}`;
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Copy authorization header if present
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add body for POST requests
    if (method === 'POST') {
      const body = await request.text();
      if (body) {
        requestOptions.body = body;
      }
    }

    const response = await fetch(url, requestOptions);
    const data = await response.text();
    
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error(`Waste materials ${method} proxy error:`, error);
    return NextResponse.json(
      { 
        success: false, 
        error: { message: 'Waste materials service unavailable' },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

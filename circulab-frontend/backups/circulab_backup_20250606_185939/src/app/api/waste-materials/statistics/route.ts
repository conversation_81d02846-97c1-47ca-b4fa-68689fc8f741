import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = 'http://localhost:4000';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Proxying statistics request to backend...');
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams.toString();
    const url = `${BACKEND_URL}/api/waste-materials/statistics${searchParams ? `?${searchParams}` : ''}`;
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Copy authorization header if present
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });
    
    const data = await response.text();
    
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Statistics proxy error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: { message: 'Statistics service unavailable' },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

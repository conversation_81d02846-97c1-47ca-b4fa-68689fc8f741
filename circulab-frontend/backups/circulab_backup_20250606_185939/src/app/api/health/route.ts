import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = 'http://localhost:4000';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Proxying health check to backend...');
    
    const response = await fetch(`${BACKEND_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const data = await response.text();
    
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Health check proxy error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: { message: 'Backend connection failed' },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

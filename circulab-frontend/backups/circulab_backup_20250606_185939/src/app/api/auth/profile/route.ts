import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = 'http://localhost:4000';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Proxying profile request to backend...');
    
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    const response = await fetch(`${BACKEND_URL}/api/auth/profile`, {
      method: 'GET',
      headers,
    });
    
    const data = await response.text();
    
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Profile proxy error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: { message: 'Profile service unavailable' },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

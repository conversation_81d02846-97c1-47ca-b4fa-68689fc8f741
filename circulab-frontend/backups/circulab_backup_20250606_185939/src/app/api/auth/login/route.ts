import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = 'http://localhost:4000';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Proxying login request to backend...');
    
    const body = await request.text();
    
    const response = await fetch(`${BACKEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body,
    });
    
    const data = await response.text();
    
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Login proxy error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: { message: 'Authentication service unavailable' },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

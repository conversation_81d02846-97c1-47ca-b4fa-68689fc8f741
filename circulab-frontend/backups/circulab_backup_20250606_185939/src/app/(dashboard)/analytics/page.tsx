'use client';

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function AnalyticsPage() {
  return (
    <div className="animate-fade-in">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics & Rapports</CardTitle>
                  <CardDescription>
                    Analysez les données de vos déchets et générez des rapports détaillés
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="mx-auto w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Module Analytics
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Visualisez les tendances, générez des rapports et analysez l'efficacité de votre gestion des déchets.
                    </p>
                    <Button>
                      Voir les Rapports
                    </Button>
                  </div>
                </CardContent>
              </Card>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, BellRing, Check, Trash2, Eye, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { notificationsApi, type UserNotificationsResponse, type Notification, type NotificationType } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);
  const { toast } = useToast();

  const fetchNotifications = async (page = 1, type = '', isRead?: boolean) => {
    try {
      setLoading(true);
      const response = await notificationsApi.getMy({
        page,
        limit: 10,
        type: type as NotificationType || undefined,
        isRead,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      if (response.success && response.data) {
        setNotifications(response.data.notifications);
        setTotal(response.data.total);
        setTotalPages(response.data.totalPages);
        setCurrentPage(response.data.page);
        setUnreadCount(response.data.unreadCount);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch notifications',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const isRead = statusFilter === 'read' ? true : 
                  statusFilter === 'unread' ? false : undefined;
    fetchNotifications(1, typeFilter, isRead);
  }, [typeFilter, statusFilter]);

  const handlePageChange = (page: number) => {
    const isRead = statusFilter === 'read' ? true : 
                  statusFilter === 'unread' ? false : undefined;
    fetchNotifications(page, typeFilter, isRead);
  };

  const handleMarkAsRead = async (id: string) => {
    try {
      await notificationsApi.markAsRead(id);
      toast({
        title: 'Success',
        description: 'Notification marked as read',
      });
      const isRead = statusFilter === 'read' ? true : 
                    statusFilter === 'unread' ? false : undefined;
      fetchNotifications(currentPage, typeFilter, isRead);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read',
        variant: 'destructive',
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // This would need the current user ID - you'd get this from your auth context
      // For now, we'll skip this implementation
      toast({
        title: 'Info',
        description: 'Mark all as read functionality needs user context',
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this notification?')) return;

    try {
      await notificationsApi.delete(id);
      toast({
        title: 'Success',
        description: 'Notification deleted successfully',
      });
      const isRead = statusFilter === 'read' ? true : 
                    statusFilter === 'unread' ? false : undefined;
      fetchNotifications(currentPage, typeFilter, isRead);
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete notification',
        variant: 'destructive',
      });
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'NEW_WASTE_AVAILABLE':
        return '📦';
      case 'WASTE_STATUS_UPDATED':
        return '🔄';
      case 'PROCESSING_COMPLETE':
        return '✅';
      case 'CONTRACT_PROPOSAL':
        return '📋';
      case 'MESSAGE_RECEIVED':
        return '💬';
      case 'SYSTEM_ALERT':
        return '⚠️';
      default:
        return 'ℹ️';
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'NEW_WASTE_AVAILABLE':
        return 'bg-blue-100 text-blue-800';
      case 'WASTE_STATUS_UPDATED':
        return 'bg-yellow-100 text-yellow-800';
      case 'PROCESSING_COMPLETE':
        return 'bg-green-100 text-green-800';
      case 'CONTRACT_PROPOSAL':
        return 'bg-purple-100 text-purple-800';
      case 'MESSAGE_RECEIVED':
        return 'bg-indigo-100 text-indigo-800';
      case 'SYSTEM_ALERT':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with your CircuLab activities
          </p>
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" onClick={handleMarkAllAsRead}>
              <Check className="mr-2 h-4 w-4" />
              Mark All Read ({unreadCount})
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Notifications</CardTitle>
          <CardDescription>
            Filter by type and read status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All types</SelectItem>
                <SelectItem value="NEW_WASTE_AVAILABLE">New Waste Available</SelectItem>
                <SelectItem value="WASTE_STATUS_UPDATED">Status Updated</SelectItem>
                <SelectItem value="PROCESSING_COMPLETE">Processing Complete</SelectItem>
                <SelectItem value="CONTRACT_PROPOSAL">Contract Proposal</SelectItem>
                <SelectItem value="MESSAGE_RECEIVED">Message Received</SelectItem>
                <SelectItem value="SYSTEM_ALERT">System Alert</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All notifications</SelectItem>
                <SelectItem value="unread">Unread</SelectItem>
                <SelectItem value="read">Read</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <div className="grid gap-4">
        {loading ? (
          <div className="text-center py-8">Loading notifications...</div>
        ) : notifications.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Bell className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No notifications found</h3>
              <p className="text-muted-foreground">
                {typeFilter || statusFilter ? 'No notifications match your filters.' : 'You\'re all caught up!'}
              </p>
            </CardContent>
          </Card>
        ) : (
          notifications.map((notification) => (
            <Card key={notification.id} className={!notification.isRead ? 'border-l-4 border-l-blue-500' : ''}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-lg">{getNotificationIcon(notification.type)}</span>
                      <Badge className={getTypeBadgeColor(notification.type)}>
                        {notification.type.replace(/_/g, ' ')}
                      </Badge>
                      {!notification.isRead && (
                        <Badge variant="default" className="bg-blue-100 text-blue-800">
                          <BellRing className="h-3 w-3 mr-1" />
                          New
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm mb-3">{notification.message}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>{new Date(notification.createdAt).toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {notification.linkTo && (
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    {!notification.isRead && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDelete(notification.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}

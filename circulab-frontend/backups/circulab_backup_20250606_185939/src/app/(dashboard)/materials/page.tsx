'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function MaterialsPage() {
  return (
    <div className="animate-fade-in">
              <Card>
                <CardHeader>
                  <CardTitle>Gestion des Déchets</CardTitle>
                  <CardDescription>
                    Gérez vos matériaux de déchets et suivez leur traitement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="mx-auto w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Module de Gestion des Déchets
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Cette section permet de gérer vos matériaux de déchets, suivre leur statut et organiser leur traitement.
                    </p>
                    <Button>
                      Ajouter un Déchet
                    </Button>
                  </div>
                </CardContent>
              </Card>
    </div>
  );
}

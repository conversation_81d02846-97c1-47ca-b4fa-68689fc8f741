'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MapPin,
  Calendar,
  Target,
  Table,
  BarChart3,
  Download
} from 'lucide-react';
import { useWasteMaterialsStore } from '@/store/wasteMaterialsStore';
import { DashboardFilters } from '@/components/dashboard/DashboardFilters';
import { WasteMetricsCard } from '@/components/dashboard/WasteMetricsCard';
import { ProcessingChart } from '@/components/dashboard/ProcessingChart';
import { EmissionsByScope } from '@/components/dashboard/EmissionsByScope';
import { WasteGeneratedMap } from '@/components/dashboard/WasteGeneratedMap';
import Link from 'next/link';



const DonutChart = ({ title, data, centerValue, centerLabel }: {
  title: string;
  data: Array<{ label: string; value: number; color: string }>;
  centerValue?: string;
  centerLabel?: string;
}) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let cumulativePercentage = 0;

  const createPath = (percentage: number, cumulativePercentage: number) => {
    const startAngle = cumulativePercentage * 3.6; // Convert to degrees
    const endAngle = (cumulativePercentage + percentage) * 3.6;

    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);

    const largeArcFlag = percentage > 50 ? 1 : 0;

    const x1 = 50 + 35 * Math.cos(startAngleRad);
    const y1 = 50 + 35 * Math.sin(startAngleRad);
    const x2 = 50 + 35 * Math.cos(endAngleRad);
    const y2 = 50 + 35 * Math.sin(endAngleRad);

    return `M 50 50 L ${x1} ${y1} A 35 35 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-6">
          <div className="relative">
            <svg width="200" height="200" viewBox="0 0 100 100" className="transform -rotate-90">
              {data.map((item, index) => {
                const percentage = (item.value / total) * 100;
                const path = createPath(percentage, cumulativePercentage);
                cumulativePercentage += percentage;

                return (
                  <path
                    key={index}
                    d={path}
                    fill={item.color}
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                  />
                );
              })}
              {/* Inner circle to create donut effect */}
              <circle cx="50" cy="50" r="20" fill="white" />
            </svg>
            {centerValue && (
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span className="text-2xl font-bold text-gray-900">{centerValue}</span>
                {centerLabel && <span className="text-xs text-gray-500">{centerLabel}</span>}
              </div>
            )}
          </div>
          <div className="space-y-3">
            {data.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-sm text-gray-700">{item.label}</span>
                <span className="text-sm font-medium text-gray-900">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const BarChart = ({ title, data, subtitle }: {
  title: string;
  data: Array<{ label: string; value: number; color: string }>;
  subtitle?: string;
}) => {
  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        {subtitle && <CardDescription>{subtitle}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">{item.label}</span>
                <span className="text-sm font-bold text-gray-900">{item.value}T</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${(item.value / maxValue) * 100}%`,
                    backgroundColor: item.color
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default function DashboardPage() {
  const { statistics, fetchStatistics } = useWasteMaterialsStore();
  const [activeTab, setActiveTab] = useState('overview');
  const [viewMode, setViewMode] = useState('targets');

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  const stats = statistics?.overview;

  // Mock data for charts (inspired by reference design)
  const wasteTypesData = [
    { label: 'Plastiques', value: 35, color: '#22c55e' },
    { label: 'Métaux', value: 25, color: '#3b82f6' },
    { label: 'Organiques', value: 20, color: '#f59e0b' },
    { label: 'Carton', value: 20, color: '#ef4444' },
  ];

  const treatmentData = [
    { label: 'Recyclage', value: 45, color: '#22c55e' },
    { label: 'Valorisation', value: 30, color: '#3b82f6' },
    { label: 'Enfouissement', value: 25, color: '#f59e0b' },
  ];

  const collectedMassesData = [
    { label: 'Plastiques', value: 45, color: '#22c55e' },
    { label: 'Carton', value: 38, color: '#f59e0b' },
    { label: 'Organiques', value: 32, color: '#3b82f6' },
    { label: 'Métaux', value: 28, color: '#ef4444' },
    { label: 'Verre', value: 25, color: '#8b5cf6' },
    { label: 'Autres', value: 20, color: '#6b7280' },
  ];

  const serviceProvidersData = [
    { label: 'ETS Recyclage', value: 40, color: '#22c55e' },
    { label: 'Green Solutions', value: 35, color: '#3b82f6' },
    { label: 'EcoTech', value: 25, color: '#f59e0b' },
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar - Filters */}
      <DashboardFilters />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation */}
        <div className="bg-white border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">Dashboards</h1>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>

            {/* Dashboard Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
              <TabsList className="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
                <TabsTrigger value="overview">KPIs</TabsTrigger>
                <TabsTrigger value="waste">Waste dashboard</TabsTrigger>
                <TabsTrigger value="processing">Processing dashboard</TabsTrigger>
                <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
                <TabsTrigger value="materials">Materials</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="flex-1 overflow-auto p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="overview" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                {/* Waste Processing Metrics */}
                <WasteMetricsCard
                  title="Waste processed"
                  value="476,117.93"
                  unit="tonnes"
                  trend={12.5}
                  chartData={[
                    { month: 'Jan', value: 400000 },
                    { month: 'Feb', value: 420000 },
                    { month: 'Mar', value: 450000 },
                    { month: 'Apr', value: 476117 }
                  ]}
                />

                {/* Processing by Category */}
                <EmissionsByScope
                  title="Processing by category"
                  percentage={75}
                  categories={[
                    { name: 'Organic', color: '#E91E63', percentage: 45 },
                    { name: 'Plastic', color: '#9E9E9E', percentage: 30 },
                    { name: 'Metal', color: '#757575', percentage: 25 }
                  ]}
                />
              </div>

              {/* Waste Generated Map */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Waste generated</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={viewMode === 'targets' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('targets')}
                    >
                      <Target className="h-4 w-4 mr-1" />
                      view targets
                    </Button>
                    <Button
                      variant={viewMode === 'yearly' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('yearly')}
                    >
                      <Calendar className="h-4 w-4 mr-1" />
                      yearly
                    </Button>
                    <Button
                      variant={viewMode === 'map' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('map')}
                    >
                      <MapPin className="h-4 w-4 mr-1" />
                      map
                    </Button>
                    <Button
                      variant={viewMode === 'table' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('table')}
                    >
                      <Table className="h-4 w-4 mr-1" />
                      table
                    </Button>
                    <Button
                      variant={viewMode === 'chart' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('chart')}
                    >
                      <BarChart3 className="h-4 w-4 mr-1" />
                      chart
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <WasteGeneratedMap viewMode={viewMode} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="waste" className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Waste</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats?.totalMaterials || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      +12% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Processing Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">87%</div>
                    <p className="text-xs text-muted-foreground">
                      +5% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Facilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats?.totalMaterials || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      +3 new this month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">87%</div>
                    <p className="text-xs text-muted-foreground">
                      +2% from last week
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Charts */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Processing Efficiency</CardTitle>
                    <CardDescription>
                      Monthly processing trends
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ProcessingChart />
                  </CardContent>
                </Card>

                <DonutChart
                  title="Types de déchets"
                  data={wasteTypesData}
                  centerValue="100%"
                  centerLabel="Total"
                />
              </div>
            </TabsContent>

            {/* Other tabs remain similar but adapted for waste management */}
            <TabsContent value="processing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Processing Overview</CardTitle>
                  <CardDescription>
                    Track processing activities and efficiency
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 lg:grid-cols-2">
                    <BarChart
                      title="Masses Collectées"
                      data={collectedMassesData}
                      subtitle="Répartition par type de déchet"
                    />
                    <DonutChart
                      title="Traitements"
                      data={treatmentData}
                      centerValue="100%"
                      centerLabel="Total"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="efficiency" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Efficiency Metrics</CardTitle>
                  <CardDescription>
                    Monitor operational efficiency and optimization opportunities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <DonutChart
                    title="Prestataires"
                    data={serviceProvidersData}
                    centerValue="3"
                    centerLabel="Actifs"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="materials" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Material Management</CardTitle>
                  <CardDescription>
                    Manage waste materials and inventory
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <Link href="/materials">
                      <Button className="w-full h-16 flex-col space-y-2 hover:bg-primary/90 transition-colors">
                        <span className="text-sm font-medium">Gérer les Déchets</span>
                      </Button>
                    </Link>
                    <Link href="/analytics">
                      <Button variant="outline" className="w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors">
                        <span className="text-sm font-medium">Voir Analytics</span>
                      </Button>
                    </Link>
                    <Link href="/treatment-methods">
                      <Button variant="outline" className="w-full h-16 flex-col space-y-2 hover:bg-primary/5 transition-colors">
                        <span className="text-sm font-medium">Traitements</span>
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics & Reports</CardTitle>
                  <CardDescription>
                    Detailed analytics and reporting tools
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Analytics dashboard will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/authStore';

export default function DebugPage() {
  const authStore = useAuthStore();
  const [apiTest, setApiTest] = useState<any>(null);
  const [localStorage, setLocalStorage] = useState<any>(null);

  useEffect(() => {
    // Test API connectivity
    const testAPI = async () => {
      try {
        const response = await fetch('http://localhost:4000/health');
        const data = await response.json();
        setApiTest({ success: true, data, status: response.status });
      } catch (error) {
        setApiTest({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    };

    // Check localStorage
    const checkLocalStorage = () => {
      if (typeof window !== 'undefined') {
        const data = {
          accessToken: localStorage.getItem('accessToken'),
          refreshToken: localStorage.getItem('refreshToken'),
          authStorage: localStorage.getItem('auth-storage'),
        };
        setLocalStorage(data);
      }
    };

    testAPI();
    checkLocalStorage();
  }, []);

  const clearStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.clear();
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🔍 CircuLab Debug Page</h1>
        
        {/* Auth Store State */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">🔐 Auth Store State</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Is Loading:</strong> {authStore.isLoading ? '✅ True' : '❌ False'}
            </div>
            <div>
              <strong>Is Authenticated:</strong> {authStore.isAuthenticated ? '✅ True' : '❌ False'}
            </div>
            <div>
              <strong>User:</strong> {authStore.user ? '✅ Present' : '❌ Null'}
            </div>
            <div>
              <strong>Tokens:</strong> {authStore.tokens ? '✅ Present' : '❌ Null'}
            </div>
            <div>
              <strong>Error:</strong> {authStore.error || '❌ None'}
            </div>
          </div>
          
          {authStore.user && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <strong>User Details:</strong>
              <pre className="text-sm mt-2">{JSON.stringify(authStore.user, null, 2)}</pre>
            </div>
          )}
        </div>

        {/* API Test */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">🌐 API Connectivity Test</h2>
          {apiTest ? (
            <div>
              <div className={`p-4 rounded ${apiTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <strong>Status:</strong> {apiTest.success ? '✅ Success' : '❌ Failed'}
                {apiTest.success && (
                  <div className="mt-2">
                    <strong>Response:</strong> {apiTest.data.status} (Status: {apiTest.status})
                  </div>
                )}
                {!apiTest.success && (
                  <div className="mt-2">
                    <strong>Error:</strong> {apiTest.error}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div>Testing API...</div>
          )}
        </div>

        {/* LocalStorage */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">💾 LocalStorage Data</h2>
          {localStorage ? (
            <div>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <strong>Access Token:</strong> {localStorage.accessToken ? '✅ Present' : '❌ None'}
                  {localStorage.accessToken && (
                    <div className="text-xs text-gray-600 mt-1 break-all">
                      {localStorage.accessToken.substring(0, 50)}...
                    </div>
                  )}
                </div>
                <div>
                  <strong>Refresh Token:</strong> {localStorage.refreshToken ? '✅ Present' : '❌ None'}
                  {localStorage.refreshToken && (
                    <div className="text-xs text-gray-600 mt-1 break-all">
                      {localStorage.refreshToken.substring(0, 50)}...
                    </div>
                  )}
                </div>
                <div>
                  <strong>Auth Storage:</strong> {localStorage.authStorage ? '✅ Present' : '❌ None'}
                  {localStorage.authStorage && (
                    <div className="text-xs text-gray-600 mt-1 break-all">
                      {localStorage.authStorage.substring(0, 100)}...
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div>Loading localStorage data...</div>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">🛠️ Debug Actions</h2>
          <div className="space-y-4">
            <button
              onClick={() => authStore.initialize()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-4"
            >
              🔄 Re-initialize Auth
            </button>
            <button
              onClick={clearStorage}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 mr-4"
            >
              🗑️ Clear Storage & Reload
            </button>
            <button
              onClick={() => window.location.href = '/login'}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-4"
            >
              🔐 Go to Login
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              📊 Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

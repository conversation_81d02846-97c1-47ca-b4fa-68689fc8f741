import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Suspense } from "react";
import ErrorBoundary from "@/components/ErrorBoundary";
import { SafeAuthProvider } from "@/lib/safeImports";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "CircuLab - Industrial Waste Valorization Platform",
  description: "Comprehensive platform for managing and valorizing industrial waste materials through intelligent matching and processing tracking.",
};

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ErrorBoundary>
          <Suspense fallback={<LoadingFallback />}>
            <SafeAuthProvider>
              {children}
            </SafeAuthProvider>
          </Suspense>
        </ErrorBoundary>
      </body>
    </html>
  );
}

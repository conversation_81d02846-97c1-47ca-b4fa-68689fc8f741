# 🔧 Session 2 : Atelier Pratique - Diagnostic et Récupération
**Durée : 60 minutes | Hands-on obligatoire**

## 📋 Objectifs de la Session

À la fin de cette session, vous serez capable de :
- ✅ Diagnostiquer des problèmes réels en temps réel
- ✅ Utiliser tous les outils de diagnostic disponibles
- ✅ Interpréter et appliquer les recommandations
- ✅ Récupérer automatiquement des erreurs courantes

---

## 🎯 Partie 1 : Préparation de l'Environnement (10 min)

### Vérification Initiale
```bash
# 1. Vérifier que l'application fonctionne
npm run dev

# 2. Vérifier le score de confiance initial
./scripts/check-dev-health.sh

# 3. Ouvrir le Health Dashboard
open http://localhost:3000/health
```

### Configuration du Terminal
```bash
# Terminal 1 : Monitoring en temps réel
tail -f logs/health-check.log

# Terminal 2 : Commandes de diagnostic
cd circulab-frontend

# Terminal 3 : Health Dashboard ouvert dans le navigateur
```

---

## 🧪 Partie 2 : Simulation Contrôlée d'Erreurs (20 min)

### Exercice 1 : Simulation de Conflit d'Extension (5 min)

**Objectif :** Comprendre comment le système détecte et isole les extensions problématiques.

```bash
# 1. Créer un conflit simulé
./scripts/test-extension-conflict.sh run

# 2. Observer l'impact sur le Health Dashboard
# - Score de confiance devrait baisser
# - Composant "Extensions" devrait passer en warning/error

# 3. Vérifier les recommandations
./scripts/check-dev-health.sh

# 4. Appliquer la récupération automatique
./scripts/test-extension-conflict.sh recover
```

**Questions à observer :**
- Combien de temps pour détecter le problème ?
- Quelles recommandations sont proposées ?
- Le score remonte-t-il après récupération ?

### Exercice 2 : Corruption de Cache (5 min)

**Objectif :** Tester la récupération automatique des caches corrompus.

```bash
# 1. Corrompre le cache Next.js
rm -rf .next/cache/*
echo "corrupted" > .next/cache/webpack.lock

# 2. Vérifier l'impact
./scripts/check-dev-health.sh

# 3. Observer la récupération automatique
npm run build

# 4. Vérifier la récupération
./scripts/check-dev-health.sh
```

### Exercice 3 : Problème de Dépendances (5 min)

**Objectif :** Simuler et résoudre des problèmes de dépendances.

```bash
# 1. Simuler une dépendance manquante
mv node_modules/react node_modules/react.backup

# 2. Vérifier l'impact
./scripts/check-dev-health.sh

# 3. Observer les recommandations
# Le système devrait recommander npm install

# 4. Appliquer la correction
mv node_modules/react.backup node_modules/react

# 5. Vérifier la récupération
./scripts/check-dev-health.sh
```

### Exercice 4 : Erreur TypeScript (5 min)

**Objectif :** Gérer les erreurs de compilation TypeScript.

```bash
# 1. Introduire une erreur TypeScript
echo "const invalidCode: string = 123;" >> src/temp-error.ts

# 2. Vérifier l'impact
./scripts/check-dev-health.sh

# 3. Observer les détails de l'erreur
npx tsc --noEmit

# 4. Corriger l'erreur
rm src/temp-error.ts

# 5. Vérifier la récupération
./scripts/check-dev-health.sh
```

---

## 🔍 Partie 3 : Diagnostic Avancé (15 min)

### Utilisation du Debug Dashboard

**Accès :** `http://localhost:3000/debug`

#### Informations Disponibles
1. **System Info**
   - Version Node.js, npm
   - Système d'exploitation
   - Mémoire disponible

2. **Project Status**
   - État des dépendances
   - Configuration TypeScript
   - Taille des caches

3. **Performance Metrics**
   - Temps de build
   - Temps de démarrage
   - Utilisation mémoire

### Analyse des Logs

```bash
# 1. Logs de santé détaillés
cat logs/health-check.log | grep ERROR

# 2. Logs de build
cat .next/build.log 2>/dev/null || echo "No build log"

# 3. Logs d'alertes
cat logs/alert-history.log 2>/dev/null || echo "No alerts yet"
```

### Métriques de Performance

```bash
# 1. Analyser les métriques
curl -s http://localhost:3000/api/health | jq '.'

# 2. Vérifier les tendances
ls -la metrics/

# 3. Examiner les rapports
ls -la reports/
```

---

## 🛠️ Partie 4 : Actions Correctives en Temps Réel (10 min)

### Scénario Réel : Score de Confiance Bas

**Situation :** Le score de confiance est tombé à 65%

#### Étape 1 : Diagnostic
```bash
# Identifier les problèmes
./scripts/check-dev-health.sh

# Examiner les recommandations
jq '.recommendations[]' logs/health-report.json
```

#### Étape 2 : Priorisation
1. **Erreurs critiques** (score < 50%) → Action immédiate
2. **Warnings** (score 50-80%) → Action planifiée
3. **Optimisations** (score > 80%) → Amélioration continue

#### Étape 3 : Application des Corrections
```bash
# Exemple de corrections courantes

# 1. Nettoyage des caches
rm -rf .next node_modules/.cache

# 2. Réinstallation des dépendances
npm install

# 3. Rebuild complet
npm run build

# 4. Vérification
./scripts/check-dev-health.sh
```

### Workflow de Récupération Standard

```bash
# Script de récupération rapide
#!/bin/bash

echo "🔧 Récupération automatique CircuLab..."

# 1. Nettoyage
rm -rf .next
npm cache clean --force

# 2. Réinstallation
npm install

# 3. Rebuild
npm run build

# 4. Vérification
./scripts/check-dev-health.sh

echo "✅ Récupération terminée"
```

---

## 📊 Partie 5 : Interprétation des Recommandations (5 min)

### Types de Recommandations

#### 🚨 **Critiques** (Action Immédiate)
```json
{
  "level": "critical",
  "message": "TypeScript compilation failed",
  "action": "Fix compilation errors before proceeding",
  "command": "npx tsc --noEmit"
}
```

#### ⚠️ **Warnings** (Action Recommandée)
```json
{
  "level": "warning", 
  "message": "Cache size is large",
  "action": "Consider cleaning build cache",
  "command": "rm -rf .next"
}
```

#### 💡 **Optimisations** (Amélioration)
```json
{
  "level": "info",
  "message": "Dependencies could be updated",
  "action": "Update to latest versions",
  "command": "npm update"
}
```

### Priorisation des Actions

1. **Sécurité** : Erreurs de compilation, dépendances manquantes
2. **Performance** : Caches volumineux, processus lents
3. **Maintenance** : Mises à jour, nettoyage

---

## ✅ Exercices Pratiques

### Exercice Final : Récupération Complète

**Scénario :** Votre environnement est complètement cassé (score < 30%)

```bash
# 1. Casser l'environnement (simulation)
rm -rf .next node_modules
echo "broken" > package.json.backup
mv package.json package.json.original
mv package.json.backup package.json

# 2. Constater les dégâts
./scripts/check-dev-health.sh

# 3. Récupérer étape par étape
mv package.json.original package.json
npm install
npm run build

# 4. Vérifier la récupération complète
./scripts/check-dev-health.sh
```

---

## 🎯 Checklist de Compétences

À la fin de cette session, vous devez être capable de :

- [ ] Identifier rapidement la cause d'un score bas
- [ ] Utiliser les scripts de diagnostic appropriés
- [ ] Interpréter les recommandations du système
- [ ] Appliquer les corrections dans le bon ordre
- [ ] Vérifier l'efficacité des corrections
- [ ] Utiliser le Debug Dashboard pour l'analyse avancée

---

## 🚀 Préparation Session 3

**Prochaine session : Intégration Workflow (30 min)**

**Nous verrons :**
- Configuration des alertes personnelles
- Intégration dans le workflow quotidien
- Bonnes pratiques de l'équipe
- Checklist de début/fin de journée

---

## 📞 Support Immédiat

**Pendant l'atelier :**
- Formateur disponible pour aide individuelle
- Exemples supplémentaires sur demande
- Debugging en temps réel

**Après l'atelier :**
- Documentation complète : `/docs/`
- Scripts de récupération : `/scripts/`
- Support équipe architecture

---

## 🏆 Objectif de Maîtrise

**Vous devez maintenant être autonome pour :**
- Diagnostiquer n'importe quel problème en < 2 minutes
- Récupérer automatiquement en < 5 minutes
- Maintenir un score de confiance > 80% en permanence

**Bravo ! Vous maîtrisez l'Architecture Défensive CircuLab ! 🎉**

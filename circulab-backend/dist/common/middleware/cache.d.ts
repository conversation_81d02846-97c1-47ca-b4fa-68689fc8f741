import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
declare class MemoryCache {
    private cache;
    private cleanupInterval;
    constructor();
    set(key: string, data: any, ttlSeconds?: number): void;
    get(key: string): any | null;
    delete(key: string): boolean;
    clear(): void;
    private cleanup;
    getStats(): {
        size: number;
        keys: string[];
    };
    destroy(): void;
}
declare const cache: MemoryCache;
export interface CacheOptions {
    ttl?: number;
    keyGenerator?: (req: AuthenticatedRequest) => string;
    skipCache?: (req: AuthenticatedRequest) => boolean;
}
/**
 * Cache middleware for Express routes
 * @param options Cache configuration options
 */
export declare function cacheMiddleware(options?: CacheOptions): (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
/**
 * Cache invalidation middleware
 * Clears cache entries that match a pattern
 */
export declare function invalidateCache(pattern?: string | RegExp): (_req: Request, _res: Response, next: NextFunction) => void;
/**
 * Get cache statistics
 */
export declare function getCacheStats(): {
    size: number;
    keys: string[];
};
/**
 * Clear all cache entries
 */
export declare function clearCache(): void;
export default cache;
//# sourceMappingURL=cache.d.ts.map
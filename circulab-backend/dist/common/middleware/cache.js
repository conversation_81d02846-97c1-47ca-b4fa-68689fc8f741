"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheMiddleware = cacheMiddleware;
exports.invalidateCache = invalidateCache;
exports.getCacheStats = getCacheStats;
exports.clearCache = clearCache;
const logger_1 = __importDefault(require("../../config/logger"));
class MemoryCache {
    constructor() {
        this.cache = new Map();
        // Clean up expired entries every 5 minutes
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 5 * 60 * 1000);
    }
    set(key, data, ttlSeconds = 300) {
        const entry = {
            data,
            timestamp: Date.now(),
            ttl: ttlSeconds * 1000,
        };
        this.cache.set(key, entry);
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if entry has expired
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    delete(key) {
        return this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    cleanup() {
        const now = Date.now();
        let deletedCount = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
                deletedCount++;
            }
        }
        if (deletedCount > 0) {
            logger_1.default.debug(`Cache cleanup: removed ${deletedCount} expired entries`);
        }
    }
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
        };
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.clear();
    }
}
// Global cache instance
const cache = new MemoryCache();
/**
 * Cache middleware for Express routes
 * @param options Cache configuration options
 */
function cacheMiddleware(options = {}) {
    const { ttl = 300, // 5 minutes default
    keyGenerator = (req) => `${req.method}:${req.originalUrl}:${req.user?.id || 'anonymous'}`, skipCache = () => false, } = options;
    return (req, res, next) => {
        // Skip caching if specified
        if (skipCache(req)) {
            return next();
        }
        // Generate cache key
        const cacheKey = keyGenerator(req);
        // Try to get from cache
        const cachedData = cache.get(cacheKey);
        if (cachedData) {
            logger_1.default.debug(`Cache hit for key: ${cacheKey}`);
            res.json(cachedData);
            return;
        }
        // Store original json method
        const originalJson = res.json;
        // Override json method to cache the response
        res.json = function (data) {
            // Cache the response data
            cache.set(cacheKey, data, ttl);
            logger_1.default.debug(`Cache set for key: ${cacheKey}, TTL: ${ttl}s`);
            // Call original json method
            return originalJson.call(this, data);
        };
        next();
    };
}
/**
 * Cache invalidation middleware
 * Clears cache entries that match a pattern
 */
function invalidateCache(pattern) {
    return (_req, _res, next) => {
        if (!pattern) {
            cache.clear();
            logger_1.default.debug('Cache cleared completely');
        }
        else {
            const stats = cache.getStats();
            let deletedCount = 0;
            for (const key of stats.keys) {
                if (typeof pattern === 'string' && key.includes(pattern)) {
                    cache.delete(key);
                    deletedCount++;
                }
                else if (pattern instanceof RegExp && pattern.test(key)) {
                    cache.delete(key);
                    deletedCount++;
                }
            }
            logger_1.default.debug(`Cache invalidation: removed ${deletedCount} entries matching pattern`);
        }
        next();
    };
}
/**
 * Get cache statistics
 */
function getCacheStats() {
    return cache.getStats();
}
/**
 * Clear all cache entries
 */
function clearCache() {
    cache.clear();
}
exports.default = cache;
//# sourceMappingURL=cache.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Import configurations
const env_1 = __importDefault(require("./config/env"));
const logger_1 = __importDefault(require("./config/logger"));
const database_1 = __importDefault(require("./config/database"));
// Import middleware
const errorHandler_1 = require("./common/middleware/errorHandler");
const logging_1 = require("./common/middleware/logging");
// Import routes
const auth_routes_1 = __importDefault(require("./modules/auth/auth.routes"));
const wasteMaterials_routes_1 = __importDefault(require("./modules/wasteMaterials/wasteMaterials.routes"));
const treatmentMethods_routes_1 = __importDefault(require("./modules/treatmentMethods/treatmentMethods.routes"));
const companies_routes_1 = __importDefault(require("./modules/companies/companies.routes"));
const users_routes_1 = __importDefault(require("./modules/users/users.routes"));
const notifications_routes_1 = __importDefault(require("./modules/notifications/notifications.routes"));
const processingHistory_routes_1 = __importDefault(require("./modules/processingHistory/processingHistory.routes"));
class App {
    constructor() {
        this.app = (0, express_1.default)();
        this.db = database_1.default.getInstance();
        this.initializeMiddleware();
        this.initializeSwagger();
        this.initializeRoutes();
        this.initializeFrontendServing();
        // Error handling must be last to catch all unhandled routes
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        // CORS configuration
        this.app.use((0, cors_1.default)({
            origin: env_1.default.CORS_ORIGIN.split(',').map(origin => origin.trim()),
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization'],
        }));
        // Body parsing middleware
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        // Logging middleware
        this.app.use(logging_1.requestLogger);
        this.app.use(logging_1.performanceLogger);
        // Health check endpoint
        this.app.get('/health', async (_req, res) => {
            const dbHealth = await this.db.healthCheck();
            res.status(dbHealth ? 200 : 503).json({
                status: dbHealth ? 'healthy' : 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: env_1.default.NODE_ENV,
                database: dbHealth ? 'connected' : 'disconnected',
            });
        });
    }
    initializeSwagger() {
        const options = {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'CircuLab API',
                    version: '1.0.0',
                    description: 'Industrial Waste Valorization Platform API',
                    contact: {
                        name: 'CircuLab Team',
                        email: '<EMAIL>',
                    },
                },
                servers: [
                    {
                        url: `http://localhost:${env_1.default.PORT}/api`,
                        description: 'Development server',
                    },
                ],
                components: {
                    securitySchemes: {
                        bearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                        },
                    },
                    schemas: {
                        User: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', format: 'uuid' },
                                email: { type: 'string', format: 'email' },
                                username: { type: 'string' },
                                isEmailVerified: { type: 'boolean' },
                                createdAt: { type: 'string', format: 'date-time' },
                                updatedAt: { type: 'string', format: 'date-time' },
                                lastLoginAt: { type: 'string', format: 'date-time' },
                                companyId: { type: 'string', format: 'uuid' },
                            },
                        },
                        Company: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', format: 'uuid' },
                                name: { type: 'string' },
                                siret: { type: 'string' },
                                address: { type: 'string' },
                                industryType: { type: 'string' },
                                contactPerson: { type: 'string' },
                                contactEmail: { type: 'string', format: 'email' },
                                createdAt: { type: 'string', format: 'date-time' },
                                updatedAt: { type: 'string', format: 'date-time' },
                            },
                        },
                        WasteMaterial: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', format: 'uuid' },
                                type: { type: 'string' },
                                description: { type: 'string' },
                                quantity: { type: 'number' },
                                unit: { type: 'string' },
                                source: { type: 'string' },
                                currentStatus: {
                                    type: 'string',
                                    enum: ['AVAILABLE', 'PENDING_PICKUP', 'IN_TRANSIT', 'PROCESSING', 'VALORIZED', 'DISPOSED', 'AWAITING_ANALYSIS']
                                },
                                location_address: { type: 'string' },
                                location_latitude: { type: 'number' },
                                location_longitude: { type: 'number' },
                                isHazardous: { type: 'boolean' },
                                createdAt: { type: 'string', format: 'date-time' },
                                updatedAt: { type: 'string', format: 'date-time' },
                            },
                        },
                        ApiResponse: {
                            type: 'object',
                            properties: {
                                success: { type: 'boolean' },
                                data: { type: 'object' },
                                message: { type: 'string' },
                                timestamp: { type: 'string', format: 'date-time' },
                            },
                        },
                        Error: {
                            type: 'object',
                            properties: {
                                success: { type: 'boolean', example: false },
                                error: {
                                    type: 'object',
                                    properties: {
                                        message: { type: 'string' },
                                    },
                                },
                                timestamp: { type: 'string', format: 'date-time' },
                                path: { type: 'string' },
                                method: { type: 'string' },
                            },
                        },
                    },
                },
            },
            apis: ['./src/modules/**/*.controller.ts', './src/modules/**/*.routes.ts'],
        };
        const specs = (0, swagger_jsdoc_1.default)(options);
        this.app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(specs, {
            explorer: true,
            customCss: '.swagger-ui .topbar { display: none }',
            customSiteTitle: 'CircuLab API Documentation',
        }));
        // Serve OpenAPI spec as JSON
        this.app.get('/api-docs.json', (_req, res) => {
            res.setHeader('Content-Type', 'application/json');
            res.send(specs);
        });
    }
    initializeRoutes() {
        // API routes
        this.app.use('/api/auth', auth_routes_1.default);
        this.app.use('/api/waste-materials', wasteMaterials_routes_1.default);
        this.app.use('/api/treatment-methods', treatmentMethods_routes_1.default);
        this.app.use('/api/companies', companies_routes_1.default);
        this.app.use('/api/users', users_routes_1.default);
        this.app.use('/api/notifications', notifications_routes_1.default);
        this.app.use('/api/processing-history', processingHistory_routes_1.default);
    }
    initializeFrontendServing() {
        // Check if we're in production mode and frontend build exists
        // From dist/server.js, we need to go up to project root, then to frontend
        const projectRoot = path_1.default.join(__dirname, '../..');
        const frontendBuildPath = path_1.default.join(projectRoot, 'circulab-frontend/.next');
        const frontendStaticPath = path_1.default.join(projectRoot, 'circulab-frontend/out');
        const frontendPublicPath = path_1.default.join(projectRoot, 'circulab-frontend/public');
        // Debug logging
        logger_1.default.info('Frontend paths:', {
            buildPath: frontendBuildPath,
            staticPath: frontendStaticPath,
            publicPath: frontendPublicPath,
            buildExists: fs_1.default.existsSync(frontendBuildPath),
            staticExists: fs_1.default.existsSync(frontendStaticPath),
            publicExists: fs_1.default.existsSync(frontendPublicPath),
        });
        // Check if frontend build exists to determine serving mode
        const hasFrontendBuild = fs_1.default.existsSync(frontendBuildPath) || fs_1.default.existsSync(frontendStaticPath);
        // Force production mode if frontend build exists, regardless of NODE_ENV
        const shouldServeStatic = hasFrontendBuild || process.env.FORCE_STATIC_SERVING === 'true';
        logger_1.default.info('Frontend serving decision:', {
            hasFrontendBuild,
            shouldServeStatic,
            nodeEnv: env_1.default.NODE_ENV,
            forceStatic: process.env.FORCE_STATIC_SERVING,
        });
        // In development mode without frontend build, serve API info at root
        if (!shouldServeStatic) {
            logger_1.default.info('Serving in development mode - no static frontend');
            this.app.get('/', (_req, res) => {
                res.json({
                    message: 'CircuLab API is running!',
                    version: '1.0.0',
                    documentation: '/api-docs',
                    health: '/health',
                    frontend: 'http://localhost:3000',
                    note: 'In development mode. Frontend runs separately on port 3000.',
                });
            });
            return;
        }
        logger_1.default.info('Setting up static frontend serving...');
        // Production mode: serve the Next.js application
        try {
            // Serve specific static assets only (not the entire directory)
            if (fs_1.default.existsSync(frontendStaticPath)) {
                // Static export mode - serve specific assets
                this.app.use('/_next', express_1.default.static(path_1.default.join(frontendStaticPath, '_next')));
                this.app.use('/static', express_1.default.static(path_1.default.join(frontendStaticPath, 'static')));
                logger_1.default.info('Serving Next.js static export assets from:', frontendStaticPath);
            }
            else if (fs_1.default.existsSync(frontendBuildPath)) {
                // Server-side rendering mode
                this.app.use('/_next', express_1.default.static(path_1.default.join(frontendBuildPath, 'static')));
                this.app.use('/static', express_1.default.static(path_1.default.join(frontendBuildPath, 'static')));
                logger_1.default.info('Serving Next.js build assets from:', frontendBuildPath);
            }
            // Serve specific public assets (favicon, images, etc.) but not index.html
            if (fs_1.default.existsSync(frontendPublicPath)) {
                // Serve specific files from public directory
                this.app.get('/favicon.ico', (_req, res) => {
                    const faviconPath = path_1.default.join(frontendPublicPath, 'favicon.ico');
                    if (fs_1.default.existsSync(faviconPath)) {
                        res.sendFile(faviconPath);
                    }
                    else {
                        res.status(404).end();
                    }
                });
                // Serve other static assets from public (images, etc.)
                this.app.use('/images', express_1.default.static(path_1.default.join(frontendPublicPath, 'images')));
                this.app.use('/icons', express_1.default.static(path_1.default.join(frontendPublicPath, 'icons')));
                logger_1.default.info('Serving public assets from:', frontendPublicPath);
            }
            // Handle client-side routing - serve index.html for all non-API routes
            // Use a more specific approach instead of '*' which can cause path-to-regexp errors
            this.app.use((req, res, next) => {
                // Skip API routes, health check, and documentation
                if (req.path.startsWith('/api/') ||
                    req.path.startsWith('/health') ||
                    req.path.startsWith('/api-docs') ||
                    req.path.startsWith('/_next') ||
                    req.path.startsWith('/static') ||
                    req.path.startsWith('/images') ||
                    req.path.startsWith('/icons') ||
                    req.path === '/favicon.ico') {
                    return next();
                }
                // Always serve the frontend for non-API routes
                logger_1.default.info(`Serving frontend for route: ${req.path}`);
                // Try to serve the appropriate HTML file from Next.js build
                let indexPath = null;
                // Check for Next.js static export
                if (fs_1.default.existsSync(frontendStaticPath)) {
                    const staticIndexPath = path_1.default.join(frontendStaticPath, 'index.html');
                    if (fs_1.default.existsSync(staticIndexPath)) {
                        indexPath = staticIndexPath;
                    }
                }
                // Check for Next.js standalone build
                if (!indexPath && fs_1.default.existsSync(frontendBuildPath)) {
                    const buildIndexPath = path_1.default.join(frontendBuildPath, 'server/pages/index.html');
                    if (fs_1.default.existsSync(buildIndexPath)) {
                        indexPath = buildIndexPath;
                    }
                    // Try alternative Next.js build structure
                    const altIndexPath = path_1.default.join(frontendBuildPath, 'static/index.html');
                    if (!indexPath && fs_1.default.existsSync(altIndexPath)) {
                        indexPath = altIndexPath;
                    }
                }
                // Check public directory
                if (!indexPath) {
                    const publicIndexPath = path_1.default.join(__dirname, '../../circulab-frontend/public/index.html');
                    if (fs_1.default.existsSync(publicIndexPath)) {
                        indexPath = publicIndexPath;
                    }
                }
                if (indexPath) {
                    res.sendFile(path_1.default.resolve(indexPath));
                }
                else {
                    // Fallback: serve a basic HTML page with Next.js app structure
                    res.send(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>CircuLab - Waste Valorization Platform</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                  body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; }
                  .container { display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                  .card { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
                  .logo { font-size: 2rem; font-weight: bold; color: #667eea; margin-bottom: 1rem; }
                  .message { color: #666; margin-bottom: 1.5rem; }
                  .button { display: inline-block; background: #667eea; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; margin: 0.5rem; }
                  .button:hover { background: #5a6fd8; }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="card">
                    <div class="logo">🌱 CircuLab</div>
                    <div class="message">
                      <p>Plateforme de Valorisation des Déchets Industriels</p>
                      <p>L'application frontend est en cours de chargement...</p>
                    </div>
                    <a href="/api-docs" class="button">📚 Documentation API</a>
                    <a href="/health" class="button">🔍 État du Serveur</a>
                    <script>
                      // Auto-refresh every 3 seconds to check if frontend is available
                      setTimeout(() => window.location.reload(), 3000);
                    </script>
                  </div>
                </div>
              </body>
            </html>
          `);
                }
            });
        }
        catch (error) {
            logger_1.default.error('Error setting up frontend serving:', error);
            logger_1.default.error('Error details:', {
                message: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : 'No stack trace',
            });
            // Fallback route for production
            this.app.get('/', (_req, res) => {
                res.json({
                    message: 'CircuLab API is running!',
                    version: '1.0.0',
                    documentation: '/api-docs',
                    health: '/health',
                    error: 'Frontend build not found',
                    errorDetails: error instanceof Error ? error.message : 'Unknown error',
                });
            });
        }
    }
    initializeErrorHandling() {
        // 404 handler
        this.app.use(errorHandler_1.notFoundHandler);
        // Global error handler
        this.app.use(errorHandler_1.errorHandler);
    }
    async start() {
        try {
            // Connect to database
            await this.db.connect();
            // Start server
            this.app.listen(env_1.default.PORT, () => {
                logger_1.default.info(`🚀 CircuLab API server started successfully`, {
                    port: env_1.default.PORT,
                    environment: env_1.default.NODE_ENV,
                    documentation: `http://localhost:${env_1.default.PORT}/api-docs`,
                });
            });
        }
        catch (error) {
            logger_1.default.error('Failed to start server', { error });
            process.exit(1);
        }
    }
    async stop() {
        try {
            await this.db.disconnect();
            logger_1.default.info('Server stopped successfully');
        }
        catch (error) {
            logger_1.default.error('Error stopping server', { error });
        }
    }
}
exports.default = App;
//# sourceMappingURL=app.js.map
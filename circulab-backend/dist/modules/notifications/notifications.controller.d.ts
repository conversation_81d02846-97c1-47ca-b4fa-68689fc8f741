import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../../common/middleware/auth';
/**
 * @swagger
 * components:
 *   schemas:
 *     Notification:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         userId:
 *           type: string
 *           format: uuid
 *         type:
 *           type: string
 *           enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
 *         message:
 *           type: string
 *         isRead:
 *           type: boolean
 *         linkTo:
 *           type: string
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *     CreateNotificationDto:
 *       type: object
 *       required:
 *         - userId
 *         - type
 *         - message
 *       properties:
 *         userId:
 *           type: string
 *           format: uuid
 *         type:
 *           type: string
 *           enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
 *         message:
 *           type: string
 *           maxLength: 500
 *         linkTo:
 *           type: string
 *           maxLength: 200
 */
export declare class NotificationsController {
    private notificationsService;
    constructor();
    /**
     * @swagger
     * /api/notifications:
     *   post:
     *     summary: Create a new notification
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/CreateNotificationDto'
     *     responses:
     *       201:
     *         description: Notification created successfully
     *       400:
     *         description: Invalid input data
     *       404:
     *         description: User not found
     */
    create: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/bulk:
     *   post:
     *     summary: Create bulk notifications
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - userIds
     *               - type
     *               - message
     *             properties:
     *               userIds:
     *                 type: array
     *                 items:
     *                   type: string
     *                   format: uuid
     *               type:
     *                 type: string
     *                 enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
     *               message:
     *                 type: string
     *               linkTo:
     *                 type: string
     *     responses:
     *       201:
     *         description: Bulk notifications created successfully
     */
    createBulk: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications:
     *   get:
     *     summary: Get all notifications with filtering and pagination
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: query
     *         name: userId
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Filter by user ID
     *       - in: query
     *         name: type
     *         schema:
     *           type: string
     *         description: Filter by notification type
     *       - in: query
     *         name: isRead
     *         schema:
     *           type: boolean
     *         description: Filter by read status
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *           default: 1
     *         description: Page number
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *           default: 10
     *         description: Number of items per page
     *     responses:
     *       200:
     *         description: Notifications retrieved successfully
     */
    findAll: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/user/{userId}:
     *   get:
     *     summary: Get notifications for a specific user
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: userId
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: User ID
     *     responses:
     *       200:
     *         description: User notifications retrieved successfully
     *       404:
     *         description: User not found
     */
    findByUserId: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/my:
     *   get:
     *     summary: Get current user's notifications
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: Current user notifications retrieved successfully
     */
    findMyNotifications: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/{id}:
     *   get:
     *     summary: Get notification by ID
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Notification ID
     *     responses:
     *       200:
     *         description: Notification retrieved successfully
     *       404:
     *         description: Notification not found
     */
    findById: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/{id}:
     *   put:
     *     summary: Update notification
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Notification ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               isRead:
     *                 type: boolean
     *     responses:
     *       200:
     *         description: Notification updated successfully
     *       404:
     *         description: Notification not found
     */
    update: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/{id}/read:
     *   put:
     *     summary: Mark notification as read
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Notification ID
     *     responses:
     *       200:
     *         description: Notification marked as read
     *       404:
     *         description: Notification not found
     */
    markAsRead: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/mark-all-read:
     *   put:
     *     summary: Mark all notifications as read for a user
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - userId
     *             properties:
     *               userId:
     *                 type: string
     *                 format: uuid
     *               type:
     *                 type: string
     *     responses:
     *       200:
     *         description: Notifications marked as read
     */
    markAllAsRead: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/{id}:
     *   delete:
     *     summary: Delete notification
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Notification ID
     *     responses:
     *       200:
     *         description: Notification deleted successfully
     *       404:
     *         description: Notification not found
     */
    delete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/notifications/statistics:
     *   get:
     *     summary: Get notification statistics
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: Statistics retrieved successfully
     */
    getStatistics: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=notifications.controller.d.ts.map
import { NotificationType } from '@prisma/client';
export declare class CreateNotificationDto {
    userId: string;
    type: NotificationType;
    message: string;
    linkTo?: string;
}
export declare class UpdateNotificationDto {
    isRead?: boolean;
}
export declare class NotificationParamsDto {
    id: string;
}
export declare class NotificationQueryDto {
    userId?: string;
    type?: NotificationType;
    isRead?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class BulkNotificationDto {
    userIds: string[];
    type: NotificationType;
    message: string;
    linkTo?: string;
}
export declare class MarkAllReadDto {
    userId: string;
    type?: NotificationType;
}
//# sourceMappingURL=notification.dto.d.ts.map
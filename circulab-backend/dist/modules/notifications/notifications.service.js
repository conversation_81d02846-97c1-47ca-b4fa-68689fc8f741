"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const client_1 = require("@prisma/client");
const database_1 = __importDefault(require("../../config/database"));
const errorHandler_1 = require("../../common/middleware/errorHandler");
const logger_1 = __importDefault(require("../../config/logger"));
class NotificationsService {
    constructor() {
        this.db = database_1.default.getInstance();
    }
    async create(createNotificationDto) {
        try {
            // Verify user exists
            const user = await this.db.prisma.user.findUnique({
                where: { id: createNotificationDto.userId }
            });
            if (!user) {
                throw new errorHandler_1.AppError('User not found', 404);
            }
            const notification = await this.db.prisma.notification.create({
                data: createNotificationDto,
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            username: true
                        }
                    }
                }
            });
            logger_1.default.info('Notification created successfully', {
                notificationId: notification.id,
                userId: notification.userId,
                type: notification.type
            });
            return notification;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error creating notification', { error, data: createNotificationDto });
            throw new errorHandler_1.AppError('Failed to create notification', 500);
        }
    }
    async createBulk(bulkNotificationDto) {
        try {
            // Verify all users exist
            const users = await this.db.prisma.user.findMany({
                where: { id: { in: bulkNotificationDto.userIds } },
                select: { id: true }
            });
            if (users.length !== bulkNotificationDto.userIds.length) {
                throw new errorHandler_1.AppError('One or more users not found', 404);
            }
            // Create notifications for all users
            const notifications = bulkNotificationDto.userIds.map(userId => ({
                userId,
                type: bulkNotificationDto.type,
                message: bulkNotificationDto.message,
                linkTo: bulkNotificationDto.linkTo
            }));
            const result = await this.db.prisma.notification.createMany({
                data: notifications
            });
            logger_1.default.info('Bulk notifications created successfully', {
                count: result.count,
                type: bulkNotificationDto.type,
                userCount: bulkNotificationDto.userIds.length
            });
            return { count: result.count };
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error creating bulk notifications', { error, data: bulkNotificationDto });
            throw new errorHandler_1.AppError('Failed to create bulk notifications', 500);
        }
    }
    async findAll(queryDto) {
        try {
            const { userId, type, isRead, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = queryDto;
            const where = {};
            if (userId) {
                where.userId = userId;
            }
            if (type) {
                where.type = type;
            }
            if (isRead !== undefined) {
                where.isRead = isRead;
            }
            const [notifications, total] = await Promise.all([
                this.db.prisma.notification.findMany({
                    where,
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                username: true
                            }
                        }
                    },
                    orderBy: { [sortBy]: sortOrder },
                    skip: (page - 1) * limit,
                    take: limit
                }),
                this.db.prisma.notification.count({ where })
            ]);
            return {
                notifications,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            logger_1.default.error('Error fetching notifications', { error, query: queryDto });
            throw new errorHandler_1.AppError('Failed to fetch notifications', 500);
        }
    }
    async findById(id) {
        try {
            const notification = await this.db.prisma.notification.findUnique({
                where: { id },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            username: true,
                            company: {
                                select: {
                                    id: true,
                                    name: true
                                }
                            }
                        }
                    }
                }
            });
            if (!notification) {
                throw new errorHandler_1.AppError('Notification not found', 404);
            }
            return notification;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error fetching notification by ID', { error, notificationId: id });
            throw new errorHandler_1.AppError('Failed to fetch notification', 500);
        }
    }
    async findByUserId(userId, queryDto) {
        try {
            // Verify user exists
            const user = await this.db.prisma.user.findUnique({
                where: { id: userId }
            });
            if (!user) {
                throw new errorHandler_1.AppError('User not found', 404);
            }
            const { type, isRead, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = queryDto;
            const where = { userId };
            if (type) {
                where.type = type;
            }
            if (isRead !== undefined) {
                where.isRead = isRead;
            }
            const [notifications, total, unreadCount] = await Promise.all([
                this.db.prisma.notification.findMany({
                    where,
                    orderBy: { [sortBy]: sortOrder },
                    skip: (page - 1) * limit,
                    take: limit
                }),
                this.db.prisma.notification.count({ where }),
                this.db.prisma.notification.count({
                    where: { userId, isRead: false }
                })
            ]);
            return {
                notifications,
                total,
                unreadCount,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error fetching user notifications', { error, userId, query: queryDto });
            throw new errorHandler_1.AppError('Failed to fetch user notifications', 500);
        }
    }
    async update(id, updateNotificationDto) {
        try {
            // Check if notification exists
            const existingNotification = await this.db.prisma.notification.findUnique({
                where: { id }
            });
            if (!existingNotification) {
                throw new errorHandler_1.AppError('Notification not found', 404);
            }
            const notification = await this.db.prisma.notification.update({
                where: { id },
                data: updateNotificationDto,
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            username: true
                        }
                    }
                }
            });
            logger_1.default.info('Notification updated successfully', {
                notificationId: id,
                userId: notification.userId
            });
            return notification;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error updating notification', { error, notificationId: id, data: updateNotificationDto });
            throw new errorHandler_1.AppError('Failed to update notification', 500);
        }
    }
    async markAsRead(id) {
        return this.update(id, { isRead: true });
    }
    async markAllAsRead(markAllReadDto) {
        try {
            // Verify user exists
            const user = await this.db.prisma.user.findUnique({
                where: { id: markAllReadDto.userId }
            });
            if (!user) {
                throw new errorHandler_1.AppError('User not found', 404);
            }
            const where = {
                userId: markAllReadDto.userId,
                isRead: false
            };
            if (markAllReadDto.type) {
                where.type = markAllReadDto.type;
            }
            const result = await this.db.prisma.notification.updateMany({
                where,
                data: { isRead: true }
            });
            logger_1.default.info('Notifications marked as read', {
                userId: markAllReadDto.userId,
                count: result.count,
                type: markAllReadDto.type
            });
            return { count: result.count };
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error marking notifications as read', { error, data: markAllReadDto });
            throw new errorHandler_1.AppError('Failed to mark notifications as read', 500);
        }
    }
    async delete(id) {
        try {
            // Check if notification exists
            const existingNotification = await this.db.prisma.notification.findUnique({
                where: { id }
            });
            if (!existingNotification) {
                throw new errorHandler_1.AppError('Notification not found', 404);
            }
            await this.db.prisma.notification.delete({
                where: { id }
            });
            logger_1.default.info('Notification deleted successfully', {
                notificationId: id,
                userId: existingNotification.userId
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error deleting notification', { error, notificationId: id });
            throw new errorHandler_1.AppError('Failed to delete notification', 500);
        }
    }
    async deleteByUserId(userId, type) {
        try {
            // Verify user exists
            const user = await this.db.prisma.user.findUnique({
                where: { id: userId }
            });
            if (!user) {
                throw new errorHandler_1.AppError('User not found', 404);
            }
            const where = { userId };
            if (type) {
                where.type = type;
            }
            const result = await this.db.prisma.notification.deleteMany({
                where
            });
            logger_1.default.info('User notifications deleted', {
                userId,
                count: result.count,
                type
            });
            return { count: result.count };
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error deleting user notifications', { error, userId, type });
            throw new errorHandler_1.AppError('Failed to delete user notifications', 500);
        }
    }
    async getStatistics() {
        try {
            const [totalNotifications, unreadNotifications, notificationsByType, notificationsByUser, recentNotifications] = await Promise.all([
                this.db.prisma.notification.count(),
                this.db.prisma.notification.count({ where: { isRead: false } }),
                this.db.prisma.notification.groupBy({
                    by: ['type'],
                    _count: { id: true }
                }),
                this.db.prisma.notification.groupBy({
                    by: ['userId'],
                    _count: { id: true }
                }),
                this.db.prisma.notification.findMany({
                    select: {
                        id: true,
                        type: true,
                        message: true,
                        createdAt: true
                    },
                    orderBy: { createdAt: 'desc' },
                    take: 10
                })
            ]);
            // Get user details for notificationsByUser
            const userIds = notificationsByUser.map(item => item.userId);
            const users = await this.db.prisma.user.findMany({
                where: { id: { in: userIds } },
                select: { id: true, email: true }
            });
            const userMap = new Map(users.map(u => [u.id, u.email]));
            // Get unread counts per user
            const unreadCounts = await this.db.prisma.notification.groupBy({
                by: ['userId'],
                where: { isRead: false },
                _count: { id: true }
            });
            const unreadMap = new Map(unreadCounts.map(item => [item.userId, item._count.id]));
            return {
                totalNotifications,
                unreadNotifications,
                notificationsByType: notificationsByType.map(item => ({
                    type: item.type,
                    count: item._count.id
                })),
                notificationsByUser: notificationsByUser.map(item => ({
                    userId: item.userId,
                    userEmail: userMap.get(item.userId) || 'Unknown',
                    count: item._count.id,
                    unreadCount: unreadMap.get(item.userId) || 0
                })),
                recentNotifications
            };
        }
        catch (error) {
            logger_1.default.error('Error fetching notification statistics', { error });
            throw new errorHandler_1.AppError('Failed to fetch notification statistics', 500);
        }
    }
    // Helper method to create system notifications
    async createSystemNotification(userIds, message, linkTo) {
        return this.createBulk({
            userIds,
            type: client_1.NotificationType.SYSTEM_ALERT,
            message,
            linkTo
        });
    }
    // Helper method to create waste status notifications
    async createWasteStatusNotification(userId, wasteId, status) {
        return this.create({
            userId,
            type: client_1.NotificationType.WASTE_STATUS_UPDATED,
            message: `Your waste material status has been updated to: ${status}`,
            linkTo: `/materials/${wasteId}`
        });
    }
    // Helper method to create new waste available notifications
    async createNewWasteNotification(userIds, wasteType, wasteId) {
        return this.createBulk({
            userIds,
            type: client_1.NotificationType.NEW_WASTE_AVAILABLE,
            message: `New ${wasteType} waste material is available for processing`,
            linkTo: `/materials/${wasteId}`
        });
    }
}
exports.NotificationsService = NotificationsService;
//# sourceMappingURL=notifications.service.js.map
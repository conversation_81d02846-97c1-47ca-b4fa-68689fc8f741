import { Notification, NotificationType } from '@prisma/client';
import { CreateNotificationDto, UpdateNotificationDto, NotificationQueryDto, BulkNotificationDto, MarkAllReadDto } from './dto/notification.dto';
export declare class NotificationsService {
    private db;
    constructor();
    create(createNotificationDto: CreateNotificationDto): Promise<Notification>;
    createBulk(bulkNotificationDto: BulkNotificationDto): Promise<{
        count: number;
    }>;
    findAll(queryDto: NotificationQueryDto): Promise<{
        notifications: Notification[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findById(id: string): Promise<Notification>;
    findByUserId(userId: string, queryDto: Omit<NotificationQueryDto, 'userId'>): Promise<{
        notifications: Notification[];
        total: number;
        unreadCount: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    update(id: string, updateNotificationDto: UpdateNotificationDto): Promise<Notification>;
    markAsRead(id: string): Promise<Notification>;
    markAllAsRead(markAllReadDto: MarkAllReadDto): Promise<{
        count: number;
    }>;
    delete(id: string): Promise<void>;
    deleteByUserId(userId: string, type?: NotificationType): Promise<{
        count: number;
    }>;
    getStatistics(): Promise<{
        totalNotifications: number;
        unreadNotifications: number;
        notificationsByType: Array<{
            type: NotificationType;
            count: number;
        }>;
        notificationsByUser: Array<{
            userId: string;
            userEmail: string;
            count: number;
            unreadCount: number;
        }>;
        recentNotifications: Array<{
            id: string;
            type: NotificationType;
            message: string;
            createdAt: Date;
        }>;
    }>;
    createSystemNotification(userIds: string[], message: string, linkTo?: string): Promise<{
        count: number;
    }>;
    createWasteStatusNotification(userId: string, wasteId: string, status: string): Promise<Notification>;
    createNewWasteNotification(userIds: string[], wasteType: string, wasteId: string): Promise<{
        count: number;
    }>;
}
//# sourceMappingURL=notifications.service.d.ts.map
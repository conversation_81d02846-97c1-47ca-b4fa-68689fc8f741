"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const notifications_controller_1 = require("./notifications.controller");
const auth_1 = require("../../common/middleware/auth");
const validation_1 = require("../../common/middleware/validation");
const notification_dto_1 = require("./dto/notification.dto");
const cache_1 = require("../../common/middleware/cache");
const router = (0, express_1.Router)();
const notificationsController = new notifications_controller_1.NotificationsController();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
// Statistics route (before parameterized routes) - with caching
router.get('/statistics', (0, auth_1.requirePermission)('view_analytics'), (0, cache_1.cacheMiddleware)({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `notification-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
}), notificationsController.getStatistics);
// Special routes
router.get('/my', notificationsController.findMyNotifications);
router.post('/bulk', (0, auth_1.requirePermission)('manage_notifications'), (0, validation_1.validationMiddleware)(notification_dto_1.BulkNotificationDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.createBulk);
router.put('/mark-all-read', (0, validation_1.validationMiddleware)(notification_dto_1.MarkAllReadDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.markAllAsRead);
// User-specific notifications
router.get('/user/:userId', (0, auth_1.requirePermission)('manage_notifications'), notificationsController.findByUserId);
// CRUD routes
router.post('/', (0, auth_1.requirePermission)('manage_notifications'), (0, validation_1.validationMiddleware)(notification_dto_1.CreateNotificationDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.create);
router.get('/', (0, auth_1.requirePermission)('manage_notifications'), (0, validation_1.validateQuery)(notification_dto_1.NotificationQueryDto), notificationsController.findAll);
router.get('/:id', (0, validation_1.validateParams)(notification_dto_1.NotificationParamsDto), notificationsController.findById);
router.put('/:id', (0, validation_1.validateParams)(notification_dto_1.NotificationParamsDto), (0, validation_1.validationMiddleware)(notification_dto_1.UpdateNotificationDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.update);
router.put('/:id/read', (0, validation_1.validateParams)(notification_dto_1.NotificationParamsDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.markAsRead);
router.delete('/:id', (0, validation_1.validateParams)(notification_dto_1.NotificationParamsDto), (0, cache_1.invalidateCache)('notification-statistics'), notificationsController.delete);
exports.default = router;
//# sourceMappingURL=notifications.routes.js.map
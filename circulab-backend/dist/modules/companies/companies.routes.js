"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const companies_controller_1 = require("./companies.controller");
const auth_1 = require("../../common/middleware/auth");
const validation_1 = require("../../common/middleware/validation");
const company_dto_1 = require("./dto/company.dto");
const cache_1 = require("../../common/middleware/cache");
const router = (0, express_1.Router)();
const companiesController = new companies_controller_1.CompaniesController();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
// Statistics route (before parameterized routes) - with caching
router.get('/statistics', (0, auth_1.requirePermission)('view_analytics'), (0, cache_1.cacheMiddleware)({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `company-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
}), companiesController.getStatistics);
// CRUD routes
router.post('/', (0, auth_1.requirePermission)('manage_companies'), (0, validation_1.validationMiddleware)(company_dto_1.CreateCompanyDto), (0, cache_1.invalidateCache)('company-statistics'), // Invalidate statistics cache
companiesController.create);
router.get('/', (0, auth_1.requirePermission)('read_companies'), (0, validation_1.validateQuery)(company_dto_1.CompanyQueryDto), companiesController.findAll);
router.get('/:id', (0, auth_1.requirePermission)('read_companies'), (0, validation_1.validateParams)(company_dto_1.CompanyParamsDto), companiesController.findById);
router.put('/:id', (0, auth_1.requirePermission)('manage_companies'), (0, validation_1.validateParams)(company_dto_1.CompanyParamsDto), (0, validation_1.validationMiddleware)(company_dto_1.UpdateCompanyDto), (0, cache_1.invalidateCache)('company-statistics'), // Invalidate statistics cache
companiesController.update);
router.delete('/:id', (0, auth_1.requirePermission)('manage_companies'), (0, validation_1.validateParams)(company_dto_1.CompanyParamsDto), (0, cache_1.invalidateCache)('company-statistics'), // Invalidate statistics cache
companiesController.delete);
exports.default = router;
//# sourceMappingURL=companies.routes.js.map
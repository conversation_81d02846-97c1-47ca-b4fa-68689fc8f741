import { Company } from '@prisma/client';
import { CreateCompanyDto, UpdateCompanyDto, CompanyQueryDto } from './dto/company.dto';
export declare class CompaniesService {
    private db;
    constructor();
    create(createCompanyDto: CreateCompanyDto): Promise<Company>;
    findAll(queryDto: CompanyQueryDto): Promise<{
        companies: Company[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findById(id: string): Promise<Company>;
    update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<Company>;
    delete(id: string): Promise<void>;
    getStatistics(): Promise<{
        totalCompanies: number;
        companiesByIndustry: Array<{
            industryType: string;
            count: number;
        }>;
        topProducers: Array<{
            name: string;
            wasteCount: number;
        }>;
        topProcessors: Array<{
            name: string;
            processedCount: number;
        }>;
        recentlyJoined: Array<{
            name: string;
            createdAt: Date;
        }>;
    }>;
}
//# sourceMappingURL=companies.service.d.ts.map
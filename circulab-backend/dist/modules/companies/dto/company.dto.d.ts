export declare class CreateCompanyDto {
    name: string;
    siret?: string;
    address?: string;
    industryType?: string;
    contactPerson?: string;
    contactEmail?: string;
}
export declare class UpdateCompanyDto {
    name?: string;
    siret?: string;
    address?: string;
    industryType?: string;
    contactPerson?: string;
    contactEmail?: string;
}
export declare class CompanyParamsDto {
    id: string;
}
export declare class CompanyQueryDto {
    search?: string;
    industryType?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
//# sourceMappingURL=company.dto.d.ts.map
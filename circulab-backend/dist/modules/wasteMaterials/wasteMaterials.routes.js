"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const wasteMaterials_controller_1 = require("./wasteMaterials.controller");
const validation_1 = require("../../common/middleware/validation");
const auth_1 = require("../../common/middleware/auth");
const cache_1 = require("../../common/middleware/cache");
const wasteMaterial_dto_1 = require("./dto/wasteMaterial.dto");
const router = (0, express_1.Router)();
const wasteMaterialsController = new wasteMaterials_controller_1.WasteMaterialsController();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
// Statistics route (before parameterized routes) - with caching
router.get('/statistics', (0, auth_1.requirePermission)('view_analytics'), (0, cache_1.cacheMiddleware)({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
}), wasteMaterialsController.getStatistics);
// CRUD routes
router.post('/', (0, auth_1.requirePermission)('create_waste'), (0, validation_1.validationMiddleware)(wasteMaterial_dto_1.CreateWasteMaterialDto), (0, cache_1.invalidateCache)('statistics'), // Invalidate statistics cache
wasteMaterialsController.create);
router.get('/', (0, auth_1.requirePermission)('read_waste'), (0, validation_1.validateQuery)(wasteMaterial_dto_1.WasteMaterialQueryDto), wasteMaterialsController.findAll);
router.get('/:id', (0, auth_1.requirePermission)('read_waste'), (0, validation_1.validateParams)(wasteMaterial_dto_1.WasteMaterialParamsDto), wasteMaterialsController.findById);
router.put('/:id', (0, auth_1.requirePermission)('update_waste'), (0, validation_1.validateParams)(wasteMaterial_dto_1.WasteMaterialParamsDto), (0, validation_1.validationMiddleware)(wasteMaterial_dto_1.UpdateWasteMaterialDto), (0, cache_1.invalidateCache)('statistics'), // Invalidate statistics cache
wasteMaterialsController.update);
router.delete('/:id', (0, auth_1.requirePermission)('delete_waste'), (0, validation_1.validateParams)(wasteMaterial_dto_1.WasteMaterialParamsDto), (0, cache_1.invalidateCache)('statistics'), // Invalidate statistics cache
wasteMaterialsController.delete);
exports.default = router;
//# sourceMappingURL=wasteMaterials.routes.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const users_service_1 = require("./users.service");
const logger_1 = __importDefault(require("../../config/logger"));
/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         username:
 *           type: string
 *           nullable: true
 *         isEmailVerified:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           nullable: true
 *     CreateUserDto:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 8
 *           maxLength: 128
 *         username:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         companyId:
 *           type: string
 *           format: uuid
 *         roleNames:
 *           type: array
 *           items:
 *             type: string
 *         isEmailVerified:
 *           type: boolean
 */
class UsersController {
    constructor() {
        /**
         * @swagger
         * /api/users:
         *   post:
         *     summary: Create a new user
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/CreateUserDto'
         *     responses:
         *       201:
         *         description: User created successfully
         *       400:
         *         description: Invalid input data
         *       409:
         *         description: User already exists
         */
        this.create = async (req, res, next) => {
            try {
                const createUserDto = req.body;
                const user = await this.usersService.create(createUserDto);
                logger_1.default.info('User created via API', {
                    userId: user.id,
                    createdBy: req.user?.id,
                    email: user.email
                });
                res.status(201).json({
                    success: true,
                    data: user,
                    message: 'User created successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users:
         *   get:
         *     summary: Get all users with filtering and pagination
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: query
         *         name: search
         *         schema:
         *           type: string
         *         description: Search in email or username
         *       - in: query
         *         name: companyId
         *         schema:
         *           type: string
         *           format: uuid
         *         description: Filter by company ID
         *       - in: query
         *         name: role
         *         schema:
         *           type: string
         *         description: Filter by role name
         *       - in: query
         *         name: isEmailVerified
         *         schema:
         *           type: boolean
         *         description: Filter by email verification status
         *       - in: query
         *         name: page
         *         schema:
         *           type: integer
         *           default: 1
         *         description: Page number
         *       - in: query
         *         name: limit
         *         schema:
         *           type: integer
         *           default: 10
         *         description: Number of items per page
         *     responses:
         *       200:
         *         description: Users retrieved successfully
         */
        this.findAll = async (req, res, next) => {
            try {
                const queryDto = req.query;
                const result = await this.usersService.findAll(queryDto);
                res.json({
                    success: true,
                    data: result,
                    message: 'Users retrieved successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users/{id}:
         *   get:
         *     summary: Get user by ID
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *           format: uuid
         *         description: User ID
         *     responses:
         *       200:
         *         description: User retrieved successfully
         *       404:
         *         description: User not found
         */
        this.findById = async (req, res, next) => {
            try {
                const { id } = req.params;
                const user = await this.usersService.findById(id);
                res.json({
                    success: true,
                    data: user,
                    message: 'User retrieved successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users/{id}:
         *   put:
         *     summary: Update user
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *           format: uuid
         *         description: User ID
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/CreateUserDto'
         *     responses:
         *       200:
         *         description: User updated successfully
         *       404:
         *         description: User not found
         *       409:
         *         description: Conflict with existing user
         */
        this.update = async (req, res, next) => {
            try {
                const { id } = req.params;
                const updateUserDto = req.body;
                const user = await this.usersService.update(id, updateUserDto);
                logger_1.default.info('User updated via API', {
                    userId: id,
                    updatedBy: req.user?.id,
                    email: user.email
                });
                res.json({
                    success: true,
                    data: user,
                    message: 'User updated successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users/{id}/password:
         *   put:
         *     summary: Update user password
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *           format: uuid
         *         description: User ID
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             required:
         *               - currentPassword
         *               - newPassword
         *             properties:
         *               currentPassword:
         *                 type: string
         *               newPassword:
         *                 type: string
         *                 minLength: 8
         *     responses:
         *       200:
         *         description: Password updated successfully
         *       400:
         *         description: Invalid current password
         *       404:
         *         description: User not found
         */
        this.updatePassword = async (req, res, next) => {
            try {
                const { id } = req.params;
                const updatePasswordDto = req.body;
                await this.usersService.updatePassword(id, updatePasswordDto);
                logger_1.default.info('User password updated via API', {
                    userId: id,
                    updatedBy: req.user?.id
                });
                res.json({
                    success: true,
                    message: 'Password updated successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users/{id}:
         *   delete:
         *     summary: Delete user
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *           format: uuid
         *         description: User ID
         *     responses:
         *       200:
         *         description: User deleted successfully
         *       400:
         *         description: Cannot delete user with associated data
         *       404:
         *         description: User not found
         */
        this.delete = async (req, res, next) => {
            try {
                const { id } = req.params;
                await this.usersService.delete(id);
                logger_1.default.info('User deleted via API', {
                    userId: id,
                    deletedBy: req.user?.id
                });
                res.json({
                    success: true,
                    message: 'User deleted successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        /**
         * @swagger
         * /api/users/statistics:
         *   get:
         *     summary: Get user statistics
         *     tags: [Users]
         *     security:
         *       - bearerAuth: []
         *     responses:
         *       200:
         *         description: Statistics retrieved successfully
         */
        this.getStatistics = async (req, res, next) => {
            try {
                const statistics = await this.usersService.getStatistics();
                res.json({
                    success: true,
                    data: statistics,
                    message: 'User statistics retrieved successfully',
                    timestamp: new Date().toISOString()
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.usersService = new users_service_1.UsersService();
    }
}
exports.UsersController = UsersController;
//# sourceMappingURL=users.controller.js.map
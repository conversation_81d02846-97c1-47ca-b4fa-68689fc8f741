import { User } from '@prisma/client';
import { CreateUserDto, UpdateUserDto, UpdateUserPasswordDto, UserQueryDto } from './dto/user.dto';
export declare class UsersService {
    private db;
    constructor();
    create(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>>;
    findAll(queryDto: UserQueryDto): Promise<{
        users: Omit<User, 'password'>[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findById(id: string): Promise<Omit<User, 'password'>>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<Omit<User, 'password'>>;
    updatePassword(id: string, updatePasswordDto: UpdateUserPasswordDto): Promise<void>;
    delete(id: string): Promise<void>;
    private assignRoles;
    getStatistics(): Promise<{
        totalUsers: number;
        usersByRole: Array<{
            role: string;
            count: number;
        }>;
        usersByCompany: Array<{
            company: string;
            count: number;
        }>;
        recentlyJoined: Array<{
            email: string;
            username: string | null;
            createdAt: Date;
        }>;
        emailVerificationStats: {
            verified: number;
            unverified: number;
        };
    }>;
}
//# sourceMappingURL=users.service.d.ts.map
import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../../common/middleware/auth';
/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         username:
 *           type: string
 *           nullable: true
 *         isEmailVerified:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           nullable: true
 *     CreateUserDto:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 8
 *           maxLength: 128
 *         username:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         companyId:
 *           type: string
 *           format: uuid
 *         roleNames:
 *           type: array
 *           items:
 *             type: string
 *         isEmailVerified:
 *           type: boolean
 */
export declare class UsersController {
    private usersService;
    constructor();
    /**
     * @swagger
     * /api/users:
     *   post:
     *     summary: Create a new user
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/CreateUserDto'
     *     responses:
     *       201:
     *         description: User created successfully
     *       400:
     *         description: Invalid input data
     *       409:
     *         description: User already exists
     */
    create: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users:
     *   get:
     *     summary: Get all users with filtering and pagination
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: query
     *         name: search
     *         schema:
     *           type: string
     *         description: Search in email or username
     *       - in: query
     *         name: companyId
     *         schema:
     *           type: string
     *           format: uuid
     *         description: Filter by company ID
     *       - in: query
     *         name: role
     *         schema:
     *           type: string
     *         description: Filter by role name
     *       - in: query
     *         name: isEmailVerified
     *         schema:
     *           type: boolean
     *         description: Filter by email verification status
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *           default: 1
     *         description: Page number
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *           default: 10
     *         description: Number of items per page
     *     responses:
     *       200:
     *         description: Users retrieved successfully
     */
    findAll: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users/{id}:
     *   get:
     *     summary: Get user by ID
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: User ID
     *     responses:
     *       200:
     *         description: User retrieved successfully
     *       404:
     *         description: User not found
     */
    findById: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users/{id}:
     *   put:
     *     summary: Update user
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: User ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/CreateUserDto'
     *     responses:
     *       200:
     *         description: User updated successfully
     *       404:
     *         description: User not found
     *       409:
     *         description: Conflict with existing user
     */
    update: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users/{id}/password:
     *   put:
     *     summary: Update user password
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: User ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - currentPassword
     *               - newPassword
     *             properties:
     *               currentPassword:
     *                 type: string
     *               newPassword:
     *                 type: string
     *                 minLength: 8
     *     responses:
     *       200:
     *         description: Password updated successfully
     *       400:
     *         description: Invalid current password
     *       404:
     *         description: User not found
     */
    updatePassword: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users/{id}:
     *   delete:
     *     summary: Delete user
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: User ID
     *     responses:
     *       200:
     *         description: User deleted successfully
     *       400:
     *         description: Cannot delete user with associated data
     *       404:
     *         description: User not found
     */
    delete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    /**
     * @swagger
     * /api/users/statistics:
     *   get:
     *     summary: Get user statistics
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: Statistics retrieved successfully
     */
    getStatistics: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=users.controller.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const users_controller_1 = require("./users.controller");
const auth_1 = require("../../common/middleware/auth");
const validation_1 = require("../../common/middleware/validation");
const user_dto_1 = require("./dto/user.dto");
const cache_1 = require("../../common/middleware/cache");
const router = (0, express_1.Router)();
const usersController = new users_controller_1.UsersController();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
// Statistics route (before parameterized routes) - with caching
router.get('/statistics', (0, auth_1.requirePermission)('view_analytics'), (0, cache_1.cacheMiddleware)({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `user-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
}), usersController.getStatistics);
// CRUD routes
router.post('/', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validationMiddleware)(user_dto_1.CreateUserDto), (0, cache_1.invalidateCache)('user-statistics'), // Invalidate statistics cache
usersController.create);
router.get('/', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validateQuery)(user_dto_1.UserQueryDto), usersController.findAll);
router.get('/:id', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validateParams)(user_dto_1.UserParamsDto), usersController.findById);
router.put('/:id', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validateParams)(user_dto_1.UserParamsDto), (0, validation_1.validationMiddleware)(user_dto_1.UpdateUserDto), (0, cache_1.invalidateCache)('user-statistics'), // Invalidate statistics cache
usersController.update);
router.put('/:id/password', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validateParams)(user_dto_1.UserParamsDto), (0, validation_1.validationMiddleware)(user_dto_1.UpdateUserPasswordDto), usersController.updatePassword);
router.delete('/:id', (0, auth_1.requirePermission)('manage_users'), (0, validation_1.validateParams)(user_dto_1.UserParamsDto), (0, cache_1.invalidateCache)('user-statistics'), // Invalidate statistics cache
usersController.delete);
exports.default = router;
//# sourceMappingURL=users.routes.js.map
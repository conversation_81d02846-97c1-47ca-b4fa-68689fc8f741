export declare class CreateUserDto {
    email: string;
    password: string;
    username?: string;
    companyId?: string;
    roleNames?: string[];
    isEmailVerified?: boolean;
}
export declare class UpdateUserDto {
    email?: string;
    username?: string;
    companyId?: string;
    roleNames?: string[];
    isEmailVerified?: boolean;
}
export declare class UpdateUserPasswordDto {
    currentPassword: string;
    newPassword: string;
}
export declare class UserParamsDto {
    id: string;
}
export declare class UserQueryDto {
    search?: string;
    companyId?: string;
    role?: string;
    isEmailVerified?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
//# sourceMappingURL=user.dto.d.ts.map
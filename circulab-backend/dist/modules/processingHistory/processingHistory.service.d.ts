import { ProcessingHistory } from '@prisma/client';
import { CreateProcessingHistoryDto, UpdateProcessingHistoryDto, ProcessingHistoryQueryDto, ProcessingEfficiencyQueryDto } from './dto/processingHistory.dto';
export declare class ProcessingHistoryService {
    private db;
    constructor();
    create(createProcessingHistoryDto: CreateProcessingHistoryDto): Promise<ProcessingHistory>;
    findAll(queryDto: ProcessingHistoryQueryDto): Promise<{
        processingHistory: ProcessingHistory[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findById(id: string): Promise<ProcessingHistory>;
    update(id: string, updateProcessingHistoryDto: UpdateProcessingHistoryDto): Promise<ProcessingHistory>;
    delete(id: string): Promise<void>;
    getStatistics(): Promise<{
        totalProcessed: number;
        totalQuantityProcessed: number;
        processingByMethod: Array<{
            method: string;
            count: number;
            totalQuantity: number;
        }>;
        processingByCompany: Array<{
            company: string;
            count: number;
            totalQuantity: number;
        }>;
        processingTrends: Array<{
            month: string;
            count: number;
            quantity: number;
        }>;
        environmentalImpact: {
            totalCO2Saved: number;
            totalEnergySaved: number;
            totalWaterSaved: number;
        };
    }>;
    getProcessingEfficiency(queryDto: ProcessingEfficiencyQueryDto): Promise<{
        averageEfficiency: number;
        efficiencyByMethod: Array<{
            method: string;
            efficiency: number;
            processedCount: number;
        }>;
        efficiencyTrends: Array<{
            month: string;
            efficiency: number;
        }>;
    }>;
}
//# sourceMappingURL=processingHistory.service.d.ts.map
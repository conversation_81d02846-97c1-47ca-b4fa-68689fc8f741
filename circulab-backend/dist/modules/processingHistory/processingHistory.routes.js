"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const processingHistory_controller_1 = require("./processingHistory.controller");
const auth_1 = require("../../common/middleware/auth");
const validation_1 = require("../../common/middleware/validation");
const processingHistory_dto_1 = require("./dto/processingHistory.dto");
const cache_1 = require("../../common/middleware/cache");
const router = (0, express_1.Router)();
const processingHistoryController = new processingHistory_controller_1.ProcessingHistoryController();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
// Statistics route (before parameterized routes) - with caching
router.get('/statistics', (0, auth_1.requirePermission)('view_analytics'), (0, cache_1.cacheMiddleware)({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `processing-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
}), processingHistoryController.getStatistics);
// Efficiency route - with caching
router.get('/efficiency', (0, auth_1.requirePermission)('view_analytics'), (0, validation_1.validateQuery)(processingHistory_dto_1.ProcessingEfficiencyQueryDto), (0, cache_1.cacheMiddleware)({
    ttl: 600, // Cache for 10 minutes
    keyGenerator: (req) => {
        const query = req.query;
        return `processing-efficiency:${query.companyId || 'all'}:${query.treatmentMethodId || 'all'}:${query.dateFrom || ''}:${query.dateTo || ''}`;
    },
}), processingHistoryController.getProcessingEfficiency);
// CRUD routes
router.post('/', (0, auth_1.requirePermission)('process_waste'), (0, validation_1.validationMiddleware)(processingHistory_dto_1.CreateProcessingHistoryDto), (0, cache_1.invalidateCache)('processing-statistics'), // Invalidate cache
processingHistoryController.create);
router.get('/', (0, auth_1.requirePermission)('read_waste'), (0, validation_1.validateQuery)(processingHistory_dto_1.ProcessingHistoryQueryDto), processingHistoryController.findAll);
router.get('/:id', (0, auth_1.requirePermission)('read_waste'), (0, validation_1.validateParams)(processingHistory_dto_1.ProcessingHistoryParamsDto), processingHistoryController.findById);
router.put('/:id', (0, auth_1.requirePermission)('process_waste'), (0, validation_1.validateParams)(processingHistory_dto_1.ProcessingHistoryParamsDto), (0, validation_1.validationMiddleware)(processingHistory_dto_1.UpdateProcessingHistoryDto), (0, cache_1.invalidateCache)('processing-statistics'), // Invalidate cache
processingHistoryController.update);
router.delete('/:id', (0, auth_1.requirePermission)('process_waste'), (0, validation_1.validateParams)(processingHistory_dto_1.ProcessingHistoryParamsDto), (0, cache_1.invalidateCache)('processing-statistics'), // Invalidate cache
processingHistoryController.delete);
exports.default = router;
//# sourceMappingURL=processingHistory.routes.js.map
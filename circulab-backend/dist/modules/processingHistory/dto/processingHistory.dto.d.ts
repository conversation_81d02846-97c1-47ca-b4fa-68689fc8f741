export declare class CreateProcessingHistoryDto {
    wasteMaterialId: string;
    processedByUserId?: string;
    processorCompanyId: string;
    treatmentMethodId: string;
    dateProcessed: string;
    inputQuantity?: number;
    outputQuantity?: number;
    outputMaterialType?: string;
    environmentalImpactAchieved?: any;
    notes?: string;
}
export declare class UpdateProcessingHistoryDto {
    processedByUserId?: string;
    treatmentMethodId?: string;
    dateProcessed?: string;
    inputQuantity?: number;
    outputQuantity?: number;
    outputMaterialType?: string;
    environmentalImpactAchieved?: any;
    notes?: string;
}
export declare class ProcessingHistoryParamsDto {
    id: string;
}
export declare class ProcessingHistoryQueryDto {
    wasteMaterialId?: string;
    processedByUserId?: string;
    processorCompanyId?: string;
    treatmentMethodId?: string;
    wasteType?: string;
    treatmentMethod?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ProcessingEfficiencyQueryDto {
    companyId?: string;
    treatmentMethodId?: string;
    dateFrom?: string;
    dateTo?: string;
}
//# sourceMappingURL=processingHistory.dto.d.ts.map
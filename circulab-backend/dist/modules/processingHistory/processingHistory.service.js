"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingHistoryService = void 0;
const client_1 = require("@prisma/client");
const database_1 = __importDefault(require("../../config/database"));
const errorHandler_1 = require("../../common/middleware/errorHandler");
const logger_1 = __importDefault(require("../../config/logger"));
class ProcessingHistoryService {
    constructor() {
        this.db = database_1.default.getInstance();
    }
    async create(createProcessingHistoryDto) {
        try {
            // Validate waste material exists and is available for processing
            const wasteMaterial = await this.db.prisma.wasteMaterial.findUnique({
                where: { id: createProcessingHistoryDto.wasteMaterialId }
            });
            if (!wasteMaterial) {
                throw new errorHandler_1.AppError('Waste material not found', 404);
            }
            if (wasteMaterial.currentStatus === client_1.WasteStatus.VALORIZED || wasteMaterial.currentStatus === client_1.WasteStatus.DISPOSED) {
                throw new errorHandler_1.AppError('Waste material has already been processed', 400);
            }
            // Validate processor company exists
            const processorCompany = await this.db.prisma.company.findUnique({
                where: { id: createProcessingHistoryDto.processorCompanyId }
            });
            if (!processorCompany) {
                throw new errorHandler_1.AppError('Processor company not found', 404);
            }
            // Validate treatment method exists
            const treatmentMethod = await this.db.prisma.treatmentMethod.findUnique({
                where: { id: createProcessingHistoryDto.treatmentMethodId }
            });
            if (!treatmentMethod) {
                throw new errorHandler_1.AppError('Treatment method not found', 404);
            }
            // Validate user exists if provided
            if (createProcessingHistoryDto.processedByUserId) {
                const user = await this.db.prisma.user.findUnique({
                    where: { id: createProcessingHistoryDto.processedByUserId }
                });
                if (!user) {
                    throw new errorHandler_1.AppError('Processing user not found', 404);
                }
            }
            // Create processing history record
            const processingHistory = await this.db.prisma.processingHistory.create({
                data: {
                    ...createProcessingHistoryDto,
                    dateProcessed: new Date(createProcessingHistoryDto.dateProcessed)
                },
                include: {
                    wasteMaterial: {
                        select: {
                            id: true,
                            type: true,
                            quantity: true,
                            unit: true,
                            currentStatus: true
                        }
                    },
                    processedByUser: {
                        select: {
                            id: true,
                            email: true,
                            username: true
                        }
                    },
                    processorCompany: {
                        select: {
                            id: true,
                            name: true,
                            industryType: true
                        }
                    },
                    treatmentMethod: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            technologyType: true
                        }
                    }
                }
            });
            // Update waste material status to VALORIZED
            await this.db.prisma.wasteMaterial.update({
                where: { id: createProcessingHistoryDto.wasteMaterialId },
                data: { currentStatus: client_1.WasteStatus.VALORIZED }
            });
            logger_1.default.info('Processing history created successfully', {
                processingHistoryId: processingHistory.id,
                wasteMaterialId: createProcessingHistoryDto.wasteMaterialId,
                processorCompanyId: createProcessingHistoryDto.processorCompanyId
            });
            return processingHistory;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error creating processing history', { error, data: createProcessingHistoryDto });
            throw new errorHandler_1.AppError('Failed to create processing history', 500);
        }
    }
    async findAll(queryDto) {
        try {
            const { wasteMaterialId, processedByUserId, processorCompanyId, treatmentMethodId, wasteType, treatmentMethod, dateFrom, dateTo, page = 1, limit = 10, sortBy = 'dateProcessed', sortOrder = 'desc' } = queryDto;
            const where = {};
            if (wasteMaterialId) {
                where.wasteMaterialId = wasteMaterialId;
            }
            if (processedByUserId) {
                where.processedByUserId = processedByUserId;
            }
            if (processorCompanyId) {
                where.processorCompanyId = processorCompanyId;
            }
            if (treatmentMethodId) {
                where.treatmentMethodId = treatmentMethodId;
            }
            if (wasteType) {
                where.wasteMaterial = {
                    type: { contains: wasteType }
                };
            }
            if (treatmentMethod) {
                where.treatmentMethod = {
                    name: { contains: treatmentMethod }
                };
            }
            if (dateFrom || dateTo) {
                where.dateProcessed = {};
                if (dateFrom) {
                    where.dateProcessed.gte = new Date(dateFrom);
                }
                if (dateTo) {
                    where.dateProcessed.lte = new Date(dateTo);
                }
            }
            const [processingHistory, total] = await Promise.all([
                this.db.prisma.processingHistory.findMany({
                    where,
                    include: {
                        wasteMaterial: {
                            select: {
                                id: true,
                                type: true,
                                quantity: true,
                                unit: true,
                                source: true,
                                producerCompany: {
                                    select: {
                                        id: true,
                                        name: true
                                    }
                                }
                            }
                        },
                        processedByUser: {
                            select: {
                                id: true,
                                email: true,
                                username: true
                            }
                        },
                        processorCompany: {
                            select: {
                                id: true,
                                name: true,
                                industryType: true
                            }
                        },
                        treatmentMethod: {
                            select: {
                                id: true,
                                name: true,
                                description: true,
                                technologyType: true
                            }
                        }
                    },
                    orderBy: { [sortBy]: sortOrder },
                    skip: (page - 1) * limit,
                    take: limit
                }),
                this.db.prisma.processingHistory.count({ where })
            ]);
            return {
                processingHistory,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            logger_1.default.error('Error fetching processing history', { error, query: queryDto });
            throw new errorHandler_1.AppError('Failed to fetch processing history', 500);
        }
    }
    async findById(id) {
        try {
            const processingHistory = await this.db.prisma.processingHistory.findUnique({
                where: { id },
                include: {
                    wasteMaterial: {
                        include: {
                            producerCompany: {
                                select: {
                                    id: true,
                                    name: true,
                                    industryType: true,
                                    contactPerson: true,
                                    contactEmail: true
                                }
                            },
                            producerUser: {
                                select: {
                                    id: true,
                                    email: true,
                                    username: true
                                }
                            }
                        }
                    },
                    processedByUser: {
                        select: {
                            id: true,
                            email: true,
                            username: true
                        }
                    },
                    processorCompany: {
                        select: {
                            id: true,
                            name: true,
                            industryType: true,
                            contactPerson: true,
                            contactEmail: true
                        }
                    },
                    treatmentMethod: true
                }
            });
            if (!processingHistory) {
                throw new errorHandler_1.AppError('Processing history not found', 404);
            }
            return processingHistory;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error fetching processing history by ID', { error, processingHistoryId: id });
            throw new errorHandler_1.AppError('Failed to fetch processing history', 500);
        }
    }
    async update(id, updateProcessingHistoryDto) {
        try {
            // Check if processing history exists
            const existingProcessingHistory = await this.db.prisma.processingHistory.findUnique({
                where: { id }
            });
            if (!existingProcessingHistory) {
                throw new errorHandler_1.AppError('Processing history not found', 404);
            }
            // Validate treatment method if being updated
            if (updateProcessingHistoryDto.treatmentMethodId) {
                const treatmentMethod = await this.db.prisma.treatmentMethod.findUnique({
                    where: { id: updateProcessingHistoryDto.treatmentMethodId }
                });
                if (!treatmentMethod) {
                    throw new errorHandler_1.AppError('Treatment method not found', 404);
                }
            }
            // Validate user if being updated
            if (updateProcessingHistoryDto.processedByUserId) {
                const user = await this.db.prisma.user.findUnique({
                    where: { id: updateProcessingHistoryDto.processedByUserId }
                });
                if (!user) {
                    throw new errorHandler_1.AppError('Processing user not found', 404);
                }
            }
            const updateData = { ...updateProcessingHistoryDto };
            if (updateProcessingHistoryDto.dateProcessed) {
                updateData.dateProcessed = new Date(updateProcessingHistoryDto.dateProcessed);
            }
            const processingHistory = await this.db.prisma.processingHistory.update({
                where: { id },
                data: updateData,
                include: {
                    wasteMaterial: {
                        select: {
                            id: true,
                            type: true,
                            quantity: true,
                            unit: true
                        }
                    },
                    processedByUser: {
                        select: {
                            id: true,
                            email: true,
                            username: true
                        }
                    },
                    processorCompany: {
                        select: {
                            id: true,
                            name: true,
                            industryType: true
                        }
                    },
                    treatmentMethod: {
                        select: {
                            id: true,
                            name: true,
                            description: true
                        }
                    }
                }
            });
            logger_1.default.info('Processing history updated successfully', {
                processingHistoryId: id
            });
            return processingHistory;
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error updating processing history', { error, processingHistoryId: id, data: updateProcessingHistoryDto });
            throw new errorHandler_1.AppError('Failed to update processing history', 500);
        }
    }
    async delete(id) {
        try {
            // Check if processing history exists
            const existingProcessingHistory = await this.db.prisma.processingHistory.findUnique({
                where: { id },
                include: {
                    wasteMaterial: true
                }
            });
            if (!existingProcessingHistory) {
                throw new errorHandler_1.AppError('Processing history not found', 404);
            }
            // Delete processing history
            await this.db.prisma.processingHistory.delete({
                where: { id }
            });
            // Revert waste material status back to AVAILABLE
            await this.db.prisma.wasteMaterial.update({
                where: { id: existingProcessingHistory.wasteMaterialId },
                data: { currentStatus: client_1.WasteStatus.AVAILABLE }
            });
            logger_1.default.info('Processing history deleted successfully', {
                processingHistoryId: id,
                wasteMaterialId: existingProcessingHistory.wasteMaterialId
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            logger_1.default.error('Error deleting processing history', { error, processingHistoryId: id });
            throw new errorHandler_1.AppError('Failed to delete processing history', 500);
        }
    }
    async getStatistics() {
        try {
            const [totalProcessed, processingByMethod, processingByCompany, processingTrends, environmentalImpactData] = await Promise.all([
                this.db.prisma.processingHistory.count(),
                this.db.prisma.processingHistory.groupBy({
                    by: ['treatmentMethodId'],
                    _count: { id: true },
                    _sum: { inputQuantity: true }
                }),
                this.db.prisma.processingHistory.groupBy({
                    by: ['processorCompanyId'],
                    _count: { id: true },
                    _sum: { inputQuantity: true }
                }),
                this.db.prisma.processingHistory.findMany({
                    select: {
                        dateProcessed: true,
                        inputQuantity: true
                    },
                    orderBy: { dateProcessed: 'desc' },
                    take: 365 // Last year
                }),
                this.db.prisma.processingHistory.findMany({
                    select: {
                        environmentalImpactAchieved: true
                    }
                })
            ]);
            // Get method names
            const methodIds = processingByMethod.map(item => item.treatmentMethodId);
            const methods = await this.db.prisma.treatmentMethod.findMany({
                where: { id: { in: methodIds } },
                select: { id: true, name: true }
            });
            const methodMap = new Map(methods.map(m => [m.id, m.name]));
            // Get company names
            const companyIds = processingByCompany.map(item => item.processorCompanyId);
            const companies = await this.db.prisma.company.findMany({
                where: { id: { in: companyIds } },
                select: { id: true, name: true }
            });
            const companyMap = new Map(companies.map(c => [c.id, c.name]));
            // Calculate total quantity processed
            const totalQuantityProcessed = processingByMethod.reduce((sum, item) => sum + (item._sum.inputQuantity || 0), 0);
            // Process trends by month
            const trendMap = new Map();
            processingTrends.forEach(item => {
                const month = item.dateProcessed.toISOString().substring(0, 7); // YYYY-MM
                const existing = trendMap.get(month) || { count: 0, quantity: 0 };
                trendMap.set(month, {
                    count: existing.count + 1,
                    quantity: existing.quantity + (item.inputQuantity || 0)
                });
            });
            const trends = Array.from(trendMap.entries())
                .map(([month, data]) => ({ month, ...data }))
                .sort((a, b) => a.month.localeCompare(b.month))
                .slice(-12); // Last 12 months
            // Calculate environmental impact
            let totalCO2Saved = 0;
            let totalEnergySaved = 0;
            let totalWaterSaved = 0;
            environmentalImpactData
                .filter(item => item.environmentalImpactAchieved !== null)
                .forEach(item => {
                const impact = item.environmentalImpactAchieved;
                if (impact) {
                    totalCO2Saved += impact.co2_reduced_kg || impact.co2_saved_kg || 0;
                    totalEnergySaved += impact.energy_saved_kwh || 0;
                    totalWaterSaved += impact.water_saved_l || 0;
                }
            });
            return {
                totalProcessed,
                totalQuantityProcessed,
                processingByMethod: processingByMethod.map(item => ({
                    method: methodMap.get(item.treatmentMethodId) || 'Unknown',
                    count: item._count.id,
                    totalQuantity: item._sum.inputQuantity || 0
                })),
                processingByCompany: processingByCompany.map(item => ({
                    company: companyMap.get(item.processorCompanyId) || 'Unknown',
                    count: item._count.id,
                    totalQuantity: item._sum.inputQuantity || 0
                })),
                processingTrends: trends,
                environmentalImpact: {
                    totalCO2Saved,
                    totalEnergySaved,
                    totalWaterSaved
                }
            };
        }
        catch (error) {
            logger_1.default.error('Error fetching processing history statistics', { error });
            throw new errorHandler_1.AppError('Failed to fetch processing history statistics', 500);
        }
    }
    async getProcessingEfficiency(queryDto) {
        try {
            const { companyId, treatmentMethodId, dateFrom, dateTo } = queryDto;
            const where = {
                AND: [
                    { inputQuantity: { not: null } },
                    { outputQuantity: { not: null } },
                    { inputQuantity: { gt: 0 } }
                ]
            };
            if (companyId) {
                where.processorCompanyId = companyId;
            }
            if (treatmentMethodId) {
                where.treatmentMethodId = treatmentMethodId;
            }
            if (dateFrom || dateTo) {
                where.dateProcessed = {};
                if (dateFrom) {
                    where.dateProcessed.gte = new Date(dateFrom);
                }
                if (dateTo) {
                    where.dateProcessed.lte = new Date(dateTo);
                }
            }
            const processingData = await this.db.prisma.processingHistory.findMany({
                where,
                select: {
                    inputQuantity: true,
                    outputQuantity: true,
                    dateProcessed: true,
                    treatmentMethod: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                }
            });
            if (processingData.length === 0) {
                return {
                    averageEfficiency: 0,
                    efficiencyByMethod: [],
                    efficiencyTrends: []
                };
            }
            // Calculate overall efficiency
            const totalInput = processingData.reduce((sum, item) => sum + (item.inputQuantity || 0), 0);
            const totalOutput = processingData.reduce((sum, item) => sum + (item.outputQuantity || 0), 0);
            const averageEfficiency = totalInput > 0 ? (totalOutput / totalInput) * 100 : 0;
            // Calculate efficiency by method
            const methodMap = new Map();
            processingData.forEach(item => {
                const methodName = item.treatmentMethod.name;
                const existing = methodMap.get(methodName) || { input: 0, output: 0, count: 0 };
                methodMap.set(methodName, {
                    input: existing.input + (item.inputQuantity || 0),
                    output: existing.output + (item.outputQuantity || 0),
                    count: existing.count + 1
                });
            });
            const efficiencyByMethod = Array.from(methodMap.entries()).map(([method, data]) => ({
                method,
                efficiency: data.input > 0 ? (data.output / data.input) * 100 : 0,
                processedCount: data.count
            }));
            // Calculate efficiency trends by month
            const trendMap = new Map();
            processingData.forEach(item => {
                const month = item.dateProcessed.toISOString().substring(0, 7); // YYYY-MM
                const existing = trendMap.get(month) || { input: 0, output: 0 };
                trendMap.set(month, {
                    input: existing.input + (item.inputQuantity || 0),
                    output: existing.output + (item.outputQuantity || 0)
                });
            });
            const efficiencyTrends = Array.from(trendMap.entries())
                .map(([month, data]) => ({
                month,
                efficiency: data.input > 0 ? (data.output / data.input) * 100 : 0
            }))
                .sort((a, b) => a.month.localeCompare(b.month));
            return {
                averageEfficiency,
                efficiencyByMethod,
                efficiencyTrends
            };
        }
        catch (error) {
            logger_1.default.error('Error calculating processing efficiency', { error, query: queryDto });
            throw new errorHandler_1.AppError('Failed to calculate processing efficiency', 500);
        }
    }
}
exports.ProcessingHistoryService = ProcessingHistoryService;
//# sourceMappingURL=processingHistory.service.js.map
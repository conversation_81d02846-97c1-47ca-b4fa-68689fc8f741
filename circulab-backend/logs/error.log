{
  service: 'circulab-backend',
  error: {
    message: 'Cannot set property query of #<IncomingMessage> which has only a getter',
    statusCode: 500,
    stack: 'TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/dist/common/middleware/validation.js:58:23',
    url: '/api/treatment-methods',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 10:18:18'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /favicon.ico not found',
    statusCode: 404,
    stack: 'Error: Route /favicon.ico not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/dist/common/middleware/errorHandler.js:49:19)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/dist/common/middleware/logging.js:74:5)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/favicon.ico',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 11:00:45'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 16:57:56'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:01:08'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:01:43'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:02:41'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /materials not found',
    statusCode: 404,
    stack: 'Error: Route /materials not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/materials',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:02:41'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /analytics not found',
    statusCode: 404,
    stack: 'Error: Route /analytics not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/analytics',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:02:41'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:06:38'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /materials not found',
    statusCode: 404,
    stack: 'Error: Route /materials not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/materials',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:06:38'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /analytics not found',
    statusCode: 404,
    stack: 'Error: Route /analytics not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/analytics',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:06:38'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:06:38'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /favicon.ico not found',
    statusCode: 404,
    stack: 'Error: Route /favicon.ico not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/favicon.ico',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:08:51'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /register not found',
    statusCode: 404,
    stack: 'Error: Route /register not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/register',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:12:58'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:17:01'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:17:45'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:20:47'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at SendStream.error (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/serve-static/index.js:120:7)\n' +
      '    at SendStream.emit (node:events:518:28)\n' +
      '    at SendStream.emit (node:domain:489:12)\n' +
      '    at SendStream.error (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/send/index.js:168:17)',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:21:18'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:22:07'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:22:33'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:24:06'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at SendStream.error (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/serve-static/index.js:120:7)\n' +
      '    at SendStream.emit (node:events:518:28)\n' +
      '    at SendStream.emit (node:domain:489:12)\n' +
      '    at SendStream.error (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/send/index.js:168:17)',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:24:27'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:25:10'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:26:38'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /dashboard not found',
    statusCode: 404,
    stack: 'Error: Route /dashboard not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at performanceLogger (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/logging.ts:87:3)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/dashboard',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 17:26:56'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:28:11'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:29:16'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:31:30'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:32:36'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:34:43'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:36:15'
}
{
  service: 'circulab-backend',
  message: 'Error details: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  level: 'error',
  timestamp: '2025-05-25 17:36:15'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:36:45'
}
{
  service: 'circulab-backend',
  message: 'Error details: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:153:13)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:171:21)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:188:32)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:195:24)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:213:26)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:285:23)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:320:18)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/src/index.ts:503:40\n' +
    '    at Array.map (<anonymous>)',
  level: 'error',
  timestamp: '2025-05-25 17:36:45'
}
{
  service: 'circulab-backend',
  level: 'error',
  message: 'Error setting up frontend serving: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  timestamp: '2025-05-25 17:37:37'
}
{
  service: 'circulab-backend',
  message: 'Error details: Missing parameter name at 1: https://git.new/pathToRegexpError',
  stack: 'TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n' +
    '    at name (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:73:19)\n' +
    '    at lexer (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:91:27)\n' +
    '    at lexer.next (<anonymous>)\n' +
    '    at Iter.peek (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:106:38)\n' +
    '    at Iter.tryConsume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:112:28)\n' +
    '    at Iter.text (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:128:30)\n' +
    '    at consume (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:152:29)\n' +
    '    at parse (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:183:20)\n' +
    '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/path-to-regexp/dist/index.js:294:74\n' +
    '    at Array.map (<anonymous>)',
  level: 'error',
  timestamp: '2025-05-25 17:37:37'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Access token required',
    statusCode: 401,
    stack: 'Error: Access token required\n' +
      '    at authenticateToken (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/auth.ts:36:13)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at Function.handle (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:186:3)\n' +
      '    at router (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:60:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)',
    url: '/api/auth/profile',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 18:18:49'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Invalid token',
    statusCode: 401,
    stack: 'Error: Invalid token\n' +
      '    at authenticateToken (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/auth.ts:94:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at Function.handle (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:186:3)\n' +
      '    at router (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:60:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)',
    url: '/api/waste-materials/statistics',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 19:31:44'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /api/health not found',
    statusCode: 404,
    stack: 'Error: Route /api/health not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/src/app.ts:305:18\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechets/circulab-backend/node_modules/router/index.js:297:9',
    url: '/api/health',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-05-25 22:13:19'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Access token required',
    statusCode: 401,
    stack: 'Error: Access token required\n' +
      '    at authenticateToken (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/common/middleware/auth.ts:36:13)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at Function.handle (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:186:3)\n' +
      '    at router (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:60:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)',
    url: '/api/companies',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-06-05 20:05:47'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /api/health not found',
    statusCode: 404,
    stack: 'Error: Route /api/health not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/app.ts:313:18\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9',
    url: '/api/health',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-06-05 20:40:20'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Access token required',
    statusCode: 401,
    stack: 'Error: Access token required\n' +
      '    at authenticateToken (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/common/middleware/auth.ts:36:13)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at Function.handle (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:186:3)\n' +
      '    at router (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:60:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)',
    url: '/api/waste-materials',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-06-05 20:40:30'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Route /api/health not found',
    statusCode: 404,
    stack: 'Error: Route /api/health not found\n' +
      '    at notFoundHandler (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/common/middleware/errorHandler.ts:61:17)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/app.ts:313:18\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9',
    url: '/api/health',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-06-05 22:21:48'
}
{
  service: 'circulab-backend',
  error: {
    message: 'Access token required',
    statusCode: 401,
    stack: 'Error: Access token required\n' +
      '    at authenticateToken (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/src/common/middleware/auth.ts:36:13)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)\n' +
      '    at /Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:297:9\n' +
      '    at processParams (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:582:12)\n' +
      '    at next (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:291:5)\n' +
      '    at Function.handle (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:186:3)\n' +
      '    at router (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:60:12)\n' +
      '    at Layer.handleRequest (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/lib/layer.js:152:17)\n' +
      '    at trimPrefix (/Users/<USER>/Desktop/CircuLab-Apps-Dechet/circulab-backend/node_modules/router/index.js:342:13)',
    url: '/api/auth/health',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1'
  },
  level: 'error',
  message: 'API Error',
  timestamp: '2025-06-05 22:21:57'
}

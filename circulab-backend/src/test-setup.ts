import 'reflect-metadata';
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'file:./test.db';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

let prisma: PrismaClient;

// Global test setup
beforeAll(async () => {
  // Remove existing test database
  const testDbPath = path.join(process.cwd(), 'test.db');
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  // Initialize Prisma client
  prisma = new PrismaClient();

  // Push schema to test database
  try {
    execSync('npx prisma db push --force-reset', {
      env: { ...process.env, DATABASE_URL: 'file:./test.db' },
      stdio: 'pipe'
    });
  } catch (error) {
    console.error('Failed to setup test database:', error);
    throw error;
  }

  // Connect to database
  await prisma.$connect();
});

afterAll(async () => {
  // Cleanup after all tests
  if (prisma) {
    await prisma.$disconnect();
  }

  // Remove test database
  const testDbPath = path.join(process.cwd(), 'test.db');
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }
});

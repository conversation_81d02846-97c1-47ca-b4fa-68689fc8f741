import 'reflect-metadata';
import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import path from 'path';
import fs from 'fs';

// Import configurations
import env from './config/env';
import logger from './config/logger';
import DatabaseService from './config/database';

// Import middleware
import { errorHandler, notFoundHandler } from './common/middleware/errorHandler';
import { requestLogger, performanceLogger } from './common/middleware/logging';

// Import routes
import authRoutes from './modules/auth/auth.routes';
import wasteMaterialsRoutes from './modules/wasteMaterials/wasteMaterials.routes';
import treatmentMethodsRoutes from './modules/treatmentMethods/treatmentMethods.routes';
import companiesRoutes from './modules/companies/companies.routes';
import usersRoutes from './modules/users/users.routes';

class App {
  public app: Application;
  private db: DatabaseService;

  constructor() {
    this.app = express();
    this.db = DatabaseService.getInstance();

    this.initializeMiddleware();
    this.initializeSwagger();
    this.initializeRoutes();
    this.initializeFrontendServing();
    // Error handling must be last to catch all unhandled routes
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    this.app.use(requestLogger);
    this.app.use(performanceLogger);

    // Health check endpoint
    this.app.get('/health', async (_req, res) => {
      const dbHealth = await this.db.healthCheck();

      res.status(dbHealth ? 200 : 503).json({
        status: dbHealth ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: env.NODE_ENV,
        database: dbHealth ? 'connected' : 'disconnected',
      });
    });
  }

  private initializeSwagger(): void {
    const options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'CircuLab API',
          version: '1.0.0',
          description: 'Industrial Waste Valorization Platform API',
          contact: {
            name: 'CircuLab Team',
            email: '<EMAIL>',
          },
        },
        servers: [
          {
            url: `http://localhost:${env.PORT}/api`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
          schemas: {
            User: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                email: { type: 'string', format: 'email' },
                username: { type: 'string' },
                isEmailVerified: { type: 'boolean' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
                lastLoginAt: { type: 'string', format: 'date-time' },
                companyId: { type: 'string', format: 'uuid' },
              },
            },
            Company: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' },
                siret: { type: 'string' },
                address: { type: 'string' },
                industryType: { type: 'string' },
                contactPerson: { type: 'string' },
                contactEmail: { type: 'string', format: 'email' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
              },
            },
            WasteMaterial: {
              type: 'object',
              properties: {
                id: { type: 'string', format: 'uuid' },
                type: { type: 'string' },
                description: { type: 'string' },
                quantity: { type: 'number' },
                unit: { type: 'string' },
                source: { type: 'string' },
                currentStatus: {
                  type: 'string',
                  enum: ['AVAILABLE', 'PENDING_PICKUP', 'IN_TRANSIT', 'PROCESSING', 'VALORIZED', 'DISPOSED', 'AWAITING_ANALYSIS']
                },
                location_address: { type: 'string' },
                location_latitude: { type: 'number' },
                location_longitude: { type: 'number' },
                isHazardous: { type: 'boolean' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
              },
            },
            ApiResponse: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: { type: 'object' },
                message: { type: 'string' },
                timestamp: { type: 'string', format: 'date-time' },
              },
            },
            Error: {
              type: 'object',
              properties: {
                success: { type: 'boolean', example: false },
                error: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                  },
                },
                timestamp: { type: 'string', format: 'date-time' },
                path: { type: 'string' },
                method: { type: 'string' },
              },
            },
          },
        },
      },
      apis: ['./src/modules/**/*.controller.ts', './src/modules/**/*.routes.ts'],
    };

    const specs = swaggerJsdoc(options);
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'CircuLab API Documentation',
    }));

    // Serve OpenAPI spec as JSON
    this.app.get('/api-docs.json', (_req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(specs);
    });
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/waste-materials', wasteMaterialsRoutes);
    this.app.use('/api/treatment-methods', treatmentMethodsRoutes);
    this.app.use('/api/companies', companiesRoutes);
  }

  private initializeFrontendServing(): void {
    // Check if we're in production mode and frontend build exists
    // From dist/server.js, we need to go up to project root, then to frontend
    const projectRoot = path.join(__dirname, '../..');
    const frontendBuildPath = path.join(projectRoot, 'circulab-frontend/.next');
    const frontendStaticPath = path.join(projectRoot, 'circulab-frontend/out');
    const frontendPublicPath = path.join(projectRoot, 'circulab-frontend/public');

    // Debug logging
    logger.info('Frontend paths:', {
      buildPath: frontendBuildPath,
      staticPath: frontendStaticPath,
      publicPath: frontendPublicPath,
      buildExists: fs.existsSync(frontendBuildPath),
      staticExists: fs.existsSync(frontendStaticPath),
      publicExists: fs.existsSync(frontendPublicPath),
    });

    // Check if frontend build exists to determine serving mode
    const hasFrontendBuild = fs.existsSync(frontendBuildPath) || fs.existsSync(frontendStaticPath);

    // Force production mode if frontend build exists, regardless of NODE_ENV
    const shouldServeStatic = hasFrontendBuild || process.env.FORCE_STATIC_SERVING === 'true';

    logger.info('Frontend serving decision:', {
      hasFrontendBuild,
      shouldServeStatic,
      nodeEnv: env.NODE_ENV,
      forceStatic: process.env.FORCE_STATIC_SERVING,
    });

    // In development mode without frontend build, serve API info at root
    if (!shouldServeStatic) {
      logger.info('Serving in development mode - no static frontend');
      this.app.get('/', (_req, res) => {
        res.json({
          message: 'CircuLab API is running!',
          version: '1.0.0',
          documentation: '/api-docs',
          health: '/health',
          frontend: 'http://localhost:3000',
          note: 'In development mode. Frontend runs separately on port 3000.',
        });
      });
      return;
    }

    logger.info('Setting up static frontend serving...');

    // Production mode: serve the Next.js application
    try {
      // Serve specific static assets only (not the entire directory)
      if (fs.existsSync(frontendStaticPath)) {
        // Static export mode - serve specific assets
        this.app.use('/_next', express.static(path.join(frontendStaticPath, '_next')));
        this.app.use('/static', express.static(path.join(frontendStaticPath, 'static')));
        logger.info('Serving Next.js static export assets from:', frontendStaticPath);
      } else if (fs.existsSync(frontendBuildPath)) {
        // Server-side rendering mode
        this.app.use('/_next', express.static(path.join(frontendBuildPath, 'static')));
        this.app.use('/static', express.static(path.join(frontendBuildPath, 'static')));
        logger.info('Serving Next.js build assets from:', frontendBuildPath);
      }

      // Serve specific public assets (favicon, images, etc.) but not index.html
      if (fs.existsSync(frontendPublicPath)) {
        // Serve specific files from public directory
        this.app.get('/favicon.ico', (_req, res) => {
          const faviconPath = path.join(frontendPublicPath, 'favicon.ico');
          if (fs.existsSync(faviconPath)) {
            res.sendFile(faviconPath);
          } else {
            res.status(404).end();
          }
        });

        // Serve other static assets from public (images, etc.)
        this.app.use('/images', express.static(path.join(frontendPublicPath, 'images')));
        this.app.use('/icons', express.static(path.join(frontendPublicPath, 'icons')));

        logger.info('Serving public assets from:', frontendPublicPath);
      }

      // Handle client-side routing - serve index.html for all non-API routes
      // Use a more specific approach instead of '*' which can cause path-to-regexp errors
      this.app.use((req, res, next) => {
        // Skip API routes, health check, and documentation
        if (req.path.startsWith('/api/') ||
            req.path.startsWith('/health') ||
            req.path.startsWith('/api-docs') ||
            req.path.startsWith('/_next') ||
            req.path.startsWith('/static') ||
            req.path.startsWith('/images') ||
            req.path.startsWith('/icons') ||
            req.path === '/favicon.ico') {
          return next();
        }

        // Always serve the frontend for non-API routes
        logger.info(`Serving frontend for route: ${req.path}`);

        // Try to serve the appropriate HTML file from Next.js build
        let indexPath: string | null = null;

        // Check for Next.js static export
        if (fs.existsSync(frontendStaticPath)) {
          const staticIndexPath = path.join(frontendStaticPath, 'index.html');
          if (fs.existsSync(staticIndexPath)) {
            indexPath = staticIndexPath;
          }
        }

        // Check for Next.js standalone build
        if (!indexPath && fs.existsSync(frontendBuildPath)) {
          const buildIndexPath = path.join(frontendBuildPath, 'server/pages/index.html');
          if (fs.existsSync(buildIndexPath)) {
            indexPath = buildIndexPath;
          }

          // Try alternative Next.js build structure
          const altIndexPath = path.join(frontendBuildPath, 'static/index.html');
          if (!indexPath && fs.existsSync(altIndexPath)) {
            indexPath = altIndexPath;
          }
        }

        // Check public directory
        if (!indexPath) {
          const publicIndexPath = path.join(__dirname, '../../circulab-frontend/public/index.html');
          if (fs.existsSync(publicIndexPath)) {
            indexPath = publicIndexPath;
          }
        }

        if (indexPath) {
          res.sendFile(path.resolve(indexPath));
        } else {
          // Fallback: serve a basic HTML page with Next.js app structure
          res.send(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>CircuLab - Waste Valorization Platform</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                  body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; }
                  .container { display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                  .card { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
                  .logo { font-size: 2rem; font-weight: bold; color: #667eea; margin-bottom: 1rem; }
                  .message { color: #666; margin-bottom: 1.5rem; }
                  .button { display: inline-block; background: #667eea; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; margin: 0.5rem; }
                  .button:hover { background: #5a6fd8; }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="card">
                    <div class="logo">🌱 CircuLab</div>
                    <div class="message">
                      <p>Plateforme de Valorisation des Déchets Industriels</p>
                      <p>L'application frontend est en cours de chargement...</p>
                    </div>
                    <a href="/api-docs" class="button">📚 Documentation API</a>
                    <a href="/health" class="button">🔍 État du Serveur</a>
                    <script>
                      // Auto-refresh every 3 seconds to check if frontend is available
                      setTimeout(() => window.location.reload(), 3000);
                    </script>
                  </div>
                </div>
              </body>
            </html>
          `);
        }
      });

    } catch (error) {
      logger.error('Error setting up frontend serving:', error);
      logger.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });

      // Fallback route for production
      this.app.get('/', (_req, res) => {
        res.json({
          message: 'CircuLab API is running!',
          version: '1.0.0',
          documentation: '/api-docs',
          health: '/health',
          error: 'Frontend build not found',
          errorDetails: error instanceof Error ? error.message : 'Unknown error',
        });
      });
    }
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await this.db.connect();

      // Start server
      this.app.listen(env.PORT, () => {
        logger.info(`🚀 CircuLab API server started successfully`, {
          port: env.PORT,
          environment: env.NODE_ENV,
          documentation: `http://localhost:${env.PORT}/api-docs`,
        });
      });
    } catch (error) {
      logger.error('Failed to start server', { error });
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      await this.db.disconnect();
      logger.info('Server stopped successfully');
    } catch (error) {
      logger.error('Error stopping server', { error });
    }
  }
}

export default App;

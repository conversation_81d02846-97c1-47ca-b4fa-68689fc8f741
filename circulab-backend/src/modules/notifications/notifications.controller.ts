import { Request, Response, NextFunction } from 'express';
import { NotificationsService } from './notifications.service';
import { 
  CreateNotificationDto, 
  UpdateNotificationDto, 
  NotificationQueryDto, 
  BulkNotificationDto,
  MarkAllReadDto 
} from './dto/notification.dto';
import { AuthenticatedRequest } from '../../common/middleware/auth';
import logger from '../../config/logger';

/**
 * @swagger
 * components:
 *   schemas:
 *     Notification:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         userId:
 *           type: string
 *           format: uuid
 *         type:
 *           type: string
 *           enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
 *         message:
 *           type: string
 *         isRead:
 *           type: boolean
 *         linkTo:
 *           type: string
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *     CreateNotificationDto:
 *       type: object
 *       required:
 *         - userId
 *         - type
 *         - message
 *       properties:
 *         userId:
 *           type: string
 *           format: uuid
 *         type:
 *           type: string
 *           enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
 *         message:
 *           type: string
 *           maxLength: 500
 *         linkTo:
 *           type: string
 *           maxLength: 200
 */

export class NotificationsController {
  private notificationsService: NotificationsService;

  constructor() {
    this.notificationsService = new NotificationsService();
  }

  /**
   * @swagger
   * /api/notifications:
   *   post:
   *     summary: Create a new notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateNotificationDto'
   *     responses:
   *       201:
   *         description: Notification created successfully
   *       400:
   *         description: Invalid input data
   *       404:
   *         description: User not found
   */
  create = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const createNotificationDto: CreateNotificationDto = req.body;
      const notification = await this.notificationsService.create(createNotificationDto);

      logger.info('Notification created via API', {
        notificationId: notification.id,
        createdBy: req.user?.id,
        targetUserId: notification.userId
      });

      res.status(201).json({
        success: true,
        data: notification,
        message: 'Notification created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/bulk:
   *   post:
   *     summary: Create bulk notifications
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - userIds
   *               - type
   *               - message
   *             properties:
   *               userIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                   format: uuid
   *               type:
   *                 type: string
   *                 enum: [GENERIC_INFO, NEW_WASTE_AVAILABLE, WASTE_STATUS_UPDATED, PROCESSING_COMPLETE, CONTRACT_PROPOSAL, MESSAGE_RECEIVED, SYSTEM_ALERT]
   *               message:
   *                 type: string
   *               linkTo:
   *                 type: string
   *     responses:
   *       201:
   *         description: Bulk notifications created successfully
   */
  createBulk = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const bulkNotificationDto: BulkNotificationDto = req.body;
      const result = await this.notificationsService.createBulk(bulkNotificationDto);

      logger.info('Bulk notifications created via API', {
        createdBy: req.user?.id,
        count: result.count,
        type: bulkNotificationDto.type
      });

      res.status(201).json({
        success: true,
        data: result,
        message: 'Bulk notifications created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications:
   *   get:
   *     summary: Get all notifications with filtering and pagination
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: userId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by user ID
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *         description: Filter by notification type
   *       - in: query
   *         name: isRead
   *         schema:
   *           type: boolean
   *         description: Filter by read status
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: Notifications retrieved successfully
   */
  findAll = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const queryDto: NotificationQueryDto = req.query;
      const result = await this.notificationsService.findAll(queryDto);

      res.json({
        success: true,
        data: result,
        message: 'Notifications retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/user/{userId}:
   *   get:
   *     summary: Get notifications for a specific user
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: User ID
   *     responses:
   *       200:
   *         description: User notifications retrieved successfully
   *       404:
   *         description: User not found
   */
  findByUserId = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      const queryDto: Omit<NotificationQueryDto, 'userId'> = req.query;
      const result = await this.notificationsService.findByUserId(userId, queryDto);

      res.json({
        success: true,
        data: result,
        message: 'User notifications retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/my:
   *   get:
   *     summary: Get current user's notifications
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Current user notifications retrieved successfully
   */
  findMyNotifications = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const queryDto: Omit<NotificationQueryDto, 'userId'> = req.query;
      const result = await this.notificationsService.findByUserId(userId, queryDto);

      res.json({
        success: true,
        data: result,
        message: 'Your notifications retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/{id}:
   *   get:
   *     summary: Get notification by ID
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Notification ID
   *     responses:
   *       200:
   *         description: Notification retrieved successfully
   *       404:
   *         description: Notification not found
   */
  findById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const notification = await this.notificationsService.findById(id);

      res.json({
        success: true,
        data: notification,
        message: 'Notification retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/{id}:
   *   put:
   *     summary: Update notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Notification ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               isRead:
   *                 type: boolean
   *     responses:
   *       200:
   *         description: Notification updated successfully
   *       404:
   *         description: Notification not found
   */
  update = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const updateNotificationDto: UpdateNotificationDto = req.body;
      const notification = await this.notificationsService.update(id, updateNotificationDto);

      logger.info('Notification updated via API', {
        notificationId: id,
        updatedBy: req.user?.id
      });

      res.json({
        success: true,
        data: notification,
        message: 'Notification updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/{id}/read:
   *   put:
   *     summary: Mark notification as read
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Notification ID
   *     responses:
   *       200:
   *         description: Notification marked as read
   *       404:
   *         description: Notification not found
   */
  markAsRead = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const notification = await this.notificationsService.markAsRead(id);

      res.json({
        success: true,
        data: notification,
        message: 'Notification marked as read',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/mark-all-read:
   *   put:
   *     summary: Mark all notifications as read for a user
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - userId
   *             properties:
   *               userId:
   *                 type: string
   *                 format: uuid
   *               type:
   *                 type: string
   *     responses:
   *       200:
   *         description: Notifications marked as read
   */
  markAllAsRead = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const markAllReadDto: MarkAllReadDto = req.body;
      const result = await this.notificationsService.markAllAsRead(markAllReadDto);

      logger.info('All notifications marked as read via API', {
        userId: markAllReadDto.userId,
        updatedBy: req.user?.id,
        count: result.count
      });

      res.json({
        success: true,
        data: result,
        message: 'All notifications marked as read',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/{id}:
   *   delete:
   *     summary: Delete notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Notification ID
   *     responses:
   *       200:
   *         description: Notification deleted successfully
   *       404:
   *         description: Notification not found
   */
  delete = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      await this.notificationsService.delete(id);

      logger.info('Notification deleted via API', {
        notificationId: id,
        deletedBy: req.user?.id
      });

      res.json({
        success: true,
        message: 'Notification deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/notifications/statistics:
   *   get:
   *     summary: Get notification statistics
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Statistics retrieved successfully
   */
  getStatistics = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const statistics = await this.notificationsService.getStatistics();

      res.json({
        success: true,
        data: statistics,
        message: 'Notification statistics retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };
}

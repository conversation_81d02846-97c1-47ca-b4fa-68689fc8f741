import { IsString, IsOptional, <PERSON>UUI<PERSON>, IsBoolean, IsEnum, Length } from 'class-validator';
import { Transform } from 'class-transformer';
import { NotificationType } from '@prisma/client';

export class CreateNotificationDto {
  @IsUUID()
  userId: string;

  @IsEnum(NotificationType)
  type: NotificationType;

  @IsString()
  @Length(1, 500)
  message: string;

  @IsOptional()
  @IsString()
  @Length(1, 200)
  linkTo?: string;
}

export class UpdateNotificationDto {
  @IsOptional()
  @IsBoolean()
  isRead?: boolean;
}

export class NotificationParamsDto {
  @IsUUID()
  id: string;
}

export class NotificationQueryDto {
  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  isRead?: boolean;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class BulkNotificationDto {
  @IsString({ each: true })
  userIds: string[];

  @IsEnum(NotificationType)
  type: NotificationType;

  @IsString()
  @Length(1, 500)
  message: string;

  @IsOptional()
  @IsString()
  @Length(1, 200)
  linkTo?: string;
}

export class MarkAllReadDto {
  @IsUUID()
  userId: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;
}

import { Router } from 'express';
import { NotificationsController } from './notifications.controller';
import { authenticateToken, requirePermission } from '../../common/middleware/auth';
import { validationMiddleware, validateParams, validateQuery } from '../../common/middleware/validation';
import { 
  CreateNotificationDto, 
  UpdateNotificationDto, 
  NotificationParamsDto, 
  NotificationQueryDto,
  BulkNotificationDto,
  MarkAllReadDto 
} from './dto/notification.dto';
import { cacheMiddleware, invalidateCache } from '../../common/middleware/cache';

const router = Router();
const notificationsController = new NotificationsController();

// Apply authentication to all routes
router.use(authenticateToken);

// Statistics route (before parameterized routes) - with caching
router.get(
  '/statistics',
  requirePermission('view_analytics'),
  cacheMiddleware({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `notification-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
  }),
  notificationsController.getStatistics
);

// Special routes
router.get(
  '/my',
  notificationsController.findMyNotifications
);

router.post(
  '/bulk',
  requirePermission('manage_notifications'),
  validationMiddleware(BulkNotificationDto),
  invalidateCache('notification-statistics'),
  notificationsController.createBulk
);

router.put(
  '/mark-all-read',
  validationMiddleware(MarkAllReadDto),
  invalidateCache('notification-statistics'),
  notificationsController.markAllAsRead
);

// User-specific notifications
router.get(
  '/user/:userId',
  requirePermission('manage_notifications'),
  notificationsController.findByUserId
);

// CRUD routes
router.post(
  '/',
  requirePermission('manage_notifications'),
  validationMiddleware(CreateNotificationDto),
  invalidateCache('notification-statistics'),
  notificationsController.create
);

router.get(
  '/',
  requirePermission('manage_notifications'),
  validateQuery(NotificationQueryDto),
  notificationsController.findAll
);

router.get(
  '/:id',
  validateParams(NotificationParamsDto),
  notificationsController.findById
);

router.put(
  '/:id',
  validateParams(NotificationParamsDto),
  validationMiddleware(UpdateNotificationDto),
  invalidateCache('notification-statistics'),
  notificationsController.update
);

router.put(
  '/:id/read',
  validateParams(NotificationParamsDto),
  invalidateCache('notification-statistics'),
  notificationsController.markAsRead
);

router.delete(
  '/:id',
  validateParams(NotificationParamsDto),
  invalidateCache('notification-statistics'),
  notificationsController.delete
);

export default router;

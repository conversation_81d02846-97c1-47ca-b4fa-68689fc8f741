import { AuthService } from './auth.service';
import DatabaseService from '../../config/database';
import { AppError } from '../../common/middleware/errorHandler';

describe('AuthService', () => {
  let authService: AuthService;
  let db: DatabaseService;

  beforeAll(async () => {
    db = DatabaseService.getInstance();
    await db.connect();
    authService = new AuthService();
  });

  afterAll(async () => {
    await db.disconnect();
  });

  beforeEach(async () => {
    // Clean up database before each test in correct order (respecting foreign keys)
    try {
      await db.prisma.userRole.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.rolePermission.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.processingHistory.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.wasteMaterial.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.notification.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.user.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.company.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.permission.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.role.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
    try {
      await db.prisma.treatmentMethod.deleteMany();
    } catch (error) {
      // Table might not exist yet, ignore
    }
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser',
      };

      const result = await authService.register(registerDto);

      expect(result.user.email).toBe(registerDto.email);
      expect(result.user.username).toBe(registerDto.username);
      expect(result.tokens.accessToken).toBeDefined();
      expect(result.tokens.refreshToken).toBeDefined();
      expect(result.user).not.toHaveProperty('password');
    });

    it('should throw error if user already exists', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      // Register user first time
      await authService.register(registerDto);

      // Try to register same user again
      await expect(authService.register(registerDto)).rejects.toThrow(AppError);
    });
  });

  describe('login', () => {
    beforeEach(async () => {
      // Create a test user
      await authService.register({
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser',
      });
    });

    it('should login user with correct credentials', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await authService.login(loginDto);

      expect(result.user.email).toBe(loginDto.email);
      expect(result.tokens.accessToken).toBeDefined();
      expect(result.tokens.refreshToken).toBeDefined();
      expect(result.user).not.toHaveProperty('password');
    });

    it('should throw error with incorrect password', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      await expect(authService.login(loginDto)).rejects.toThrow(AppError);
    });

    it('should throw error with non-existent email', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      await expect(authService.login(loginDto)).rejects.toThrow(AppError);
    });
  });
});

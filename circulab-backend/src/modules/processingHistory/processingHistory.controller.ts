import { Request, Response, NextFunction } from 'express';
import { ProcessingHistoryService } from './processingHistory.service';
import { 
  CreateProcessingHistoryDto, 
  UpdateProcessingHistoryDto, 
  ProcessingHistoryQueryDto,
  ProcessingEfficiencyQueryDto 
} from './dto/processingHistory.dto';
import { AuthenticatedRequest } from '../../common/middleware/auth';
import logger from '../../config/logger';

/**
 * @swagger
 * components:
 *   schemas:
 *     ProcessingHistory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         wasteMaterialId:
 *           type: string
 *           format: uuid
 *         processedByUserId:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         processorCompanyId:
 *           type: string
 *           format: uuid
 *         treatmentMethodId:
 *           type: string
 *           format: uuid
 *         dateProcessed:
 *           type: string
 *           format: date-time
 *         inputQuantity:
 *           type: number
 *           nullable: true
 *         outputQuantity:
 *           type: number
 *           nullable: true
 *         outputMaterialType:
 *           type: string
 *           nullable: true
 *         environmentalImpactAchieved:
 *           type: object
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     CreateProcessingHistoryDto:
 *       type: object
 *       required:
 *         - wasteMaterialId
 *         - processorCompanyId
 *         - treatmentMethodId
 *         - dateProcessed
 *       properties:
 *         wasteMaterialId:
 *           type: string
 *           format: uuid
 *         processedByUserId:
 *           type: string
 *           format: uuid
 *         processorCompanyId:
 *           type: string
 *           format: uuid
 *         treatmentMethodId:
 *           type: string
 *           format: uuid
 *         dateProcessed:
 *           type: string
 *           format: date-time
 *         inputQuantity:
 *           type: number
 *           minimum: 0
 *         outputQuantity:
 *           type: number
 *           minimum: 0
 *         outputMaterialType:
 *           type: string
 *         environmentalImpactAchieved:
 *           type: object
 *         notes:
 *           type: string
 */

export class ProcessingHistoryController {
  private processingHistoryService: ProcessingHistoryService;

  constructor() {
    this.processingHistoryService = new ProcessingHistoryService();
  }

  /**
   * @swagger
   * /api/processing-history:
   *   post:
   *     summary: Create a new processing history record
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateProcessingHistoryDto'
   *     responses:
   *       201:
   *         description: Processing history created successfully
   *       400:
   *         description: Invalid input data or waste already processed
   *       404:
   *         description: Waste material, company, or treatment method not found
   */
  create = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const createProcessingHistoryDto: CreateProcessingHistoryDto = req.body;
      const processingHistory = await this.processingHistoryService.create(createProcessingHistoryDto);

      logger.info('Processing history created via API', {
        processingHistoryId: processingHistory.id,
        createdBy: req.user?.id,
        wasteMaterialId: createProcessingHistoryDto.wasteMaterialId
      });

      res.status(201).json({
        success: true,
        data: processingHistory,
        message: 'Processing history created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history:
   *   get:
   *     summary: Get all processing history records with filtering and pagination
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: wasteMaterialId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by waste material ID
   *       - in: query
   *         name: processedByUserId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by processing user ID
   *       - in: query
   *         name: processorCompanyId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by processor company ID
   *       - in: query
   *         name: treatmentMethodId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by treatment method ID
   *       - in: query
   *         name: wasteType
   *         schema:
   *           type: string
   *         description: Filter by waste type
   *       - in: query
   *         name: treatmentMethod
   *         schema:
   *           type: string
   *         description: Filter by treatment method name
   *       - in: query
   *         name: dateFrom
   *         schema:
   *           type: string
   *           format: date
   *         description: Filter by date from
   *       - in: query
   *         name: dateTo
   *         schema:
   *           type: string
   *           format: date
   *         description: Filter by date to
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: Processing history retrieved successfully
   */
  findAll = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const queryDto: ProcessingHistoryQueryDto = req.query;
      const result = await this.processingHistoryService.findAll(queryDto);

      res.json({
        success: true,
        data: result,
        message: 'Processing history retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history/{id}:
   *   get:
   *     summary: Get processing history by ID
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Processing history ID
   *     responses:
   *       200:
   *         description: Processing history retrieved successfully
   *       404:
   *         description: Processing history not found
   */
  findById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const processingHistory = await this.processingHistoryService.findById(id);

      res.json({
        success: true,
        data: processingHistory,
        message: 'Processing history retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history/{id}:
   *   put:
   *     summary: Update processing history
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Processing history ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateProcessingHistoryDto'
   *     responses:
   *       200:
   *         description: Processing history updated successfully
   *       404:
   *         description: Processing history not found
   */
  update = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const updateProcessingHistoryDto: UpdateProcessingHistoryDto = req.body;
      const processingHistory = await this.processingHistoryService.update(id, updateProcessingHistoryDto);

      logger.info('Processing history updated via API', {
        processingHistoryId: id,
        updatedBy: req.user?.id
      });

      res.json({
        success: true,
        data: processingHistory,
        message: 'Processing history updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history/{id}:
   *   delete:
   *     summary: Delete processing history
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Processing history ID
   *     responses:
   *       200:
   *         description: Processing history deleted successfully
   *       404:
   *         description: Processing history not found
   */
  delete = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      await this.processingHistoryService.delete(id);

      logger.info('Processing history deleted via API', {
        processingHistoryId: id,
        deletedBy: req.user?.id
      });

      res.json({
        success: true,
        message: 'Processing history deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history/statistics:
   *   get:
   *     summary: Get processing history statistics
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Statistics retrieved successfully
   */
  getStatistics = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const statistics = await this.processingHistoryService.getStatistics();

      res.json({
        success: true,
        data: statistics,
        message: 'Processing history statistics retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/processing-history/efficiency:
   *   get:
   *     summary: Get processing efficiency metrics
   *     tags: [Processing History]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: companyId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by company ID
   *       - in: query
   *         name: treatmentMethodId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by treatment method ID
   *       - in: query
   *         name: dateFrom
   *         schema:
   *           type: string
   *           format: date
   *         description: Filter by date from
   *       - in: query
   *         name: dateTo
   *         schema:
   *           type: string
   *           format: date
   *         description: Filter by date to
   *     responses:
   *       200:
   *         description: Efficiency metrics retrieved successfully
   */
  getProcessingEfficiency = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const queryDto: ProcessingEfficiencyQueryDto = req.query;
      const efficiency = await this.processingHistoryService.getProcessingEfficiency(queryDto);

      res.json({
        success: true,
        data: efficiency,
        message: 'Processing efficiency metrics retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };
}

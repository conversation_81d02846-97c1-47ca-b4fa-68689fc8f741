import { IsString, IsOptional, IsUUID, <PERSON>N<PERSON>ber, IsDateString, IsObject, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateProcessingHistoryDto {
  @IsUUID()
  wasteMaterialId: string;

  @IsOptional()
  @IsUUID()
  processedByUserId?: string;

  @IsUUID()
  processorCompanyId: string;

  @IsUUID()
  treatmentMethodId: string;

  @IsDateString()
  dateProcessed: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  inputQuantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  outputQuantity?: number;

  @IsOptional()
  @IsString()
  outputMaterialType?: string;

  @IsOptional()
  @IsObject()
  environmentalImpactAchieved?: any; // JSON object

  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateProcessingHistoryDto {
  @IsOptional()
  @IsUUID()
  processedByUserId?: string;

  @IsOptional()
  @IsUUID()
  treatmentMethodId?: string;

  @IsOptional()
  @IsDateString()
  dateProcessed?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  inputQuantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  outputQuantity?: number;

  @IsOptional()
  @IsString()
  outputMaterialType?: string;

  @IsOptional()
  @IsObject()
  environmentalImpactAchieved?: any; // JSON object

  @IsOptional()
  @IsString()
  notes?: string;
}

export class ProcessingHistoryParamsDto {
  @IsUUID()
  id: string;
}

export class ProcessingHistoryQueryDto {
  @IsOptional()
  @IsUUID()
  wasteMaterialId?: string;

  @IsOptional()
  @IsUUID()
  processedByUserId?: string;

  @IsOptional()
  @IsUUID()
  processorCompanyId?: string;

  @IsOptional()
  @IsUUID()
  treatmentMethodId?: string;

  @IsOptional()
  @IsString()
  wasteType?: string;

  @IsOptional()
  @IsString()
  treatmentMethod?: string;

  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'dateProcessed';

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ProcessingEfficiencyQueryDto {
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @IsOptional()
  @IsUUID()
  treatmentMethodId?: string;

  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @IsOptional()
  @IsDateString()
  dateTo?: string;
}

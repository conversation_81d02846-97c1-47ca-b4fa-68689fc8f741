import { Router } from 'express';
import { ProcessingHistoryController } from './processingHistory.controller';
import { authenticateToken, requirePermission } from '../../common/middleware/auth';
import { validationMiddleware, validateParams, validateQuery } from '../../common/middleware/validation';
import { 
  CreateProcessingHistoryDto, 
  UpdateProcessingHistoryDto, 
  ProcessingHistoryParamsDto, 
  ProcessingHistoryQueryDto,
  ProcessingEfficiencyQueryDto 
} from './dto/processingHistory.dto';
import { cacheMiddleware, invalidateCache } from '../../common/middleware/cache';

const router = Router();
const processingHistoryController = new ProcessingHistoryController();

// Apply authentication to all routes
router.use(authenticateToken);

// Statistics route (before parameterized routes) - with caching
router.get(
  '/statistics',
  requirePermission('view_analytics'),
  cacheMiddleware({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `processing-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
  }),
  processingHistoryController.getStatistics
);

// Efficiency route - with caching
router.get(
  '/efficiency',
  requirePermission('view_analytics'),
  validateQuery(ProcessingEfficiencyQueryDto),
  cacheMiddleware({
    ttl: 600, // Cache for 10 minutes
    keyGenerator: (req) => {
      const query = req.query as ProcessingEfficiencyQueryDto;
      return `processing-efficiency:${query.companyId || 'all'}:${query.treatmentMethodId || 'all'}:${query.dateFrom || ''}:${query.dateTo || ''}`;
    },
  }),
  processingHistoryController.getProcessingEfficiency
);

// CRUD routes
router.post(
  '/',
  requirePermission('process_waste'),
  validationMiddleware(CreateProcessingHistoryDto),
  invalidateCache('processing-statistics'), // Invalidate cache
  processingHistoryController.create
);

router.get(
  '/',
  requirePermission('read_waste'),
  validateQuery(ProcessingHistoryQueryDto),
  processingHistoryController.findAll
);

router.get(
  '/:id',
  requirePermission('read_waste'),
  validateParams(ProcessingHistoryParamsDto),
  processingHistoryController.findById
);

router.put(
  '/:id',
  requirePermission('process_waste'),
  validateParams(ProcessingHistoryParamsDto),
  validationMiddleware(UpdateProcessingHistoryDto),
  invalidateCache('processing-statistics'), // Invalidate cache
  processingHistoryController.update
);

router.delete(
  '/:id',
  requirePermission('process_waste'),
  validateParams(ProcessingHistoryParamsDto),
  invalidateCache('processing-statistics'), // Invalidate cache
  processingHistoryController.delete
);

export default router;

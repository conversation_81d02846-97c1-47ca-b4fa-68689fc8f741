import { Request, Response, NextFunction } from 'express';
import { CompaniesService } from './companies.service';
import { CreateCompanyDto, UpdateCompanyDto, CompanyQueryDto } from './dto/company.dto';
import { AuthenticatedRequest } from '../../common/middleware/auth';
import logger from '../../config/logger';

/**
 * @swagger
 * components:
 *   schemas:
 *     Company:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         siret:
 *           type: string
 *           nullable: true
 *         address:
 *           type: string
 *           nullable: true
 *         industryType:
 *           type: string
 *           nullable: true
 *         contactPerson:
 *           type: string
 *           nullable: true
 *         contactEmail:
 *           type: string
 *           format: email
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     CreateCompanyDto:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         siret:
 *           type: string
 *           pattern: '^\\d{14}$'
 *         address:
 *           type: string
 *           minLength: 5
 *           maxLength: 200
 *         industryType:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         contactPerson:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         contactEmail:
 *           type: string
 *           format: email
 */

export class CompaniesController {
  private companiesService: CompaniesService;

  constructor() {
    this.companiesService = new CompaniesService();
  }

  /**
   * @swagger
   * /api/companies:
   *   post:
   *     summary: Create a new company
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateCompanyDto'
   *     responses:
   *       201:
   *         description: Company created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 data:
   *                   $ref: '#/components/schemas/Company'
   *       400:
   *         description: Invalid input data
   *       409:
   *         description: Company already exists
   */
  create = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const createCompanyDto: CreateCompanyDto = req.body;
      const company = await this.companiesService.create(createCompanyDto);

      logger.info('Company created via API', {
        companyId: company.id,
        userId: req.user?.userId,
        name: company.name
      });

      res.status(201).json({
        success: true,
        data: company,
        message: 'Company created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/companies:
   *   get:
   *     summary: Get all companies with filtering and pagination
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search in company name, contact person, or email
   *       - in: query
   *         name: industryType
   *         schema:
   *           type: string
   *         description: Filter by industry type
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Number of items per page
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           default: createdAt
   *         description: Field to sort by
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: Sort order
   *     responses:
   *       200:
   *         description: Companies retrieved successfully
   */
  findAll = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const queryDto: CompanyQueryDto = req.query;
      const result = await this.companiesService.findAll(queryDto);

      res.json({
        success: true,
        data: result,
        message: 'Companies retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/companies/{id}:
   *   get:
   *     summary: Get company by ID
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Company ID
   *     responses:
   *       200:
   *         description: Company retrieved successfully
   *       404:
   *         description: Company not found
   */
  findById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const company = await this.companiesService.findById(id);

      res.json({
        success: true,
        data: company,
        message: 'Company retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/companies/{id}:
   *   put:
   *     summary: Update company
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Company ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateCompanyDto'
   *     responses:
   *       200:
   *         description: Company updated successfully
   *       404:
   *         description: Company not found
   *       409:
   *         description: Conflict with existing company
   */
  update = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const updateCompanyDto: UpdateCompanyDto = req.body;
      const company = await this.companiesService.update(id, updateCompanyDto);

      logger.info('Company updated via API', {
        companyId: id,
        userId: req.user?.userId,
        name: company.name
      });

      res.json({
        success: true,
        data: company,
        message: 'Company updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/companies/{id}:
   *   delete:
   *     summary: Delete company
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Company ID
   *     responses:
   *       200:
   *         description: Company deleted successfully
   *       400:
   *         description: Cannot delete company with associated data
   *       404:
   *         description: Company not found
   */
  delete = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      await this.companiesService.delete(id);

      logger.info('Company deleted via API', {
        companyId: id,
        userId: req.user?.userId
      });

      res.json({
        success: true,
        message: 'Company deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/companies/statistics:
   *   get:
   *     summary: Get company statistics
   *     tags: [Companies]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Statistics retrieved successfully
   */
  getStatistics = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const statistics = await this.companiesService.getStatistics();

      res.json({
        success: true,
        data: statistics,
        message: 'Company statistics retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };
}

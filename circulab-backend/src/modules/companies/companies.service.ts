import { Company, Prisma } from '@prisma/client';
import DatabaseService from '../../config/database';
import { AppError } from '../../common/middleware/errorHandler';
import { CreateCompanyDto, UpdateCompanyDto, CompanyQueryDto } from './dto/company.dto';
import logger from '../../config/logger';

export class CompaniesService {
  private db: DatabaseService;

  constructor() {
    this.db = DatabaseService.getInstance();
  }

  async create(createCompanyDto: CreateCompanyDto): Promise<Company> {
    try {
      // Check if company with same name or SIRET already exists
      const existingCompany = await this.db.prisma.company.findFirst({
        where: {
          OR: [
            { name: createCompanyDto.name },
            ...(createCompanyDto.siret ? [{ siret: createCompanyDto.siret }] : [])
          ]
        }
      });

      if (existingCompany) {
        if (existingCompany.name === createCompanyDto.name) {
          throw new AppError('Company with this name already exists', 409);
        }
        if (existingCompany.siret === createCompanyDto.siret) {
          throw new AppError('Company with this SIRET already exists', 409);
        }
      }

      const company = await this.db.prisma.company.create({
        data: createCompanyDto,
        include: {
          users: {
            select: {
              id: true,
              email: true,
              username: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              producedWaste: true,
              processedWaste: true
            }
          }
        }
      });

      logger.info('Company created successfully', {
        companyId: company.id,
        name: company.name
      });

      return company;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error creating company', { error, data: createCompanyDto });
      throw new AppError('Failed to create company', 500);
    }
  }

  async findAll(queryDto: CompanyQueryDto): Promise<{
    companies: Company[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const { search, industryType, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = queryDto;

      const where: Prisma.CompanyWhereInput = {};

      if (search) {
        where.OR = [
          { name: { contains: search } },
          { contactPerson: { contains: search } },
          { contactEmail: { contains: search } }
        ];
      }

      if (industryType) {
        where.industryType = { contains: industryType };
      }

      const [companies, total] = await Promise.all([
        this.db.prisma.company.findMany({
          where,
          include: {
            users: {
              select: {
                id: true,
                email: true,
                username: true,
                createdAt: true
              }
            },
            _count: {
              select: {
                producedWaste: true,
                processedWaste: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip: (page - 1) * limit,
          take: limit
        }),
        this.db.prisma.company.count({ where })
      ]);

      return {
        companies,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error fetching companies', { error, query: queryDto });
      throw new AppError('Failed to fetch companies', 500);
    }
  }

  async findById(id: string): Promise<Company> {
    try {
      const company = await this.db.prisma.company.findUnique({
        where: { id },
        include: {
          users: {
            select: {
              id: true,
              email: true,
              username: true,
              createdAt: true,
              lastLoginAt: true
            }
          },
          producedWaste: {
            select: {
              id: true,
              type: true,
              quantity: true,
              unit: true,
              currentStatus: true,
              createdAt: true
            },
            orderBy: { createdAt: 'desc' },
            take: 10
          },
          processedWaste: {
            select: {
              id: true,
              wasteMaterial: {
                select: {
                  type: true,
                  quantity: true,
                  unit: true
                }
              },
              treatmentMethod: {
                select: {
                  name: true
                }
              },
              dateProcessed: true
            },
            orderBy: { dateProcessed: 'desc' },
            take: 10
          },
          _count: {
            select: {
              users: true,
              producedWaste: true,
              processedWaste: true
            }
          }
        }
      });

      if (!company) {
        throw new AppError('Company not found', 404);
      }

      return company;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error fetching company by ID', { error, companyId: id });
      throw new AppError('Failed to fetch company', 500);
    }
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    try {
      // Check if company exists
      const existingCompany = await this.db.prisma.company.findUnique({
        where: { id }
      });

      if (!existingCompany) {
        throw new AppError('Company not found', 404);
      }

      // Check for conflicts with name or SIRET if they're being updated
      if (updateCompanyDto.name || updateCompanyDto.siret) {
        const conflictingCompany = await this.db.prisma.company.findFirst({
          where: {
            AND: [
              { id: { not: id } },
              {
                OR: [
                  ...(updateCompanyDto.name ? [{ name: updateCompanyDto.name }] : []),
                  ...(updateCompanyDto.siret ? [{ siret: updateCompanyDto.siret }] : [])
                ]
              }
            ]
          }
        });

        if (conflictingCompany) {
          if (conflictingCompany.name === updateCompanyDto.name) {
            throw new AppError('Company with this name already exists', 409);
          }
          if (conflictingCompany.siret === updateCompanyDto.siret) {
            throw new AppError('Company with this SIRET already exists', 409);
          }
        }
      }

      const company = await this.db.prisma.company.update({
        where: { id },
        data: updateCompanyDto,
        include: {
          users: {
            select: {
              id: true,
              email: true,
              username: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              producedWaste: true,
              processedWaste: true
            }
          }
        }
      });

      logger.info('Company updated successfully', {
        companyId: company.id,
        name: company.name
      });

      return company;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error updating company', { error, companyId: id, data: updateCompanyDto });
      throw new AppError('Failed to update company', 500);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      // Check if company exists
      const existingCompany = await this.db.prisma.company.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              users: true,
              producedWaste: true,
              processedWaste: true
            }
          }
        }
      });

      if (!existingCompany) {
        throw new AppError('Company not found', 404);
      }

      // Check if company has associated data
      if (existingCompany._count.users > 0) {
        throw new AppError('Cannot delete company with associated users', 400);
      }

      if (existingCompany._count.producedWaste > 0 || existingCompany._count.processedWaste > 0) {
        throw new AppError('Cannot delete company with associated waste materials or processing history', 400);
      }

      await this.db.prisma.company.delete({
        where: { id }
      });

      logger.info('Company deleted successfully', {
        companyId: id,
        name: existingCompany.name
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error deleting company', { error, companyId: id });
      throw new AppError('Failed to delete company', 500);
    }
  }

  async getStatistics(): Promise<{
    totalCompanies: number;
    companiesByIndustry: Array<{ industryType: string; count: number }>;
    topProducers: Array<{ name: string; wasteCount: number }>;
    topProcessors: Array<{ name: string; processedCount: number }>;
    recentlyJoined: Array<{ name: string; createdAt: Date }>;
  }> {
    try {
      const [
        totalCompanies,
        companiesByIndustry,
        topProducers,
        topProcessors,
        recentlyJoined
      ] = await Promise.all([
        this.db.prisma.company.count(),
        this.db.prisma.company.groupBy({
          by: ['industryType'],
          _count: { id: true },
          where: { industryType: { not: null } }
        }),
        this.db.prisma.company.findMany({
          select: {
            name: true,
            _count: { select: { producedWaste: true } }
          },
          orderBy: { producedWaste: { _count: 'desc' } },
          take: 5
        }),
        this.db.prisma.company.findMany({
          select: {
            name: true,
            _count: { select: { processedWaste: true } }
          },
          orderBy: { processedWaste: { _count: 'desc' } },
          take: 5
        }),
        this.db.prisma.company.findMany({
          select: {
            name: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 5
        })
      ]);

      return {
        totalCompanies,
        companiesByIndustry: companiesByIndustry.map(item => ({
          industryType: item.industryType || 'Unknown',
          count: item._count.id
        })),
        topProducers: topProducers.map(company => ({
          name: company.name,
          wasteCount: company._count.producedWaste
        })),
        topProcessors: topProcessors.map(company => ({
          name: company.name,
          processedCount: company._count.processedWaste
        })),
        recentlyJoined: recentlyJoined.map(company => ({
          name: company.name,
          createdAt: company.createdAt
        }))
      };
    } catch (error) {
      logger.error('Error fetching company statistics', { error });
      throw new AppError('Failed to fetch company statistics', 500);
    }
  }
}

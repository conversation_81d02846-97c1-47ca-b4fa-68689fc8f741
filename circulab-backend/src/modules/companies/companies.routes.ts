import { Router } from 'express';
import { CompaniesController } from './companies.controller';
import { authenticateToken, requirePermission } from '../../common/middleware/auth';
import { validationMiddleware, validateParams, validateQuery } from '../../common/middleware/validation';
import { CreateCompanyDto, UpdateCompanyDto, CompanyParamsDto, CompanyQueryDto } from './dto/company.dto';
import { cacheMiddleware, invalidateCache } from '../../common/middleware/cache';

const router = Router();
const companiesController = new CompaniesController();

// Apply authentication to all routes
router.use(authenticateToken);

// Statistics route (before parameterized routes) - with caching
router.get(
  '/statistics',
  requirePermission('view_analytics'),
  cacheMiddleware({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `company-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
  }),
  companiesController.getStatistics
);

// CRUD routes
router.post(
  '/',
  requirePermission('manage_companies'),
  validationMiddleware(CreateCompanyDto),
  invalidateCache('company-statistics'), // Invalidate statistics cache
  companiesController.create
);

router.get(
  '/',
  requirePermission('read_companies'),
  validateQuery(CompanyQueryDto),
  companiesController.findAll
);

router.get(
  '/:id',
  requirePermission('read_companies'),
  validateParams(CompanyParamsDto),
  companiesController.findById
);

router.put(
  '/:id',
  requirePermission('manage_companies'),
  validateParams(CompanyParamsDto),
  validationMiddleware(UpdateCompanyDto),
  invalidateCache('company-statistics'), // Invalidate statistics cache
  companiesController.update
);

router.delete(
  '/:id',
  requirePermission('manage_companies'),
  validateParams(CompanyParamsDto),
  invalidateCache('company-statistics'), // Invalidate statistics cache
  companiesController.delete
);

export default router;

import { IsString, IsOptional, IsEmail, IsUUID, Length, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateCompanyDto {
  @IsString()
  @Length(2, 100)
  @Transform(({ value }) => value?.trim())
  name: string;

  @IsOptional()
  @IsString()
  @Length(14, 14)
  @Matches(/^\d{14}$/, { message: 'SIRET must be exactly 14 digits' })
  siret?: string;

  @IsOptional()
  @IsString()
  @Length(5, 200)
  address?: string;

  @IsOptional()
  @IsString()
  @Length(2, 50)
  industryType?: string;

  @IsOptional()
  @IsString()
  @Length(2, 100)
  contactPerson?: string;

  @IsOptional()
  @IsEmail()
  contactEmail?: string;
}

export class UpdateCompanyDto {
  @IsOptional()
  @IsString()
  @Length(2, 100)
  @Transform(({ value }) => value?.trim())
  name?: string;

  @IsOptional()
  @IsString()
  @Length(14, 14)
  @Matches(/^\d{14}$/, { message: 'SIRET must be exactly 14 digits' })
  siret?: string;

  @IsOptional()
  @IsString()
  @Length(5, 200)
  address?: string;

  @IsOptional()
  @IsString()
  @Length(2, 50)
  industryType?: string;

  @IsOptional()
  @IsString()
  @Length(2, 100)
  contactPerson?: string;

  @IsOptional()
  @IsEmail()
  contactEmail?: string;
}

export class CompanyParamsDto {
  @IsUUID()
  id: string;
}

export class CompanyQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  industryType?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

import { Router } from 'express';
import { UsersController } from './users.controller';
import { authenticateToken, requirePermission } from '../../common/middleware/auth';
import { validationMiddleware, validateParams, validateQuery } from '../../common/middleware/validation';
import { CreateUserDto, UpdateUserDto, UpdateUserPasswordDto, UserParamsDto, UserQueryDto } from './dto/user.dto';
import { cacheMiddleware, invalidateCache } from '../../common/middleware/cache';

const router = Router();
const usersController = new UsersController();

// Apply authentication to all routes
router.use(authenticateToken);

// Statistics route (before parameterized routes) - with caching
router.get(
  '/statistics',
  requirePermission('view_analytics'),
  cacheMiddleware({
    ttl: 300, // Cache for 5 minutes
    keyGenerator: (req) => `user-statistics:${req.user?.companyId || 'all'}:${req.user?.roles?.join(',') || 'none'}`,
  }),
  usersController.getStatistics
);

// CRUD routes
router.post(
  '/',
  requirePermission('manage_users'),
  validationMiddleware(CreateUserDto),
  invalidateCache('user-statistics'), // Invalidate statistics cache
  usersController.create
);

router.get(
  '/',
  requirePermission('manage_users'),
  validateQuery(UserQueryDto),
  usersController.findAll
);

router.get(
  '/:id',
  requirePermission('manage_users'),
  validateParams(UserParamsDto),
  usersController.findById
);

router.put(
  '/:id',
  requirePermission('manage_users'),
  validateParams(UserParamsDto),
  validationMiddleware(UpdateUserDto),
  invalidateCache('user-statistics'), // Invalidate statistics cache
  usersController.update
);

router.put(
  '/:id/password',
  requirePermission('manage_users'),
  validateParams(UserParamsDto),
  validationMiddleware(UpdateUserPasswordDto),
  usersController.updatePassword
);

router.delete(
  '/:id',
  requirePermission('manage_users'),
  validateParams(UserParamsDto),
  invalidateCache('user-statistics'), // Invalidate statistics cache
  usersController.delete
);

export default router;

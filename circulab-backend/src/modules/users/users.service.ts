import { User, Prisma } from '@prisma/client';
import bcrypt from 'bcryptjs';
import DatabaseService from '../../config/database';
import { AppError } from '../../common/middleware/errorHandler';
import { CreateUserDto, UpdateUserDto, UpdateUserPasswordDto, UserQueryDto } from './dto/user.dto';
import logger from '../../config/logger';

export class UsersService {
  private db: DatabaseService;

  constructor() {
    this.db = DatabaseService.getInstance();
  }

  async create(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    try {
      // Check if user with same email already exists
      const existingUser = await this.db.prisma.user.findUnique({
        where: { email: createUserDto.email }
      });

      if (existingUser) {
        throw new AppError('User with this email already exists', 409);
      }

      // Check if username is provided and unique
      if (createUserDto.username) {
        const existingUsername = await this.db.prisma.user.findUnique({
          where: { username: createUserDto.username }
        });

        if (existingUsername) {
          throw new AppError('Username already taken', 409);
        }
      }

      // Validate company exists if provided
      if (createUserDto.companyId) {
        const company = await this.db.prisma.company.findUnique({
          where: { id: createUserDto.companyId }
        });

        if (!company) {
          throw new AppError('Company not found', 404);
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

      // Create user
      const { roleNames, ...userData } = createUserDto;
      const user = await this.db.prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword,
          createdBy: 'admin' // This should be set to the current user's ID in a real scenario
        },
        include: {
          company: true,
          roles: {
            include: {
              role: true
            }
          }
        }
      });

      // Assign roles if provided
      if (roleNames && roleNames.length > 0) {
        await this.assignRoles(user.id, roleNames);
      }

      // Fetch user with updated roles
      const userWithRoles = await this.findById(user.id);

      logger.info('User created successfully', {
        userId: user.id,
        email: user.email
      });

      return userWithRoles;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error creating user', { error, data: createUserDto });
      throw new AppError('Failed to create user', 500);
    }
  }

  async findAll(queryDto: UserQueryDto): Promise<{
    users: Omit<User, 'password'>[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const { search, companyId, role, isEmailVerified, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = queryDto;

      const where: Prisma.UserWhereInput = {};

      if (search) {
        where.OR = [
          { email: { contains: search } },
          { username: { contains: search } }
        ];
      }

      if (companyId) {
        where.companyId = companyId;
      }

      if (role) {
        where.roles = {
          some: {
            role: {
              name: role
            }
          }
        };
      }

      if (isEmailVerified !== undefined) {
        where.isEmailVerified = isEmailVerified;
      }

      const [users, total] = await Promise.all([
        this.db.prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            username: true,
            isEmailVerified: true,
            createdAt: true,
            updatedAt: true,
            lastLoginAt: true,
            createdBy: true,
            companyId: true,
            company: {
              select: {
                id: true,
                name: true,
                industryType: true
              }
            },
            roles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    description: true
                  }
                }
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip: (page - 1) * limit,
          take: limit
        }),
        this.db.prisma.user.count({ where })
      ]);

      return {
        users,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error fetching users', { error, query: queryDto });
      throw new AppError('Failed to fetch users', 500);
    }
  }

  async findById(id: string): Promise<Omit<User, 'password'>> {
    try {
      const user = await this.db.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          username: true,
          isEmailVerified: true,
          createdAt: true,
          updatedAt: true,
          lastLoginAt: true,
          createdBy: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              siret: true,
              address: true,
              industryType: true,
              contactPerson: true,
              contactEmail: true
            }
          },
          roles: {
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          },
          wasteMaterials: {
            select: {
              id: true,
              type: true,
              quantity: true,
              unit: true,
              currentStatus: true,
              createdAt: true
            },
            orderBy: { createdAt: 'desc' },
            take: 5
          },
          processingHistory: {
            select: {
              id: true,
              wasteMaterial: {
                select: {
                  type: true,
                  quantity: true,
                  unit: true
                }
              },
              treatmentMethod: {
                select: {
                  name: true
                }
              },
              dateProcessed: true
            },
            orderBy: { dateProcessed: 'desc' },
            take: 5
          }
        }
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error fetching user by ID', { error, userId: id });
      throw new AppError('Failed to fetch user', 500);
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<Omit<User, 'password'>> {
    try {
      // Check if user exists
      const existingUser = await this.db.prisma.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        throw new AppError('User not found', 404);
      }

      // Check for email conflicts if email is being updated
      if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
        const emailConflict = await this.db.prisma.user.findUnique({
          where: { email: updateUserDto.email }
        });

        if (emailConflict) {
          throw new AppError('Email already in use', 409);
        }
      }

      // Check for username conflicts if username is being updated
      if (updateUserDto.username && updateUserDto.username !== existingUser.username) {
        const usernameConflict = await this.db.prisma.user.findUnique({
          where: { username: updateUserDto.username }
        });

        if (usernameConflict) {
          throw new AppError('Username already taken', 409);
        }
      }

      // Validate company exists if being updated
      if (updateUserDto.companyId) {
        const company = await this.db.prisma.company.findUnique({
          where: { id: updateUserDto.companyId }
        });

        if (!company) {
          throw new AppError('Company not found', 404);
        }
      }

      // Update user
      const { roleNames, ...userData } = updateUserDto;
      await this.db.prisma.user.update({
        where: { id },
        data: userData
      });

      // Update roles if provided
      if (roleNames !== undefined) {
        await this.assignRoles(id, roleNames);
      }

      // Fetch updated user
      const updatedUser = await this.findById(id);

      logger.info('User updated successfully', {
        userId: id,
        email: updatedUser.email
      });

      return updatedUser;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error updating user', { error, userId: id, data: updateUserDto });
      throw new AppError('Failed to update user', 500);
    }
  }

  async updatePassword(id: string, updatePasswordDto: UpdateUserPasswordDto): Promise<void> {
    try {
      // Get user with password
      const user = await this.db.prisma.user.findUnique({
        where: { id }
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(updatePasswordDto.currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new AppError('Current password is incorrect', 400);
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(updatePasswordDto.newPassword, 12);

      // Update password
      await this.db.prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword }
      });

      logger.info('User password updated successfully', { userId: id });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error updating user password', { error, userId: id });
      throw new AppError('Failed to update password', 500);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      // Check if user exists
      const existingUser = await this.db.prisma.user.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              wasteMaterials: true,
              processingHistory: true
            }
          }
        }
      });

      if (!existingUser) {
        throw new AppError('User not found', 404);
      }

      // Check if user has associated data
      if (existingUser._count.wasteMaterials > 0 || existingUser._count.processingHistory > 0) {
        throw new AppError('Cannot delete user with associated waste materials or processing history', 400);
      }

      // Delete user (this will cascade delete user roles)
      await this.db.prisma.user.delete({
        where: { id }
      });

      logger.info('User deleted successfully', {
        userId: id,
        email: existingUser.email
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error deleting user', { error, userId: id });
      throw new AppError('Failed to delete user', 500);
    }
  }

  private async assignRoles(userId: string, roleNames: string[]): Promise<void> {
    // Remove existing roles
    await this.db.prisma.userRole.deleteMany({
      where: { userId }
    });

    // Add new roles
    for (const roleName of roleNames) {
      const role = await this.db.prisma.role.findUnique({
        where: { name: roleName }
      });

      if (role) {
        await this.db.prisma.userRole.create({
          data: {
            userId,
            roleId: role.id
          }
        });
      }
    }
  }

  async getStatistics(): Promise<{
    totalUsers: number;
    usersByRole: Array<{ role: string; count: number }>;
    usersByCompany: Array<{ company: string; count: number }>;
    recentlyJoined: Array<{ email: string; username: string | null; createdAt: Date }>;
    emailVerificationStats: { verified: number; unverified: number };
  }> {
    try {
      const [
        totalUsers,
        usersByRole,
        usersByCompany,
        recentlyJoined,
        emailVerificationStats
      ] = await Promise.all([
        this.db.prisma.user.count(),
        this.db.prisma.userRole.groupBy({
          by: ['roleId'],
          _count: { userId: true }
        }),
        this.db.prisma.user.groupBy({
          by: ['companyId'],
          _count: { id: true },
          where: { companyId: { not: null } }
        }),
        this.db.prisma.user.findMany({
          select: {
            email: true,
            username: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 5
        }),
        this.db.prisma.user.groupBy({
          by: ['isEmailVerified'],
          _count: { id: true }
        })
      ]);

      // Get role names for usersByRole
      const roleIds = usersByRole.map(item => item.roleId);
      const roles = await this.db.prisma.role.findMany({
        where: { id: { in: roleIds } },
        select: { id: true, name: true }
      });

      const roleMap = new Map(roles.map(r => [r.id, r.name]));

      // Get company names for usersByCompany
      const companyIds = usersByCompany.map(item => item.companyId).filter(Boolean) as string[];
      const companies = await this.db.prisma.company.findMany({
        where: { id: { in: companyIds } },
        select: { id: true, name: true }
      });

      const companyMap = new Map(companies.map(c => [c.id, c.name]));

      return {
        totalUsers,
        usersByRole: usersByRole.map(item => ({
          role: roleMap.get(item.roleId) || 'Unknown',
          count: item._count.userId
        })),
        usersByCompany: usersByCompany.map(item => ({
          company: companyMap.get(item.companyId!) || 'Unknown',
          count: item._count.id
        })),
        recentlyJoined,
        emailVerificationStats: {
          verified: emailVerificationStats.find(item => item.isEmailVerified)?._count.id || 0,
          unverified: emailVerificationStats.find(item => !item.isEmailVerified)?._count.id || 0
        }
      };
    } catch (error) {
      logger.error('Error fetching user statistics', { error });
      throw new AppError('Failed to fetch user statistics', 500);
    }
  }
}

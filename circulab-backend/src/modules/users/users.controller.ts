import { Request, Response, NextFunction } from 'express';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UpdateUserPasswordDto, UserQueryDto } from './dto/user.dto';
import { AuthenticatedRequest } from '../../common/middleware/auth';
import logger from '../../config/logger';

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         username:
 *           type: string
 *           nullable: true
 *         isEmailVerified:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           nullable: true
 *     CreateUserDto:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 8
 *           maxLength: 128
 *         username:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         companyId:
 *           type: string
 *           format: uuid
 *         roleNames:
 *           type: array
 *           items:
 *             type: string
 *         isEmailVerified:
 *           type: boolean
 */

export class UsersController {
  private usersService: UsersService;

  constructor() {
    this.usersService = new UsersService();
  }

  /**
   * @swagger
   * /api/users:
   *   post:
   *     summary: Create a new user
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateUserDto'
   *     responses:
   *       201:
   *         description: User created successfully
   *       400:
   *         description: Invalid input data
   *       409:
   *         description: User already exists
   */
  create = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const createUserDto: CreateUserDto = req.body;
      const user = await this.usersService.create(createUserDto);

      logger.info('User created via API', {
        userId: user.id,
        createdBy: req.user?.userId,
        email: user.email
      });

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users:
   *   get:
   *     summary: Get all users with filtering and pagination
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search in email or username
   *       - in: query
   *         name: companyId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by company ID
   *       - in: query
   *         name: role
   *         schema:
   *           type: string
   *         description: Filter by role name
   *       - in: query
   *         name: isEmailVerified
   *         schema:
   *           type: boolean
   *         description: Filter by email verification status
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: Users retrieved successfully
   */
  findAll = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const queryDto: UserQueryDto = req.query;
      const result = await this.usersService.findAll(queryDto);

      res.json({
        success: true,
        data: result,
        message: 'Users retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users/{id}:
   *   get:
   *     summary: Get user by ID
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: User ID
   *     responses:
   *       200:
   *         description: User retrieved successfully
   *       404:
   *         description: User not found
   */
  findById = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const user = await this.usersService.findById(id);

      res.json({
        success: true,
        data: user,
        message: 'User retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users/{id}:
   *   put:
   *     summary: Update user
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: User ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateUserDto'
   *     responses:
   *       200:
   *         description: User updated successfully
   *       404:
   *         description: User not found
   *       409:
   *         description: Conflict with existing user
   */
  update = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const updateUserDto: UpdateUserDto = req.body;
      const user = await this.usersService.update(id, updateUserDto);

      logger.info('User updated via API', {
        userId: id,
        updatedBy: req.user?.userId,
        email: user.email
      });

      res.json({
        success: true,
        data: user,
        message: 'User updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users/{id}/password:
   *   put:
   *     summary: Update user password
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: User ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - currentPassword
   *               - newPassword
   *             properties:
   *               currentPassword:
   *                 type: string
   *               newPassword:
   *                 type: string
   *                 minLength: 8
   *     responses:
   *       200:
   *         description: Password updated successfully
   *       400:
   *         description: Invalid current password
   *       404:
   *         description: User not found
   */
  updatePassword = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const updatePasswordDto: UpdateUserPasswordDto = req.body;
      await this.usersService.updatePassword(id, updatePasswordDto);

      logger.info('User password updated via API', {
        userId: id,
        updatedBy: req.user?.userId
      });

      res.json({
        success: true,
        message: 'Password updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users/{id}:
   *   delete:
   *     summary: Delete user
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: User ID
   *     responses:
   *       200:
   *         description: User deleted successfully
   *       400:
   *         description: Cannot delete user with associated data
   *       404:
   *         description: User not found
   */
  delete = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      await this.usersService.delete(id);

      logger.info('User deleted via API', {
        userId: id,
        deletedBy: req.user?.userId
      });

      res.json({
        success: true,
        message: 'User deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/users/statistics:
   *   get:
   *     summary: Get user statistics
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Statistics retrieved successfully
   */
  getStatistics = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const statistics = await this.usersService.getStatistics();

      res.json({
        success: true,
        data: statistics,
        message: 'User statistics retrieved successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  };
}

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create permissions
  const permissions = [
    { name: 'manage_users', description: 'Create, read, update, delete users' },
    { name: 'manage_companies', description: 'Create, read, update, delete companies' },
    { name: 'read_companies', description: 'View companies' },
    { name: 'create_waste', description: 'Create new waste materials' },
    { name: 'read_waste', description: 'View waste materials' },
    { name: 'update_waste', description: 'Update waste materials' },
    { name: 'delete_waste', description: 'Delete waste materials' },
    { name: 'read_all_waste', description: 'View all waste materials across companies' },
    { name: 'process_waste', description: 'Record waste processing activities' },
    { name: 'manage_treatment_methods', description: 'Create, read, update, delete treatment methods' },
    { name: 'view_analytics', description: 'Access analytics dashboard' },
    { name: 'manage_notifications', description: 'Create and manage notifications' },
    { name: 'bulk_import', description: 'Import waste materials via CSV' },
  ];

  console.log('Creating permissions...');
  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission,
    });
  }

  // Create roles
  const roles = [
    { name: 'admin', description: 'System administrator with full access' },
    { name: 'company_admin', description: 'Company administrator' },
    { name: 'waste_producer', description: 'User who can create and manage waste materials' },
    { name: 'waste_processor', description: 'User who can process waste materials' },
    { name: 'viewer', description: 'Read-only access to waste materials' },
  ];

  console.log('Creating roles...');
  for (const role of roles) {
    await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role,
    });
  }

  // Assign permissions to roles
  const rolePermissions = [
    // Admin - all permissions
    { roleName: 'admin', permissions: permissions.map(p => p.name) },
    // Company Admin - manage company users and view all company data
    {
      roleName: 'company_admin',
      permissions: ['manage_users', 'read_companies', 'create_waste', 'read_waste', 'update_waste', 'delete_waste', 'process_waste', 'view_analytics', 'bulk_import']
    },
    // Waste Producer - create and manage waste
    {
      roleName: 'waste_producer',
      permissions: ['read_companies', 'create_waste', 'read_waste', 'update_waste', 'view_analytics']
    },
    // Waste Processor - process waste and view processing data
    {
      roleName: 'waste_processor',
      permissions: ['read_companies', 'read_waste', 'read_all_waste', 'process_waste', 'view_analytics']
    },
    // Viewer - read-only access
    {
      roleName: 'viewer',
      permissions: ['read_companies', 'read_waste', 'view_analytics']
    },
  ];

  console.log('Assigning permissions to roles...');
  for (const rolePermission of rolePermissions) {
    const role = await prisma.role.findUnique({ where: { name: rolePermission.roleName } });
    if (role) {
      for (const permissionName of rolePermission.permissions) {
        const permission = await prisma.permission.findUnique({ where: { name: permissionName } });
        if (permission) {
          await prisma.rolePermission.upsert({
            where: {
              roleId_permissionId: {
                roleId: role.id,
                permissionId: permission.id,
              },
            },
            update: {},
            create: {
              roleId: role.id,
              permissionId: permission.id,
            },
          });
        }
      }
    }
  }

  // Create default company
  console.log('Creating default company...');
  const defaultCompany = await prisma.company.upsert({
    where: { name: 'CircuLab Demo Company' },
    update: {},
    create: {
      name: 'CircuLab Demo Company',
      siret: '12345678901234',
      address: '123 Innovation Street, Tech City, 75001 Paris',
      industryType: 'Technology & Sustainability',
      contactPerson: 'Demo Manager',
      contactEmail: '<EMAIL>',
    },
  });

  // Create root admin user
  console.log('Creating root admin user...');
  const hashedPassword = await bcrypt.hash('Password123', 12);
  const adminRole = await prisma.role.findUnique({ where: { name: 'admin' } });

  const rootUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      username: 'root',
      isEmailVerified: true,
      companyId: defaultCompany.id,
      createdBy: 'system',
    },
  });

  if (adminRole) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: rootUser.id,
          roleId: adminRole.id,
        },
      },
      update: {},
      create: {
        userId: rootUser.id,
        roleId: adminRole.id,
      },
    });
  }

  // Create sample treatment methods
  console.log('Creating sample treatment methods...');
  const treatmentMethods = [
    {
      name: 'Mechanical Recycling',
      description: 'Physical processing to break down plastic materials into reusable forms',
      technologyType: 'Low',
      typicalInputs: ['Plastic PET', 'Plastic HDPE', 'Plastic PP'],
      typicalOutputs: ['PET Flakes', 'HDPE Pellets', 'PP Granules'],
      environmentalBenefits: 'Reduces landfill waste and energy consumption compared to virgin plastic production',
      costEstimate: 'Low to Medium',
    },
    {
      name: 'Chemical Recycling',
      description: 'Chemical breakdown of plastic polymers into base chemicals for new plastic production',
      technologyType: 'High',
      typicalInputs: ['Mixed Plastics', 'Contaminated Plastics', 'Multi-layer Packaging'],
      typicalOutputs: ['Monomers', 'Oligomers', 'Chemical Feedstock'],
      environmentalBenefits: 'Enables recycling of complex plastic waste streams',
      costEstimate: 'High',
    },
    {
      name: 'Composting',
      description: 'Biological decomposition of organic waste into nutrient-rich compost',
      technologyType: 'Low',
      typicalInputs: ['Organic Food Waste', 'Garden Waste', 'Paper Waste'],
      typicalOutputs: ['Compost Grade A', 'Soil Amendment', 'Organic Fertilizer'],
      environmentalBenefits: 'Reduces methane emissions and creates valuable soil amendment',
      costEstimate: 'Low',
    },
    {
      name: 'Anaerobic Digestion',
      description: 'Biological process that breaks down organic matter in oxygen-free environment',
      technologyType: 'Medium',
      typicalInputs: ['Organic Food Waste', 'Agricultural Waste', 'Sewage Sludge'],
      typicalOutputs: ['Biogas', 'Digestate', 'Renewable Energy'],
      environmentalBenefits: 'Produces renewable energy and reduces greenhouse gas emissions',
      costEstimate: 'Medium to High',
    },
    {
      name: 'Metal Recovery',
      description: 'Extraction and purification of metals from waste streams',
      technologyType: 'Medium',
      typicalInputs: ['Metal Aluminium', 'Metal Steel', 'Electronic Waste'],
      typicalOutputs: ['Pure Aluminium', 'Steel Ingots', 'Precious Metals'],
      environmentalBenefits: 'Conserves natural resources and reduces mining impact',
      costEstimate: 'Medium',
    },
  ];

  for (const method of treatmentMethods) {
    await prisma.treatmentMethod.upsert({
      where: { name: method.name },
      update: {},
      create: method,
    });
  }

  console.log('✅ Database seeding completed successfully!');
  console.log('🔑 Default login credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: Password123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
